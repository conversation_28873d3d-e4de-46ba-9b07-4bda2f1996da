/**
 * Asset Build Script for Levo Shopify Theme
 *
 * This script minifies individual CSS and JS files to improve performance
 * without merging them, which preserves all functionality.
 *
 * Usage: node build-assets.js
 */

const fs = require('fs');
const path = require('path');
const CleanCSS = require('clean-css');
const UglifyJS = require('uglify-js');

// Configuration
const config = {
  assetsDir: path.join(__dirname, 'assets'),
  outputDir: path.join(__dirname, 'assets'),

  // CSS files to minify
  cssFiles: [
    'theme-variables.css',
    'base.css',
    'button-styles.css',
    'section-headings.css',
    'collection-filters.css',
    'collection-page.css',
    'category-grid.css',
    'product-card-styled.css',
    'footer-fix.css',
    'image-dimensions.css',
    'theme.css',
    'critical.css',
    'keen-slider.css'
  ],

  // JS files to minify
  jsFiles: [
    'bfcache-fix.js',
    'accessibility-utils.js',
    'lazy-loader.js',
    'css-loader.js',
    'css-optimizer.js',
    'collection-page.js',
    'cart-drawer.js',
    'theme.js',
    'keen-slider.js',
    'carousel-init.js'
  ]
};

// Function to read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return '';
  }
}

// Function to write file content
function writeFile(filePath, content) {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Successfully wrote ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error writing file ${filePath}:`, error);
    return false;
  }
}

// Function to minify individual CSS files
function processCSS() {
  console.log('Processing CSS files...');

  // Get all CSS files in the assets directory
  const allCssFiles = fs.readdirSync(config.assetsDir)
    .filter(file => file.endsWith('.css') && !file.endsWith('.min.css'));

  // Add any CSS files that are in the config but not found in the directory scan
  config.cssFiles.forEach(file => {
    if (!allCssFiles.includes(file) && fs.existsSync(path.join(config.assetsDir, file))) {
      allCssFiles.push(file);
    }
  });

  // Process each CSS file individually
  let totalOriginalSize = 0;
  let totalMinifiedSize = 0;

  allCssFiles.forEach(file => {
    const filePath = path.join(config.assetsDir, file);
    const fileContent = readFile(filePath);

    if (!fileContent) {
      console.log(`Skipping empty file: ${file}`);
      return;
    }

    console.log(`Minifying ${file}`);

    // Minify the CSS
    const minifiedCSS = new CleanCSS({
      level: {
        1: {
          all: true,
          optimizeFont: true,
          optimizeFontWeight: true
        },
        2: {
          all: true,
          mergeMedia: false, // Don't merge media queries to preserve mobile styles
          restructureRules: false, // Don't restructure rules to preserve specificity
          mergeSemantically: false // Don't merge semantically to preserve styles
        }
      },
      compatibility: 'ie11',
      format: 'keep-breaks'
    }).minify(fileContent);

    // Write the minified CSS to the output file
    const outputFileName = file.replace('.css', '.min.css');
    const outputPath = path.join(config.outputDir, outputFileName);
    writeFile(outputPath, minifiedCSS.styles);

    totalOriginalSize += minifiedCSS.stats.originalSize;
    totalMinifiedSize += minifiedCSS.stats.minifiedSize;

    console.log(`  Original: ${(minifiedCSS.stats.originalSize / 1024).toFixed(2)}KB, Minified: ${(minifiedCSS.stats.minifiedSize / 1024).toFixed(2)}KB, Reduction: ${(100 - (minifiedCSS.stats.minifiedSize / minifiedCSS.stats.originalSize * 100)).toFixed(2)}%`);
  });

  console.log(`CSS processing complete. Total Original: ${(totalOriginalSize / 1024).toFixed(2)}KB, Total Minified: ${(totalMinifiedSize / 1024).toFixed(2)}KB, Overall Reduction: ${(100 - (totalMinifiedSize / totalOriginalSize * 100)).toFixed(2)}%`);

  return {
    files: allCssFiles,
    stats: {
      originalSize: totalOriginalSize,
      minifiedSize: totalMinifiedSize
    }
  };
}

// Function to minify individual JS files
function processJS() {
  console.log('Processing JS files...');

  // Get all JS files in the assets directory
  const allJsFiles = fs.readdirSync(config.assetsDir)
    .filter(file => file.endsWith('.js') && !file.endsWith('.min.js'));

  // Add any JS files that are in the config but not found in the directory scan
  config.jsFiles.forEach(file => {
    if (!allJsFiles.includes(file) && fs.existsSync(path.join(config.assetsDir, file))) {
      allJsFiles.push(file);
    }
  });

  // Process each JS file individually
  let totalOriginalSize = 0;
  let totalMinifiedSize = 0;

  allJsFiles.forEach(file => {
    const filePath = path.join(config.assetsDir, file);
    const fileContent = readFile(filePath);

    if (!fileContent) {
      console.log(`Skipping empty file: ${file}`);
      return;
    }

    console.log(`Minifying ${file}`);

    // Minify the JS
    const minifiedJS = UglifyJS.minify(fileContent, {
      compress: {
        drop_console: false,
        drop_debugger: true
      },
      mangle: {
        keep_fnames: true // Keep function names to prevent breaking references
      },
      output: {
        beautify: false,
        comments: 'some'
      }
    });

    if (minifiedJS.error) {
      console.error(`  Error minifying ${file}:`, minifiedJS.error);
      return;
    }

    // Write the minified JS to the output file
    const outputFileName = file.replace('.js', '.min.js');
    const outputPath = path.join(config.outputDir, outputFileName);
    writeFile(outputPath, minifiedJS.code);

    const originalSize = fileContent.length;
    const minifiedSize = minifiedJS.code.length;

    totalOriginalSize += originalSize;
    totalMinifiedSize += minifiedSize;

    console.log(`  Original: ${(originalSize / 1024).toFixed(2)}KB, Minified: ${(minifiedSize / 1024).toFixed(2)}KB, Reduction: ${(100 - (minifiedSize / originalSize * 100)).toFixed(2)}%`);
  });

  console.log(`JS processing complete. Total Original: ${(totalOriginalSize / 1024).toFixed(2)}KB, Total Minified: ${(totalMinifiedSize / 1024).toFixed(2)}KB, Overall Reduction: ${(100 - (totalMinifiedSize / totalOriginalSize * 100)).toFixed(2)}%`);

  return {
    files: allJsFiles,
    stats: {
      originalSize: totalOriginalSize,
      minifiedSize: totalMinifiedSize
    }
  };
}

// Main function to run the build process
function buildAssets() {
  console.log('Starting asset build process...');

  const cssResult = processCSS();
  const jsResult = processJS();

  console.log('\nBuild process complete!');
  console.log(`CSS: ${cssResult.files.length} files minified`);
  console.log(`JS: ${jsResult.files.length} files minified`);

  // Create a helper script to update theme.liquid to use minified files
  console.log('\nTo use minified files, update your theme.liquid file to reference .min.css and .min.js files.');
  console.log('Example: {{ "theme.min.css" | asset_url | stylesheet_tag }} instead of {{ "theme.css" | asset_url | stylesheet_tag }}');
}

// Run the build process
buildAssets();
