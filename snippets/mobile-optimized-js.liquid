{% comment %}
  Mobile-Optimized JavaScript Loading
  
  This snippet implements strategies to minimize and defer JavaScript on mobile devices:
  1. Detects mobile devices using CSS media queries
  2. Loads only critical JavaScript immediately on mobile
  3. Defers non-critical JavaScript until after page load
  4. Implements progressive enhancement for mobile users
{% endcomment %}

<script>
  // Mobile detection using matchMedia for better performance than UA sniffing
  const isMobile = window.matchMedia('(max-width: 749px)').matches;
  
  // Store this information for other scripts to use
  window.themeConfig = window.themeConfig || {};
  window.themeConfig.isMobile = isMobile;
  
  // Function to load scripts based on priority and device
  function loadThemeScript(url, priority, mobileOnly = false, desktopOnly = false) {
    // Skip desktop-only scripts on mobile
    if (isMobile && desktopOnly) return;
    
    // Skip mobile-only scripts on desktop
    if (!isMobile && mobileOnly) return;
    
    const script = document.createElement('script');
    script.src = url;
    
    if (priority === 'critical') {
      // Critical scripts load with higher priority
      script.setAttribute('fetchpriority', 'high');
      if (!isMobile) {
        // On desktop, we can afford to load critical scripts immediately
        document.head.appendChild(script);
      } else {
        // On mobile, even critical scripts are deferred slightly
        script.defer = true;
        document.head.appendChild(script);
      }
    } else if (priority === 'high') {
      // High priority but not critical - defer on all devices
      script.defer = true;
      document.head.appendChild(script);
    } else {
      // Low priority scripts
      if (isMobile) {
        // On mobile, load after page is interactive
        window.addEventListener('load', function() {
          // Further delay low-priority scripts on mobile
          setTimeout(function() {
            script.async = true;
            document.head.appendChild(script);
          }, 1000); // 1 second delay after load
        });
      } else {
        // On desktop, just defer
        script.defer = true;
        document.head.appendChild(script);
      }
    }
    
    return script;
  }
  
  // Function to load scripts only when they're needed
  function loadScriptWhenNeeded(selector, scriptUrl, priority = 'low') {
    if (!document.querySelector(selector)) return;
    
    if (isMobile) {
      // On mobile, use Intersection Observer for better performance
      if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver(function(entries) {
          if (entries.some(entry => entry.isIntersecting)) {
            loadThemeScript(scriptUrl, priority);
            observer.disconnect();
          }
        }, {rootMargin: '200px'}); // Load when within 200px of viewport
        
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => observer.observe(element));
      } else {
        // Fallback for browsers without IntersectionObserver
        loadThemeScript(scriptUrl, priority);
      }
    } else {
      // On desktop, just load the script
      loadThemeScript(scriptUrl, priority);
    }
  }
  
  // Function to load polyfills only when needed
  function loadPolyfills() {
    const polyfills = [];
    
    // IntersectionObserver polyfill
    if (!('IntersectionObserver' in window)) {
      polyfills.push('{{ "intersection-observer-polyfill.min.js" | asset_url }}');
    }
    
    // Only load polyfills if needed
    if (polyfills.length > 0) {
      const polyfillScript = document.createElement('script');
      polyfillScript.src = polyfills.join(',');
      document.head.appendChild(polyfillScript);
    }
  }
  
  // Load core theme scripts with appropriate priorities
  document.addEventListener('DOMContentLoaded', function() {
    // Critical scripts needed for basic functionality
    loadThemeScript('{{ "theme.min.js" | asset_url }}', 'critical');
    
    // BFCache fix is important for all devices
    loadThemeScript('{{ "bfcache-fix.min.js" | asset_url }}', 'high');
    
    // Lazy loading is important but can be deferred slightly
    loadThemeScript('{{ "lazy-loader.min.js" | asset_url }}', 'high');
    
    // Accessibility is important but can be loaded after critical scripts
    loadThemeScript('{{ "accessibility-utils.min.js" | asset_url }}', 'high');
    
    // Load cart drawer only when needed
    if (document.querySelector('.cart-icon') || document.querySelector('[data-ajax-cart-form]')) {
      // Cart functionality is important but can be deferred
      const priority = isMobile ? 'low' : 'high';
      loadThemeScript('{{ "cart-drawer.min.js" | asset_url }}', priority);
    }
    
    // Load collection page script only on collection pages
    {% if template contains 'collection' %}
      loadThemeScript('{{ "collection-page.min.js" | asset_url }}', isMobile ? 'low' : 'high');
    {% endif %}
    
    // Load keen-slider only when a slider is present and about to be visible
    loadScriptWhenNeeded('.keen-slider', '{{ "keen-slider.min.js" | asset_url }}', 'high');
    
    // Load carousel initialization after keen-slider
    loadScriptWhenNeeded('.keen-slider', '{{ "carousel-init.min.js" | asset_url }}', 'high');
    
    // Load any necessary polyfills
    loadPolyfills();
  });
</script>
