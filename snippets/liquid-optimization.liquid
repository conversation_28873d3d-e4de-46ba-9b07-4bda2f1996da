{% comment %}
  Liquid Optimization Snippet
  
  This snippet contains techniques to optimize Liquid rendering performance.
  Include this in the theme.liquid file to apply these optimizations.
{% endcomment %}

{% comment %}
  Optimize Liquid rendering by using static variables where possible
  This reduces the number of Liquid operations that need to be performed
{% endcomment %}

{% comment %}
  Cache frequently used values
  This reduces the number of times Shopify needs to compute these values
{% endcomment %}
{% capture shop_name %}{{ shop.name }}{% endcapture %}
{% capture shop_url %}{{ shop.url }}{% endcapture %}
{% capture page_title %}{{ page_title | default: shop.name }}{% endcapture %}

{% comment %}
  Optimize collection rendering
  Only compute collection-related variables on collection pages
{% endcomment %}
{% if template contains 'collection' and collection %}
  {% capture collection_title %}{{ collection.title }}{% endcapture %}
  {% capture collection_description %}{{ collection.description }}{% endcapture %}
  {% capture collection_products_count %}{{ collection.products_count }}{% endcapture %}
{% endif %}

{% comment %}
  Optimize product rendering
  Only compute product-related variables on product pages
{% endcomment %}
{% if template contains 'product' and product %}
  {% capture product_title %}{{ product.title }}{% endcapture %}
  {% capture product_description %}{{ product.description }}{% endcapture %}
  {% capture product_price %}{{ product.price | money }}{% endcapture %}
  {% capture product_available %}{{ product.available }}{% endcapture %}
{% endif %}

{% comment %}
  Optimize cart rendering
  Cache cart-related values to reduce API calls
{% endcomment %}
{% capture cart_item_count %}{{ cart.item_count }}{% endcapture %}
{% capture cart_total_price %}{{ cart.total_price | money }}{% endcapture %}

{% comment %}
  Optimize pagination
  Only compute pagination-related variables on paginated pages
{% endcomment %}
{% if paginate %}
  {% capture paginate_current_page %}{{ paginate.current_page }}{% endcapture %}
  {% capture paginate_pages %}{{ paginate.pages }}{% endcapture %}
{% endif %}

{% comment %}
  Optimize search rendering
  Only compute search-related variables on search pages
{% endcomment %}
{% if template contains 'search' and search %}
  {% capture search_terms %}{{ search.terms }}{% endcapture %}
  {% capture search_results_count %}{{ search.results_count }}{% endcapture %}
{% endif %}

{% comment %}
  Optimize blog rendering
  Only compute blog-related variables on blog pages
{% endcomment %}
{% if template contains 'blog' and blog %}
  {% capture blog_title %}{{ blog.title }}{% endcapture %}
  {% capture blog_articles_count %}{{ blog.articles_count }}{% endcapture %}
{% endif %}

{% comment %}
  Optimize article rendering
  Only compute article-related variables on article pages
{% endcomment %}
{% if template contains 'article' and article %}
  {% capture article_title %}{{ article.title }}{% endcapture %}
  {% capture article_author %}{{ article.author }}{% endcapture %}
  {% capture article_published_at %}{{ article.published_at | date: '%B %d, %Y' }}{% endcapture %}
{% endif %}

{% comment %}
  Optimize page rendering
  Only compute page-related variables on page templates
{% endcomment %}
{% if template contains 'page' and page %}
  {% capture page_title %}{{ page.title }}{% endcapture %}
  {% capture page_content %}{{ page.content }}{% endcapture %}
{% endif %}

{% comment %}
  Set global JavaScript variables for client-side use
  This allows JavaScript to access these values without making API calls
{% endcomment %}
<script>
  // Store shop information
  window.shopInfo = {
    name: {{ shop.name | json }},
    url: {{ shop.url | json }},
    currency: {{ shop.currency | json }}
  };
  
  // Store cart information
  window.cartInfo = {
    itemCount: {{ cart.item_count | json }},
    totalPrice: {{ cart.total_price | json }}
  };
  
  // Store template information
  window.templateInfo = {
    name: {{ template.name | json }},
    directory: {{ template.directory | json }},
    suffix: {{ template.suffix | json }}
  };
  
  {% if template contains 'product' and product %}
  // Store product information
  window.productInfo = {
    id: {{ product.id | json }},
    title: {{ product.title | json }},
    handle: {{ product.handle | json }},
    price: {{ product.price | json }},
    available: {{ product.available | json }}
  };
  {% endif %}
  
  {% if template contains 'collection' and collection %}
  // Store collection information
  window.collectionInfo = {
    id: {{ collection.id | json }},
    title: {{ collection.title | json }},
    handle: {{ collection.handle | json }},
    productsCount: {{ collection.products_count | json }}
  };
  {% endif %}
</script>
