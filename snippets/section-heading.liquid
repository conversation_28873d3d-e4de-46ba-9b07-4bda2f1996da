{% comment %}
  Section Heading Snippet

  Usage:
  {% render 'section-heading', first_word: 'First', second_word: 'Word' %}

  Parameters:
  - first_word: The first word (outlined)
  - second_word: The second word (filled)
  - outline_color: Optional color for the outline (default: #000)
  - filled_color: Optional color for the filled text (default: #000)
  - custom_class: Optional custom class for additional styling
{% endcomment %}

{% assign outline_color = outline_color | default: '#000' %}
{% assign filled_color = filled_color | default: '#000' %}

<h2 class="section-heading {{ custom_class }}">
  <span class="section-heading__outline" style="--heading-outline-color: {{ outline_color }};">{{ first_word }}</span>
  <span class="section-heading__filled" style="--heading-filled-color: {{ filled_color }};">{{ second_word }}</span>
</h2>
