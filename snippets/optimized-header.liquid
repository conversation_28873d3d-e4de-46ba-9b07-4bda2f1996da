{% comment %}
  Optimized Header Snippet
  
  This snippet contains optimizations for the header section to reduce server response time.
  It implements lazy loading for non-critical header elements and optimizes Liquid rendering.
{% endcomment %}

{% comment %}
  Optimize header menu rendering
  Instead of using nested loops and complex Liquid logic, we'll use a simpler approach
  and defer some of the processing to client-side JavaScript
{% endcomment %}

<script>
  // Store menu data for client-side processing
  window.headerMenuData = {
    links: [
      {% for link in linklists.main-menu.links %}
        {
          title: {{ link.title | json }},
          url: {{ link.url | json }},
          active: {{ link.active | json }},
          childLinks: [
            {% for childlink in link.links %}
              {
                title: {{ childlink.title | json }},
                url: {{ childlink.url | json }},
                active: {{ childlink.active | json }}
              }{% unless forloop.last %},{% endunless %}
            {% endfor %}
          ]
        }{% unless forloop.last %},{% endunless %}
      {% endfor %}
    ]
  };
  
  // Function to initialize header menu
  function initHeaderMenu() {
    const menuContainer = document.querySelector('.header__menu');
    if (!menuContainer) return;
    
    // Only process if we have menu data
    if (!window.headerMenuData || !window.headerMenuData.links) return;
    
    // Create menu HTML
    const menuLinks = window.headerMenuData.links.map(link => {
      const hasChildren = link.childLinks && link.childLinks.length > 0;
      
      // Create child links HTML if needed
      let childLinksHtml = '';
      if (hasChildren) {
        childLinksHtml = `
          <ul class="header__submenu">
            ${link.childLinks.map(childLink => `
              <li class="header__submenu-item">
                <a href="${childLink.url}" class="header__submenu-link ${childLink.active ? 'active' : ''}">
                  ${childLink.title}
                </a>
              </li>
            `).join('')}
          </ul>
        `;
      }
      
      return `
        <li class="header__menu-item ${link.active ? 'active' : ''} ${hasChildren ? 'has-submenu' : ''}">
          <a href="${link.url}" class="header__menu-link">
            ${link.title}
            ${hasChildren ? '<span class="header__menu-arrow"></span>' : ''}
          </a>
          ${childLinksHtml}
        </li>
      `;
    }).join('');
    
    // Insert menu HTML
    menuContainer.innerHTML = menuLinks;
    
    // Add event listeners for mobile menu
    const mobileMenuToggle = document.querySelector('.header__mobile-menu-toggle');
    if (mobileMenuToggle) {
      mobileMenuToggle.addEventListener('click', function() {
        document.body.classList.toggle('mobile-menu-open');
      });
    }
    
    // Add event listeners for submenu toggles
    const submenuItems = document.querySelectorAll('.header__menu-item.has-submenu');
    submenuItems.forEach(item => {
      const link = item.querySelector('.header__menu-link');
      if (link) {
        link.addEventListener('click', function(e) {
          if (window.innerWidth < 990) {
            e.preventDefault();
            item.classList.toggle('submenu-open');
          }
        });
      }
    });
  }
  
  // Initialize header menu after DOM is loaded
  document.addEventListener('DOMContentLoaded', initHeaderMenu);
</script>

{% comment %}
  Optimize cart icon rendering
  Use a placeholder initially and update it with JavaScript
{% endcomment %}
<script>
  // Function to update cart count
  function updateCartCount() {
    const cartCountElements = document.querySelectorAll('.header__cart-count');
    
    // Try to get cart count from sessionStorage first
    let cartCount = 0;
    try {
      const storedCount = sessionStorage.getItem('cart_item_count');
      if (storedCount !== null) {
        cartCount = parseInt(storedCount, 10);
      } else {
        // If not in sessionStorage, use the server-rendered value
        cartCount = {{ cart.item_count }};
      }
    } catch (e) {
      cartCount = {{ cart.item_count }};
    }
    
    // Update all cart count elements
    cartCountElements.forEach(function(element) {
      element.textContent = cartCount;
      
      // Show/hide based on count
      if (cartCount > 0) {
        element.classList.add('has-items');
      } else {
        element.classList.remove('has-items');
      }
    });
  }
  
  // Update cart count after DOM is loaded
  document.addEventListener('DOMContentLoaded', updateCartCount);
  
  // Update cart count when cart is updated
  document.addEventListener('cart:updated', updateCartCount);
</script>

{% comment %}
  Optimize search form rendering
  Defer loading of search functionality
{% endcomment %}
<script>
  // Function to initialize search form
  function initSearchForm() {
    const searchToggle = document.querySelector('.header__search-toggle');
    const searchForm = document.querySelector('.header__search-form');
    
    if (searchToggle && searchForm) {
      // Add event listener to toggle search form
      searchToggle.addEventListener('click', function(e) {
        e.preventDefault();
        searchForm.classList.toggle('active');
        
        // Focus search input when form is shown
        if (searchForm.classList.contains('active')) {
          const searchInput = searchForm.querySelector('input[type="search"]');
          if (searchInput) {
            searchInput.focus();
          }
        }
      });
      
      // Close search form when clicking outside
      document.addEventListener('click', function(e) {
        if (!searchForm.contains(e.target) && !searchToggle.contains(e.target)) {
          searchForm.classList.remove('active');
        }
      });
    }
  }
  
  // Initialize search form after DOM is loaded
  document.addEventListener('DOMContentLoaded', initSearchForm);
</script>
