// CSS Loader - Optimized for performance
// This script helps load CSS files in a non-blocking way

(function() {
  'use strict';

  // Configuration
  const config = {
    // CSS files that should be loaded with high priority (but still non-blocking)
    highPriority: [
      'theme-variables.css',
      'base.css'
    ],
    // CSS files that can be loaded with lower priority
    lowPriority: [
      'theme.css',
      'keen-slider.css',
      'collection-filters.css',
      'section-headings.css',
      'button-styles.css'
    ],
    // CSS files that should only be loaded on specific templates
    conditional: {
      'collection': ['collection-page.css'],
      'product': ['product-page.css']
    }
  };

  // Helper function to load a CSS file
  function loadCSS(href, priority = 'low', media = 'all') {
    // Create link element
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    
    // Set appropriate attributes based on priority
    if (priority === 'high') {
      link.setAttribute('fetchpriority', 'high');
      // Use preload for high priority CSS
      link.rel = 'preload';
      link.as = 'style';
      link.onload = function() {
        // Convert from preload to stylesheet once loaded
        this.onload = null;
        this.rel = 'stylesheet';
      };
    } else {
      // For low priority, use print media initially to avoid render blocking
      link.media = 'print';
      link.onload = function() {
        // Switch to all media once loaded
        this.onload = null;
        this.media = 'all';
      };
    }
    
    // Add to document
    document.head.appendChild(link);
    
    // Return the link element for potential further manipulation
    return link;
  }

  // Function to load CSS files based on their priority
  function loadAllCSS(assetUrl) {
    // Load high priority CSS first
    config.highPriority.forEach(file => {
      loadCSS(assetUrl + file, 'high');
    });
    
    // Load low priority CSS with a slight delay
    setTimeout(() => {
      config.lowPriority.forEach(file => {
        // Skip files that are conditionally loaded
        let isConditional = false;
        for (const template in config.conditional) {
          if (config.conditional[template].includes(file)) {
            isConditional = true;
            break;
          }
        }
        
        if (!isConditional) {
          loadCSS(assetUrl + file, 'low');
        }
      });
    }, 100); // Small delay to prioritize high priority CSS
    
    // Load template-specific CSS if needed
    const templateClass = document.body.className;
    for (const template in config.conditional) {
      if (templateClass.includes('template-' + template)) {
        config.conditional[template].forEach(file => {
          loadCSS(assetUrl + file, 'low');
        });
      }
    }
  }

  // Initialize when DOM is ready or immediately if already loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      // Get the asset URL from a meta tag that will be added to the head
      const assetUrlMeta = document.getElementById('asset-url');
      if (assetUrlMeta) {
        loadAllCSS(assetUrlMeta.getAttribute('content'));
      }
    });
  } else {
    const assetUrlMeta = document.getElementById('asset-url');
    if (assetUrlMeta) {
      loadAllCSS(assetUrlMeta.getAttribute('content'));
    }
  }
})();
