<div id="SearchOverlay" class="search-overlay" aria-hidden="true">
  <div class="search-overlay__inner">
    <div class="page-width">
      <div class="search-overlay__header">
        <h2 class="search-overlay__title">{{ 'general.search.title' | t }}</h2>
        <button type="button" class="search-overlay__close" aria-controls="SearchOverlay" aria-expanded="true" tabindex="-1">
          <span class="search-overlay__close-icon"></span>
          <span class="visually-hidden">{{ 'general.search.close' | t }}</span>
        </button>
      </div>

      <div class="search-overlay__content">
        <form action="/search" method="get" role="search" class="search-overlay__form">
          <div class="search-overlay__input-wrapper">
            <input type="search"
                  name="q"
                  id="SearchOverlayInput"
                  class="search-overlay__input"
                  value="{{ search.terms | escape }}"
                  placeholder="{{ 'general.search.placeholder' | t }}"
                  aria-label="{{ 'general.search.placeholder' | t }}"
                  autocomplete="off"
                  autocorrect="off"
                  autocapitalize="off"
                  spellcheck="false"
                  tabindex="-1">
            <button type="submit" class="search-overlay__submit" tabindex="-1">
              {% render 'icon-search' %}
              <span class="visually-hidden">{{ 'general.search.submit' | t }}</span>
            </button>
          </div>
        </form>

        <div id="PredictiveResults" class="predictive-results" aria-live="polite"></div>
      </div>
    </div>
  </div>
</div>

<style>
  .search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.98);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    overflow-y: auto;
  }

  .search-overlay[aria-hidden="false"] {
    opacity: 1;
    visibility: visible;
  }

  .search-overlay__inner {
    padding: 60px 0;
  }

  .search-overlay__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .search-overlay__title {
    font-size: 24px;
    margin: 0;
  }

  .search-overlay__close {
    background: transparent;
    border: none;
    padding: 10px;
    cursor: pointer;
  }

  .search-overlay__close-icon {
    position: relative;
    display: block;
    width: 20px;
    height: 20px;
  }

  .search-overlay__close-icon::before,
  .search-overlay__close-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--color-text);
  }

  .search-overlay__close-icon::before {
    transform: rotate(45deg);
  }

  .search-overlay__close-icon::after {
    transform: rotate(-45deg);
  }

  .search-overlay__form {
    margin-bottom: 30px;
  }

  .search-overlay__input-wrapper {
    position: relative;
    display: flex;
    border-bottom: 2px solid var(--color-text);
  }

  .search-overlay__input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 15px 50px 15px 0;
    font-size: 18px;
    outline: none;
    width: 100%;
  }

  .search-overlay__submit {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    padding: 10px;
    cursor: pointer;
  }

  .search-overlay__submit .icon {
    width: 20px;
    height: 20px;
  }

  .predictive-results {
    margin-top: 20px;
  }

  .predictive-result-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--color-border);
    transition: opacity 0.2s ease;
  }

  .predictive-result-item:hover {
    opacity: 0.7;
  }

  .predictive-result-item__image {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    object-fit: cover;
  }

  .predictive-result-item__info {
    flex: 1;
  }

  .predictive-result-item__title {
    font-weight: var(--font-weight-medium);
    margin: 0 0 5px;
  }

  .predictive-result-item__price {
    font-size: 14px;
  }

  .predictive-result-item__price--sale {
    color: var(--color-sale);
  }

  .predictive-result-item__price--compare {
    text-decoration: line-through;
    opacity: 0.7;
    margin-left: 5px;
  }

  .predictive-results__empty {
    padding: 20px 0;
    text-align: center;
    color: var(--color-secondary);
  }

  .predictive-results__loading {
    padding: 20px 0;
    text-align: center;
  }

  @media screen and (max-width: 749px) {
    .search-overlay__inner {
      padding: 30px 0;
    }

    .search-overlay__title {
      font-size: 20px;
    }

    .search-overlay__input {
      font-size: 16px;
      padding: 10px 40px 10px 0;
    }
  }
</style>

<script>
  class PredictiveSearch {
    constructor() {
      this.searchInput = document.getElementById('SearchOverlayInput');
      this.resultsContainer = document.getElementById('PredictiveResults');
      this.searchTimeout = null;
      this.isLoading = false;

      this.init();
    }

    init() {
      if (!this.searchInput || !this.resultsContainer) return;

      this.searchInput.addEventListener('input', this.handleInput.bind(this));
      this.searchInput.addEventListener('focus', this.handleInput.bind(this));
    }

    handleInput(event) {
      const searchTerm = event.target.value.trim();

      clearTimeout(this.searchTimeout);

      if (searchTerm.length < 3) {
        this.resultsContainer.innerHTML = '';
        return;
      }

      this.showLoading();

      this.searchTimeout = setTimeout(() => {
        this.performSearch(searchTerm);
      }, 500);
    }

    showLoading() {
      this.isLoading = true;
      this.resultsContainer.innerHTML = '<div class="predictive-results__loading">Searching...</div>';
    }

    async performSearch(searchTerm) {
      try {
        const response = await fetch(`/search/suggest.json?q=${encodeURIComponent(searchTerm)}&resources[type]=product&resources[limit]=4&resources[options][unavailable_products]=last`);
        const data = await response.json();

        this.displayResults(data.resources.results);
      } catch (error) {
        console.error('Error fetching search results:', error);
        this.resultsContainer.innerHTML = '<div class="predictive-results__empty">An error occurred. Please try again.</div>';
      } finally {
        this.isLoading = false;
      }
    }

    displayResults(results) {
      const products = results.products || [];

      if (products.length === 0) {
        this.resultsContainer.innerHTML = '<div class="predictive-results__empty">No results found</div>';
        return;
      }

      let html = '';

      products.forEach(product => {
        const image = product.image ? `<img src="${product.image}" alt="${product.title}" class="predictive-result-item__image">` : '';
        const price = this.formatPrice(product.price);
        const comparePrice = product.compare_at_price ? this.formatPrice(product.compare_at_price) : '';
        const compareHtml = comparePrice ? `<span class="predictive-result-item__price--compare">${comparePrice}</span>` : '';
        const priceClass = comparePrice ? 'predictive-result-item__price--sale' : '';

        html += `
          <a href="${product.url}" class="predictive-result-item">
            ${image}
            <div class="predictive-result-item__info">
              <h3 class="predictive-result-item__title">${product.title}</h3>
              <div class="predictive-result-item__price">
                <span class="${priceClass}">${price}</span>
                ${compareHtml}
              </div>
            </div>
          </a>
        `;
      });

      this.resultsContainer.innerHTML = html;
    }

    formatPrice(price) {
      return price ? '$' + (parseFloat(price) / 100).toFixed(2) : '';
    }
  }

  document.addEventListener('DOMContentLoaded', function() {
    // Initialize search overlay toggle
    const searchToggles = document.querySelectorAll('.header-icon[data-search-toggle]');
    const searchOverlay = document.getElementById('SearchOverlay');
    const searchOverlayClose = document.querySelector('.search-overlay__close');
    let searchFocusTrap = null;

    if (searchToggles.length > 0 && searchOverlay && searchOverlayClose) {
      // Create a focus trap for the search overlay
      searchFocusTrap = window.accessibilityUtils.createFocusTrap(searchOverlay);

      // Add click event to all search toggles
      searchToggles.forEach(searchToggle => {
        searchToggle.addEventListener('click', function(e) {
          e.preventDefault();

          // Open the search overlay
          searchOverlay.setAttribute('aria-hidden', 'false');
          document.body.style.overflow = 'hidden';

          // Enable all focusable elements inside
          const focusableElements = searchOverlay.querySelectorAll('a, button, input, select, textarea, [tabindex="-1"]');
          focusableElements.forEach(el => {
            if (el.hasAttribute('data-aria-hidden-tabindex')) {
              el.removeAttribute('tabindex');
            }
          });

          // Activate focus trap
          searchFocusTrap.activate();

          // Focus on search input
          setTimeout(() => {
            const searchInput = document.getElementById('SearchOverlayInput');
            if (searchInput) {
              searchInput.removeAttribute('tabindex');
              searchInput.focus();
            }
          }, 100);
        });
      });

      searchOverlayClose.addEventListener('click', function() {
        // Close the search overlay
        searchOverlay.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';

        // Deactivate focus trap
        searchFocusTrap.deactivate();

        // Return focus to the first toggle button
        if (searchToggles.length > 0) {
          searchToggles[0].focus();
        }
      });

      // Close on escape key
      searchOverlay.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && searchOverlay.getAttribute('aria-hidden') === 'false') {
          // Close the search overlay
          searchOverlay.setAttribute('aria-hidden', 'true');
          document.body.style.overflow = '';

          // Deactivate focus trap
          searchFocusTrap.deactivate();

          // Return focus to the first toggle button
          if (searchToggles.length > 0) {
            searchToggles[0].focus();
          }
        }
      });
    }

    // Initialize predictive search
    new PredictiveSearch();
  });
</script>
