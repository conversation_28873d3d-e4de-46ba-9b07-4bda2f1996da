{%- comment -%}
  Styled Product Grid Snippet

  Usage:
  {% render 'product-grid-styled',
    collection: collection,
    products_per_row: products_per_row,
    products_per_row_tablet: products_per_row_tablet,
    products_per_row_mobile: products_per_row_mobile,
    products_limit: products_limit
  %}

  Parameters:
  - collection: Collection object to display products from
  - products_per_row: Number of products to show per row on desktop (default: 4)
  - products_per_row_tablet: Number of products to show per row on tablet (default: 2)
  - products_per_row_mobile: Number of products to show per row on mobile (default: 1)
  - products_limit: Maximum number of products to show (default: 8)
{%- endcomment -%}

{%- liquid
  assign products_per_row = products_per_row | default: 4
  assign products_per_row_tablet = products_per_row_tablet | default: 2
  assign products_per_row_mobile = products_per_row_mobile | default: 1
  assign products_limit = products_limit | default: 8
-%}

<div class="product-grid-styled"
  style="--products-per-row: {{ products_per_row }}; --products-per-row-tablet: {{ products_per_row_tablet }}; --products-per-row-mobile: {{ products_per_row_mobile }};">
  {% for product in collection.products limit: products_limit %}
    {% render 'product-card-styled', product: product %}
  {% endfor %}
</div>

<style>
  .product-grid-styled {
    display: grid;
    grid-template-columns: repeat(var(--products-per-row), 1fr);
    gap: 30px;
    margin: 30px 0;
  }

  @media screen and (max-width: 989px) {
    .product-grid-styled {
      grid-template-columns: repeat(var(--products-per-row-tablet), 1fr);
      gap: 20px;
    }
  }

  @media screen and (max-width: 749px) {
    .product-grid-styled {
      grid-template-columns: repeat(var(--products-per-row-mobile), 1fr);
      gap: 15px;
    }
  }
</style>
