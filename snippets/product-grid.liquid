{%- comment -%}
  Product Grid Snippet

  Usage:
  {% render 'product-grid',
    collection: collection,
    products_per_row: products_per_row,
    products_limit: products_limit
  %}

  Parameters:
  - collection: Collection object to display products from
  - products_per_row: Number of products to show per row (default: 4)
  - products_limit: Maximum number of products to show (default: 8)
{%- endcomment -%}

{%- liquid
  assign products_per_row = products_per_row | default: 4
  assign products_limit = products_limit | default: 8
-%}

<div class="product-grid" style="--products-per-row: {{ products_per_row }};">
  {% for product in collection.products limit: products_limit %}
    <div class="product-grid__item">
      <div class="product-card">
        <a href="{{ product.url }}" class="product-card__link">
          <div class="product-card__image-wrapper">
            {% if product.featured_media %}
              <img
                src="{{ product.featured_media | img_url: '300x300', crop: 'center' }}"
                srcset="{{ product.featured_media | img_url: '300x300', crop: 'center' }} 1x, {{ product.featured_media | img_url: '600x600', crop: 'center' }} 2x"
                alt="{{ product.featured_media.alt | escape }}"
                loading="lazy"
                width="300"
                height="300"
                class="product-card__image"
              >
            {% else %}
              {{ 'product-1' | placeholder_svg_tag: 'product-card__image placeholder-svg' }}
            {% endif %}

            {% if product.compare_at_price > product.price %}
              <span class="product-card__badge">{{ 'products.product.on_sale' | t }}</span>
            {% endif %}
          </div>

          <div class="product-card__info">
            <h3 class="product-card__title">{{ product.title }}</h3>

            <div class="product-card__price">
              {% if product.compare_at_price > product.price %}
                <span class="product-card__price--sale">{{ product.price | money }}</span>
                <span class="product-card__price--compare">{{ product.compare_at_price | money }}</span>
              {% else %}
                <span class="product-card__price--regular">{{ product.price | money }}</span>
              {% endif %}
            </div>
          </div>
        </a>

        {% if product.available %}
          <form method="post" action="/cart/add" class="product-card__form">
            <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
            <button type="submit" class="product-card__add-to-cart button button--small">
              {{ 'products.product.add_to_cart' | t }}
            </button>
          </form>
        {% else %}
          <button type="button" class="product-card__add-to-cart button button--small button--disabled" disabled>
            {{ 'products.product.sold_out' | t }}
          </button>
        {% endif %}
      </div>
    </div>
  {% endfor %}
</div>

<style>
  .product-grid {
    display: grid;
    grid-template-columns: repeat(var(--products-per-row), 1fr);
    gap: var(--spacing-base);
  }

  @media screen and (max-width: 989px) {
    .product-grid {
      --products-per-row: 3;
    }
  }

  @media screen and (max-width: 749px) {
    .product-grid {
      --products-per-row: 2;
    }
  }

  @media screen and (max-width: 480px) {
    .product-grid {
      --products-per-row: 1;
    }
  }
</style>
