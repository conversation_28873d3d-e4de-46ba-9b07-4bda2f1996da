{% comment %}
  Critical CSS for hero slider on mobile
  This is inlined in the head to ensure fast rendering of the hero slider
{% endcomment %}

<style>
/* Critical mobile-specific styles for hero carousel */
@media screen and (max-width: 749px) {
  .hero-carousel {
    margin-bottom: 0;
    overflow: hidden;
    position: relative;
  }

  .hero-carousel__wrapper {
    position: relative;
    width: 100%;
    height: 50vh; /* Fixed height of 50% of viewport height for mobile */
    min-height: 300px; /* Minimum height to ensure visibility */
    max-height: 500px; /* Maximum height to prevent excessive space */
    overflow: hidden;
    background-color: #f7f7f7;
  }

  .hero-carousel__slider {
    display: flex;
    width: 100%;
    height: 100%;
    position: relative;
  }

  .hero-carousel__slide {
    flex: 0 0 100%;
    width: 100%;
    position: absolute;
    opacity: 0;
    z-index: 1;
    pointer-events: none;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
  }

  .hero-carousel__slide.active {
    opacity: 1;
    z-index: 2;
    position: absolute;
    pointer-events: auto;
    display: block;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
  }

  .hero-carousel__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
  }

  .hero-carousel__image--mobile {
    display: block;
  }

  .hero-carousel__image--desktop {
    display: none;
  }
}
</style>
