/* Critical CSS for Levo Theme - Optimized for Performance */
/* This file contains only the essential styles needed for above-the-fold content */

/* Base styles */
*{box-sizing:border-box;margin:0;padding:0}

body {
  font-family: var(--font-body, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif);
  font-weight: 400;
  font-size: var(--font-size-base, 16px);
  line-height: var(--theme-line-height, 1.5);
  color: var(--color-text-primary, #000);
  background-color: var(--color-background, #fff);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-size-adjust: 100%;
}

.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1 0 auto;
}

.page-width {
  width: 100%;
  margin: 0 auto;
  padding-left: var(--gutter);
  padding-right: var(--gutter);
  box-sizing: border-box;
}

.full-width {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Header styles */
.site-header {
  padding: 0;
  border-bottom: none;
  position: relative;
  z-index: 100;
  width: 100%;
}

.header-top-container {
  padding: 15px 0;
  width: 100%;
}

.header-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.header-logo {
  flex: 0 0 auto;
  text-align: center;
  margin: 0 auto;
}

.header-logo img {
  max-height: 50px;
  width: auto;
}

.header-icons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.header-icon {
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
  margin-left: 20px;
  color: var(--color-text-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Navigation styles */
.header-nav-container {
  background-color: var(--color-background);
  border-top: 1px solid var(--color-border);
  border-bottom: 1px solid var(--color-border);
  width: 100%;
  position: relative;
  z-index: 100;
  padding: 5px 0;
}

.nav-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40px;
}

.header-nav {
  display: flex;
  justify-content: center;
  flex: 1;
}

.site-nav {
  display: flex;
  justify-content: center;
  list-style: none;
  margin: 0;
  padding: 0;
  width: 100%;
}

.site-nav__item {
  position: relative;
  margin: 0 10px;
  height: 46px;
}

.site-nav__link {
  display: block;
  padding: 15px 0;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: var(--font-weight-bold);
  position: relative;
  color: var(--color-text-primary);
  height: 100%;
  box-sizing: border-box;
}

/* Hero carousel critical styles - Optimized */
.hero-carousel{margin-bottom:0;overflow:hidden;position:relative}
.hero-carousel__wrapper{position:relative;width:100%;height:40vh;overflow:hidden}
.hero-carousel__slider{display:flex;width:100%;height:100%;position:relative}
.hero-carousel__slide{flex:0 0 100%;width:100%;position:absolute;opacity:0;z-index:1;pointer-events:none;height:100%;display:none}
.hero-carousel__slide.active{opacity:1;z-index:2;position:relative;pointer-events:auto;display:block}
.hero-carousel__slide-wrapper{position:relative;overflow:hidden;height:100%;width:100%;display:flex;flex-direction:column}
.hero-carousel__image-container{position:relative;height:100%;width:100%;flex:1;min-height:0;overflow:hidden;background-color:#f7f7f7;display:block}
.hero-carousel__image{width:100%;height:100%;object-fit:cover;object-position:center;display:block;position:absolute;top:0;left:0;right:0;bottom:0;max-height:none;min-height:100%}
@media screen and (max-width:749px){.hero-carousel__wrapper{height:50vh}}

/* Category grid critical styles */
.category-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(4, 1fr);
}

.category-item {
  position: relative;
  overflow: hidden;
}

.category-item__image-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding-top: 100%;
  background-color: #f0f0f0;
}

.category-item__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Utility classes */
.visually-hidden {
  position: absolute !important;
  overflow: hidden;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  clip: rect(0 0 0 0);
  word-wrap: normal !important;
}

.focusable:focus {
  position: static !important;
  overflow: visible;
  width: auto;
  height: auto;
  margin: 0;
  clip: auto;
}

/* Skip to content link */
.skip-to-content-link {
  background-color: var(--color-primary);
  color: white;
  padding: 10px 20px;
  z-index: 9999;
  position: absolute;
  top: 0;
  left: 0;
  transform: translateY(-100%);
  transition: transform 0.3s;
}

.skip-to-content-link:focus {
  transform: translateY(0);
}
