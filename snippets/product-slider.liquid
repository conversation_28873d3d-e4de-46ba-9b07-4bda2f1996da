{%- comment -%}
  Product Slider Snippet
  
  Usage:
  {% render 'product-slider', 
    products: collection.products,
    title: 'Featured Products',
    slides_per_view: 4,
    show_dots: true,
    show_arrows: true,
    autoplay: false,
    autoplay_speed: 3000,
    slider_id: 'my-slider'
  %}
  
  Parameters:
  - products: Collection of products to display (required)
  - title: Heading for the slider (optional)
  - slides_per_view: Number of slides to show at once (default: 4)
  - slides_per_view_mobile: Number of slides on mobile (default: 1)
  - slides_per_view_tablet: Number of slides on tablet (default: 2)
  - show_dots: Show navigation dots (default: true)
  - show_arrows: Show navigation arrows (default: true)
  - autoplay: Enable autoplay (default: false)
  - autoplay_speed: Autoplay speed in ms (default: 3000)
  - slider_id: Unique ID for the slider (required for multiple sliders on one page)
{%- endcomment -%}

{%- liquid
  assign slider_id = slider_id | default: 'product-slider-' | append: section.id
  assign slides_per_view = slides_per_view | default: 4
  assign slides_per_view_mobile = slides_per_view_mobile | default: 1
  assign slides_per_view_tablet = slides_per_view_tablet | default: 2
  assign show_dots = show_dots | default: true
  assign show_arrows = show_arrows | default: true
  assign autoplay = autoplay | default: false
  assign autoplay_speed = autoplay_speed | default: 3000
-%}

<div class="product-slider-wrapper" data-slider-id="{{ slider_id }}">
  {% if title != blank %}
    <h2 class="product-slider-title">{{ title }}</h2>
  {% endif %}
  
  <div class="product-slider-container">
    {% if show_arrows %}
      <button type="button" class="product-slider-arrow product-slider-arrow--prev" data-slider-prev aria-label="{{ 'general.slider.previous' | t }}">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
          <path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/>
        </svg>
      </button>
    {% endif %}
    
    <div id="{{ slider_id }}" class="keen-slider product-slider">
      {% for product in products %}
        <div class="keen-slider__slide product-slider__slide">
          <div class="product-card">
            <a href="{{ product.url }}" class="product-card__link">
              <div class="product-card__image-wrapper">
                {% if product.featured_media %}
                  <img 
                    src="{{ product.featured_media | img_url: '300x300', crop: 'center' }}"
                    srcset="{{ product.featured_media | img_url: '300x300', crop: 'center' }} 1x, {{ product.featured_media | img_url: '600x600', crop: 'center' }} 2x"
                    alt="{{ product.featured_media.alt | escape }}"
                    loading="lazy"
                    width="300"
                    height="300"
                    class="product-card__image"
                  >
                {% else %}
                  {{ 'product-1' | placeholder_svg_tag: 'product-card__image placeholder-svg' }}
                {% endif %}
                
                {% if product.compare_at_price > product.price %}
                  <span class="product-card__badge">{{ 'products.product.on_sale' | t }}</span>
                {% endif %}
              </div>
              
              <div class="product-card__info">
                <h3 class="product-card__title">{{ product.title }}</h3>
                
                <div class="product-card__price">
                  {% if product.compare_at_price > product.price %}
                    <span class="product-card__price--sale">{{ product.price | money }}</span>
                    <span class="product-card__price--compare">{{ product.compare_at_price | money }}</span>
                  {% else %}
                    <span class="product-card__price--regular">{{ product.price | money }}</span>
                  {% endif %}
                </div>
              </div>
            </a>
            
            {% if product.available %}
              <form method="post" action="/cart/add" class="product-card__form">
                <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                <button type="submit" class="product-card__add-to-cart button button--small">
                  {{ 'products.product.add_to_cart' | t }}
                </button>
              </form>
            {% else %}
              <button type="button" class="product-card__add-to-cart button button--small button--disabled" disabled>
                {{ 'products.product.sold_out' | t }}
              </button>
            {% endif %}
          </div>
        </div>
      {% endfor %}
    </div>
    
    {% if show_arrows %}
      <button type="button" class="product-slider-arrow product-slider-arrow--next" data-slider-next aria-label="{{ 'general.slider.next' | t }}">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
          <path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"/>
        </svg>
      </button>
    {% endif %}
  </div>
  
  {% if show_dots %}
    <div class="product-slider-dots" data-slider-dots></div>
  {% endif %}
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    initProductSlider('{{ slider_id }}');
  });
  
  function initProductSlider(sliderId) {
    const sliderElement = document.getElementById(sliderId);
    if (!sliderElement) return;
    
    const sliderWrapper = document.querySelector(`[data-slider-id="${sliderId}"]`);
    const prevButton = sliderWrapper.querySelector('[data-slider-prev]');
    const nextButton = sliderWrapper.querySelector('[data-slider-next]');
    const dotsElement = sliderWrapper.querySelector('[data-slider-dots]');
    
    const options = {
      slides: {
        perView: {{ slides_per_view }},
        spacing: 16,
      },
      loop: true,
      {% if autoplay %}
      dragStart: () => {
        autoplayStop();
      },
      dragEnd: () => {
        autoplayStart();
      },
      {% endif %}
      breakpoints: {
        '(max-width: 749px)': {
          slides: {
            perView: {{ slides_per_view_mobile }},
            spacing: 16,
          },
        },
        '(min-width: 750px) and (max-width: 989px)': {
          slides: {
            perView: {{ slides_per_view_tablet }},
            spacing: 16,
          },
        }
      },
      created: (slider) => {
        if (dotsElement) {
          setupDots(slider, dotsElement);
        }
        
        {% if autoplay %}
        autoplayStart();
        {% endif %}
      }
    };
    
    const keenSlider = new KeenSlider(sliderElement, options);
    
    if (prevButton) {
      prevButton.addEventListener('click', () => keenSlider.prev());
    }
    
    if (nextButton) {
      nextButton.addEventListener('click', () => keenSlider.next());
    }
    
    {% if autoplay %}
    let autoplayTimer;
    
    function autoplayStart() {
      autoplayTimer = setInterval(() => {
        keenSlider.next();
      }, {{ autoplay_speed }});
    }
    
    function autoplayStop() {
      clearInterval(autoplayTimer);
    }
    {% endif %}
    
    function setupDots(slider, dotsElement) {
      const slides = slider.details().size;
      dotsElement.innerHTML = '';
      
      for (let i = 0; i < slides; i++) {
        const dot = document.createElement('button');
        dot.classList.add('product-slider-dot');
        dot.setAttribute('aria-label', `Go to slide ${i + 1}`);
        dot.addEventListener('click', () => {
          slider.moveToIdx(i);
        });
        dotsElement.appendChild(dot);
      }
      
      updateActiveDot(slider.details().rel);
      
      slider.on('slideChanged', (s) => {
        updateActiveDot(s.details().rel);
      });
      
      function updateActiveDot(slideIndex) {
        const dots = dotsElement.querySelectorAll('.product-slider-dot');
        dots.forEach((dot, idx) => {
          if (idx === slideIndex) {
            dot.classList.add('product-slider-dot--active');
            dot.setAttribute('aria-current', 'true');
          } else {
            dot.classList.remove('product-slider-dot--active');
            dot.removeAttribute('aria-current');
          }
        });
      }
    }
  }
</script>

<style>
  .product-slider-wrapper {
    margin: var(--spacing-extra-loose) 0;
  }
  
  .product-slider-title {
    margin-bottom: var(--spacing-base);
    text-align: center;
  }
  
  .product-slider-container {
    position: relative;
  }
  
  .product-slider {
    overflow: visible;
  }
  
  .product-slider__slide {
    padding: var(--spacing-tight);
  }
  
  .product-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
    overflow: hidden;
  }
  
  .product-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  .product-card__link {
    display: block;
    text-decoration: none;
    color: inherit;
  }
  
  .product-card__image-wrapper {
    position: relative;
    padding-top: 100%;
    overflow: hidden;
  }
  
  .product-card__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .product-card:hover .product-card__image {
    transform: scale(1.05);
  }
  
  .product-card__badge {
    position: absolute;
    top: var(--spacing-tight);
    right: var(--spacing-tight);
    background-color: var(--color-primary);
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: var(--border-radius);
  }
  
  .product-card__info {
    padding: var(--spacing-base);
    flex-grow: 1;
  }
  
  .product-card__title {
    font-size: 16px;
    margin-bottom: var(--spacing-tight);
    font-weight: 500;
  }
  
  .product-card__price {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-tight);
  }
  
  .product-card__price--sale {
    color: #e32c2b;
    font-weight: 700;
  }
  
  .product-card__price--compare {
    text-decoration: line-through;
    opacity: 0.6;
    font-size: 14px;
  }
  
  .product-card__price--regular {
    font-weight: 700;
  }
  
  .product-card__form {
    padding: 0 var(--spacing-base) var(--spacing-base);
  }
  
  .product-card__add-to-cart {
    width: 100%;
    font-size: 14px;
    padding: 8px 16px;
  }
  
  .product-slider-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    transition: background-color 0.2s ease;
  }
  
  .product-slider-arrow:hover {
    background-color: var(--color-primary);
  }
  
  .product-slider-arrow:hover svg {
    fill: white;
  }
  
  .product-slider-arrow svg {
    width: 12px;
    height: 12px;
    fill: var(--color-text);
    transition: fill 0.2s ease;
  }
  
  .product-slider-arrow--prev {
    left: -20px;
  }
  
  .product-slider-arrow--next {
    right: -20px;
  }
  
  .product-slider-dots {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-base);
    gap: 8px;
  }
  
  .product-slider-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.2);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .product-slider-dot--active {
    background-color: var(--color-primary);
  }
  
  @media screen and (max-width: 749px) {
    .product-slider-arrow {
      width: 30px;
      height: 30px;
    }
    
    .product-slider-arrow--prev {
      left: -15px;
    }
    
    .product-slider-arrow--next {
      right: -15px;
    }
  }
</style>
