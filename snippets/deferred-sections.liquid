{% comment %}
  Deferred Sections Snippet
  
  This snippet implements techniques to defer loading of non-critical sections.
  Include this in templates to improve initial server response time.
{% endcomment %}

{% comment %}
  Deferred Footer Content
  Load footer content after the initial page render
{% endcomment %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Defer loading of footer content
    const footerContainer = document.querySelector('.footer__content');
    if (footerContainer) {
      // Set a placeholder initially
      const originalContent = footerContainer.innerHTML;
      
      // Only defer on non-critical pages
      {% if template.name != 'cart' and template.name != 'checkout' %}
        // Create a placeholder for the footer content
        footerContainer.innerHTML = `
          <div class="footer__placeholder">
            <div class="footer__placeholder-item"></div>
            <div class="footer__placeholder-item"></div>
            <div class="footer__placeholder-item"></div>
          </div>
        `;
        
        // Load the footer content after a short delay
        setTimeout(function() {
          footerContainer.innerHTML = originalContent;
          
          // Dispatch event to notify that content has been loaded
          footerContainer.dispatchEvent(new CustomEvent('content-loaded'));
        }, 500);
      {% endif %}
    }
  });
</script>

{% comment %}
  Deferred Sidebar Content
  Load sidebar content after the initial page render on collection pages
{% endcomment %}
{% if template contains 'collection' %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Defer loading of collection sidebar
      const sidebarContainer = document.querySelector('.collection-sidebar');
      if (sidebarContainer) {
        // Get the URL for the sidebar content
        const sidebarUrl = `${window.location.pathname}?view=sidebar`;
        
        // Set a placeholder initially
        sidebarContainer.innerHTML = `
          <div class="sidebar-placeholder">
            <div class="sidebar-placeholder__item"></div>
            <div class="sidebar-placeholder__item"></div>
            <div class="sidebar-placeholder__item"></div>
          </div>
        `;
        
        // Load the sidebar content after the main content
        setTimeout(function() {
          fetch(sidebarUrl)
            .then(response => response.text())
            .then(html => {
              sidebarContainer.innerHTML = html;
              
              // Initialize any sidebar functionality
              if (window.initSidebar) {
                window.initSidebar();
              }
              
              // Dispatch event to notify that content has been loaded
              sidebarContainer.dispatchEvent(new CustomEvent('sidebar-loaded'));
            })
            .catch(error => {
              console.error('Error loading sidebar content:', error);
              // Fallback to a simple sidebar
              sidebarContainer.innerHTML = `
                <div class="collection-sidebar__section">
                  <h3>Filter By</h3>
                  <div class="collection-sidebar__links">
                    <a href="{{ collection.url }}">All Products</a>
                  </div>
                </div>
              `;
            });
        }, 100);
      }
    });
  </script>
{% endif %}

{% comment %}
  Deferred Product Recommendations
  Load product recommendations after the initial page render on product pages
{% endcomment %}
{% if template contains 'product' %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Defer loading of product recommendations
      const recommendationsContainer = document.querySelector('.product-recommendations');
      if (recommendationsContainer) {
        // Set a placeholder initially
        recommendationsContainer.innerHTML = `
          <div class="recommendations-placeholder">
            <div class="recommendations-placeholder__title"></div>
            <div class="recommendations-placeholder__grid">
              <div class="recommendations-placeholder__item"></div>
              <div class="recommendations-placeholder__item"></div>
              <div class="recommendations-placeholder__item"></div>
              <div class="recommendations-placeholder__item"></div>
            </div>
          </div>
        `;
        
        // Load the recommendations after the main product content
        const productId = {{ product.id | default: 0 }};
        if (productId > 0) {
          setTimeout(function() {
            const recommendationsUrl = `/recommendations/products?product_id=${productId}&limit=4&section_id=product-recommendations`;
            
            fetch(recommendationsUrl)
              .then(response => response.text())
              .then(html => {
                recommendationsContainer.innerHTML = html;
                
                // Initialize any product features on the recommendations
                if (window.initProductFeatures) {
                  window.initProductFeatures();
                }
                
                // Dispatch event to notify that content has been loaded
                recommendationsContainer.dispatchEvent(new CustomEvent('recommendations-loaded'));
              })
              .catch(error => {
                console.error('Error loading product recommendations:', error);
                recommendationsContainer.innerHTML = '';
              });
          }, 200);
        }
      }
    });
  </script>
{% endif %}
