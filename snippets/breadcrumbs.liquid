{% comment %}
  Breadcrumbs Navigation
  
  Usage:
  {% render 'breadcrumbs' %}
  
  This snippet automatically generates breadcrumbs based on the current page.
  It handles product, collection, page, blog, and article pages.
{% endcomment %}

<nav class="breadcrumbs" aria-label="Breadcrumbs">
  <div class="page-width">
    <ol class="breadcrumbs__list">
      <li class="breadcrumbs__item">
        <a href="{{ routes.root_url }}" class="breadcrumbs__link">Home</a>
        <span class="breadcrumbs__separator" aria-hidden="true">/</span>
      </li>

      {% case template %}
        {% when 'product' %}
          {% if collection.url %}
            <li class="breadcrumbs__item">
              <a href="{{ collection.url }}" class="breadcrumbs__link">{{ collection.title }}</a>
              <span class="breadcrumbs__separator" aria-hidden="true">/</span>
            </li>
          {% else %}
            {% if product.collections.first %}
              <li class="breadcrumbs__item">
                <a href="{{ product.collections.first.url }}" class="breadcrumbs__link">{{ product.collections.first.title }}</a>
                <span class="breadcrumbs__separator" aria-hidden="true">/</span>
              </li>
            {% endif %}
          {% endif %}
          <li class="breadcrumbs__item">
            <span class="breadcrumbs__current" aria-current="page">{{ product.title }}</span>
          </li>

        {% when 'collection' %}
          {% if collection.handle == 'all' %}
            <li class="breadcrumbs__item">
              <span class="breadcrumbs__current" aria-current="page">All Products</span>
            </li>
          {% else %}
            <li class="breadcrumbs__item">
              <span class="breadcrumbs__current" aria-current="page">{{ collection.title }}</span>
            </li>
          {% endif %}

        {% when 'blog' %}
          <li class="breadcrumbs__item">
            <span class="breadcrumbs__current" aria-current="page">{{ blog.title }}</span>
          </li>

        {% when 'article' %}
          <li class="breadcrumbs__item">
            <a href="{{ blog.url }}" class="breadcrumbs__link">{{ blog.title }}</a>
            <span class="breadcrumbs__separator" aria-hidden="true">/</span>
          </li>
          <li class="breadcrumbs__item">
            <span class="breadcrumbs__current" aria-current="page">{{ article.title }}</span>
          </li>

        {% when 'page' %}
          <li class="breadcrumbs__item">
            <span class="breadcrumbs__current" aria-current="page">{{ page.title }}</span>
          </li>

        {% else %}
          {% if request.path contains '/collections/' and request.path contains '/products/' %}
            {% assign url_parts = request.path | split: '/' %}
            {% assign collection_handle = url_parts[2] %}
            {% assign product_handle = url_parts[4] %}
            
            <li class="breadcrumbs__item">
              <a href="/collections/{{ collection_handle }}" class="breadcrumbs__link">{{ collections[collection_handle].title }}</a>
              <span class="breadcrumbs__separator" aria-hidden="true">/</span>
            </li>
            <li class="breadcrumbs__item">
              <span class="breadcrumbs__current" aria-current="page">{{ product_handle | replace: '-', ' ' | capitalize }}</span>
            </li>
          {% endif %}
      {% endcase %}
    </ol>
  </div>
</nav>

<style>
  .breadcrumbs {
    padding: 10px 0;
    font-size: 12px;
    color: #666;
  }

  .breadcrumbs__list {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .breadcrumbs__item {
    display: flex;
    align-items: center;
  }

  .breadcrumbs__link {
    color: #666;
    text-decoration: none;
  }

  .breadcrumbs__link:hover {
    text-decoration: underline;
  }

  .breadcrumbs__separator {
    margin: 0 5px;
  }

  .breadcrumbs__current {
    font-weight: 500;
  }
</style>
