{% comment %}
  Product Card Styled Snippet

  Usage:
  {% render 'product-card-styled', product: product %}

  Parameters:
  - product: The product object to display
{% endcomment %}

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="{{ product.url | within: collection }}" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        {% if product.featured_media %}
          {% assign img_url = product.featured_media | img_url: '400x400', crop: 'center' %}
          {% assign img_url_2x = product.featured_media | img_url: '800x800', crop: 'center' %}
          {% assign img_url_3x = product.featured_media | img_url: '1200x1200', crop: 'center' %}
          <img
            src="{{ img_url }}"
            srcset="{{ img_url }} 1x, {{ img_url_2x }} 2x, {{ img_url_3x }} 3x"
            alt="{{ product.featured_media.alt | escape }}"
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
            fetchpriority="{% if forloop.index <= 2 %}high{% else %}low{% endif %}"
          >
        {% else %}
          {{ 'product-1' | placeholder_svg_tag: 'product-card-styled__image placeholder-svg' }}
        {% endif %}

        {% assign thirty_days_ago = 'now' | date: '%s' | minus: 2592000 %}
        {% assign product_date = product.created_at | date: '%s' | plus: 0 %}
        {% if product.metafields.custom.is_new or product.tags contains 'new' or product_date > thirty_days_ago %}
          <div class="product-card-styled__tag product-card-styled__tag--new">
            NEW
          </div>
        {% endif %}

        {% if product.compare_at_price > product.price %}
          <div class="product-card-styled__tag product-card-styled__tag--sale">
            SALE
          </div>
        {% endif %}

        {% if product.metafields.reviews.rating %}
          <div class="product-card-styled__rating">
            <span class="product-card-styled__rating-stars">{{ product.metafields.reviews.rating | round: 1 }} ★</span>
            <span class="product-card-styled__rating-count">| {{ product.metafields.reviews.rating_count }}</span>
          </div>
        {% endif %}
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="{{ product.url | within: collection }}" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">{{ product.title }}</h3>
    </a>

    {% if product.description != blank %}
      <div class="product-card-styled__description">
        {{ product.description | strip_html | truncatewords: 15 }}
      </div>
    {% endif %}

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        {% if product.price_varies %}
          <span class="product-card-styled__price-from">{{ 'products.product.from_text' | t }}</span>
        {% endif %}
        Rs. {{ product.price | divided_by: 100 }}
      </span>

      {% if product.compare_at_price > product.price %}
        <span class="product-card-styled__price-compare">{{ product.compare_at_price | money }}</span>
      {% endif %}
    </div>

    <div class="product-card-styled__actions">
      {% if product.available %}
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      {% else %}
        <button type="button" class="button button--disabled product-card-styled__sold-out" disabled>
          SOLD OUT
        </button>
      {% endif %}
    </div>
  </div>
</div>
