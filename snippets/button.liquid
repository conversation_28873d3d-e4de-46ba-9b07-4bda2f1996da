{% comment %}
  Button Snippet
  
  Usage:
  {% render 'button', 
    text: 'Shop Now', 
    url: '/collections/all', 
    style: '', 
    size: '', 
    with_arrow: true,
    class: '',
    id: '',
    aria_label: ''
  %}
  
  Parameters:
  - text: Button text (required)
  - url: Button URL (required for link buttons)
  - style: Button style (optional, default: primary)
    - 'secondary': Outlined button
  - size: Button size (optional, default: regular)
    - 'small': Small button
    - 'large': Large button
  - with_arrow: Show arrow icon (optional, default: false)
  - class: Additional CSS classes (optional)
  - id: Button ID (optional)
  - aria_label: Accessible label (optional)
{% endcomment %}

{% assign button_class = 'button' %}

{% if style == 'secondary' %}
  {% assign button_class = button_class | append: ' button--secondary' %}
{% endif %}

{% if size == 'small' %}
  {% assign button_class = button_class | append: ' button--small' %}
{% elsif size == 'large' %}
  {% assign button_class = button_class | append: ' button--large' %}
{% endif %}

{% if with_arrow %}
  {% assign button_class = button_class | append: ' button--with-icon' %}
{% endif %}

{% if class != blank %}
  {% assign button_class = button_class | append: ' ' | append: class %}
{% endif %}

{% if url != blank %}
  <a href="{{ url }}" 
     class="{{ button_class }}" 
     {% if id != blank %}id="{{ id }}"{% endif %}
     {% if aria_label != blank %}aria-label="{{ aria_label }}"{% endif %}>
    {% if with_arrow %}
      {% render 'icon-arrow-right-circle' %}
    {% endif %}
    <span>{{ text }}</span>
  </a>
{% else %}
  <button type="button" 
          class="{{ button_class }}" 
          {% if id != blank %}id="{{ id }}"{% endif %}
          {% if aria_label != blank %}aria-label="{{ aria_label }}"{% endif %}>
    {% if with_arrow %}
      {% render 'icon-arrow-right-circle' %}
    {% endif %}
    <span>{{ text }}</span>
  </button>
{% endif %}
