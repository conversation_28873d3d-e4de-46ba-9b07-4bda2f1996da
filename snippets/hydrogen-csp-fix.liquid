{% comment %}
  This snippet provides a fix for Shopify Hydrogen CSP issues
  Include this snippet in the head section of your theme
{% endcomment %}

<script>
  // Hydrogen CSP Fix - Executed immediately
  (function() {
    // Function to fix Hydrogen CSP headers
    function fixHydrogenCSP() {
      // Find all meta tags
      var metaTags = document.querySelectorAll('meta');
      
      metaTags.forEach(function(tag) {
        // Check if it's a CSP meta tag
        if (tag.getAttribute('http-equiv') && 
            tag.getAttribute('http-equiv').toLowerCase() === 'content-security-policy') {
          
          var content = tag.getAttribute('content');
          
          // Check if content contains problematic characters
          if (content && (content.includes('*') || content.includes(','))) {
            // Remove the tag to prevent errors
            if (tag.parentNode) {
              tag.parentNode.removeChild(tag);
            }
            
            // Log for debugging
            console.log('Removed problematic CSP meta tag');
          }
        }
      });
    }
    
    // Run immediately
    fixHydrogenCSP();
    
    // Run again when DOM is loaded
    document.addEventListener('DOMContentLoaded', fixHydrogenCSP);
    
    // Set up a MutationObserver to catch dynamically added meta tags
    if (typeof MutationObserver !== 'undefined') {
      var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList' && mutation.addedNodes.length) {
            fixHydrogenCSP();
          }
        });
      });
      
      // Start observing the document head
      if (document.head) {
        observer.observe(document.head, {
          childList: true,
          subtree: true
        });
      }
    }
  })();
</script>
