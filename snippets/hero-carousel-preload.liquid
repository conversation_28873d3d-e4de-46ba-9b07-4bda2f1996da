{% comment %}
  Hero Carousel Preload Snippet - Simple and Reliable

  This snippet preloads hero carousel images for both mobile and desktop:
  1. Preloads the first slide's images with high priority
  2. Uses device-specific media queries to load appropriate images
  3. Keeps the implementation simple and reliable
{% endcomment %}

{% if template.name == 'index' %}
  {% comment %}
    First, check if there's a hero carousel section on the page
  {% endcomment %}
  {% assign hero_carousel_section = false %}
  {% assign hero_carousel_blocks = false %}

  {% for section in sections %}
    {% if section.type == 'hero-carousel' %}
      {% assign hero_carousel_section = section %}
      {% assign hero_carousel_blocks = section.blocks %}
      {% break %}
    {% endif %}
  {% endfor %}

  {% comment %}
    If we found a hero carousel section with blocks, preload the images
  {% endcomment %}
  {% if hero_carousel_section and hero_carousel_blocks.size > 0 %}

    {% comment %}
      Preload the first slide's images for different device sizes
    {% endcomment %}
    {% assign first_block = hero_carousel_blocks[0] %}

    {% if first_block.settings.image != blank %}
      {% comment %}
        Mobile image (if available)
      {% endcomment %}
      {% if first_block.settings.image_mobile != blank %}
        <link rel="preload"
              as="image"
              href="{{ first_block.settings.image_mobile | img_url: '750x', crop: 'center' }}"
              media="(max-width: 749px)"
              fetchpriority="high">
      {% else %}
        {% comment %}
          If no mobile image, use desktop image for mobile
        {% endcomment %}
        <link rel="preload"
              as="image"
              href="{{ first_block.settings.image | img_url: '750x', crop: 'center' }}"
              media="(max-width: 749px)"
              fetchpriority="high">
      {% endif %}

      {% comment %}
        Desktop image
      {% endcomment %}
      <link rel="preload"
            as="image"
            href="{{ first_block.settings.image | img_url: '1500x', crop: 'center' }}"
            media="(min-width: 750px)"
            fetchpriority="high">
    {% endif %}
  {% endif %}
{% endif %}
