{% comment %}
  Mega Menu Dropdown Snippet
  - Displays a dropdown menu with links and optional featured content
  - Used for main navigation items that have child links
  
  Usage:
  {% render 'mega-menu-dropdown', link: link %}
{% endcomment %}

{% if link.links.size > 0 %}
  <div class="mega-menu" data-dropdown-menu>
    <div class="mega-menu__inner">
      <div class="mega-menu__links">
        <ul class="mega-menu__list">
          {% for child_link in link.links %}
            <li class="mega-menu__item">
              <a href="{{ child_link.url }}" class="mega-menu__link">
                {{ child_link.title }}
              </a>
            </li>
          {% endfor %}
        </ul>
      </div>
    </div>
  </div>
{% endif %}
