{%- comment -%}
  Pagination Snippet

  Usage:
  {% render 'pagination', paginate: paginate %}

  Parameters:
  - paginate: Paginate object from the current page
{%- endcomment -%}

{%- if paginate.pages > 1 -%}
  <nav class="pagination" role="navigation" aria-label="{{ 'general.pagination.label' | t }}">
    <ul class="pagination__list">
      {%- if paginate.previous -%}
        <li class="pagination__item pagination__item--prev">
          <a href="{{ paginate.previous.url }}" class="pagination__link" aria-label="{{ 'general.pagination.previous' | t }}">
            <span class="icon icon-chevron-left" aria-hidden="true"></span>
            <span>{{ 'general.pagination.previous' | t }}</span>
          </a>
        </li>
      {%- else -%}
        <li class="pagination__item pagination__item--prev pagination__item--disabled">
          <span class="pagination__link" aria-disabled="true">
            <span class="icon icon-chevron-left" aria-hidden="true"></span>
            <span>{{ 'general.pagination.previous' | t }}</span>
          </span>
        </li>
      {%- endif -%}

      {%- for part in paginate.parts -%}
        {%- if part.is_link -%}
          <li class="pagination__item">
            <a href="{{ part.url }}" class="pagination__link" aria-label="{{ 'general.pagination.page' | t: number: part.title }}">
              {{ part.title }}
            </a>
          </li>
        {%- elsif part.title == paginate.current_page -%}
          <li class="pagination__item">
            <span class="pagination__link pagination__link--current" aria-current="page" aria-label="{{ 'general.pagination.page' | t: number: part.title }}">
              {{ part.title }}
            </span>
          </li>
        {%- else -%}
          <li class="pagination__item pagination__item--ellipsis">
            <span class="pagination__link" aria-hidden="true">…</span>
          </li>
        {%- endif -%}
      {%- endfor -%}

      {%- if paginate.next -%}
        <li class="pagination__item pagination__item--next">
          <a href="{{ paginate.next.url }}" class="pagination__link" aria-label="{{ 'general.pagination.next' | t }}">
            <span>{{ 'general.pagination.next' | t }}</span>
            <span class="icon icon-chevron-right" aria-hidden="true"></span>
          </a>
        </li>
      {%- else -%}
        <li class="pagination__item pagination__item--next pagination__item--disabled">
          <span class="pagination__link" aria-disabled="true">
            <span>{{ 'general.pagination.next' | t }}</span>
            <span class="icon icon-chevron-right" aria-hidden="true"></span>
          </span>
        </li>
      {%- endif -%}
    </ul>
  </nav>
{%- endif -%}

<style>
  .pagination {
    margin: var(--spacing-medium) 0;
  }

  .pagination__list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .pagination__item {
    margin: 0 2px;
  }

  .pagination__link {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    padding: 0 10px;
    border-radius: var(--border-radius);
    border: 1px solid var(--color-border);
    text-decoration: none;
    color: var(--color-text);
    transition: all 0.2s ease;
  }

  .pagination__link:hover:not(.pagination__link--current, [aria-disabled="true"]) {
    background-color: var(--color-background-light);
    border-color: var(--color-primary);
  }

  .pagination__link--current {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-primary-contrast);
  }

  .pagination__item--disabled .pagination__link {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pagination__item--prev .pagination__link,
  .pagination__item--next .pagination__link {
    padding: 0 15px;
  }

  .pagination__item--prev .icon {
    margin-right: 5px;
  }

  .pagination__item--next .icon {
    margin-left: 5px;
  }

  .pagination__item--ellipsis .pagination__link {
    border: none;
  }

  @media screen and (max-width: 749px) {
    .pagination__item:not(.pagination__item--prev, .pagination__item--next, .pagination__item--ellipsis) {
      display: none;
    }

    .pagination__item--ellipsis {
      display: none;
    }

    .pagination__item:nth-child(2),
    .pagination__item:nth-last-child(2) {
      display: block;
    }

    .pagination__item:nth-child(3):not(.pagination__item--ellipsis) {
      display: block;
    }

    .pagination__item:nth-last-child(3):not(.pagination__item--ellipsis) {
      display: block;
    }

    .pagination__item--current {
      display: block !important;
    }
  }
</style>
