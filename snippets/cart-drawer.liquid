<!-- Cart Drawer -->
<div id="cart-drawer" class="cart-drawer">
  <div class="cart-drawer__header">
    <h2 class="cart-drawer__title">Your Cart</h2>
    <button class="cart-drawer__close" aria-label="Close cart">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>
  <div class="cart-drawer__content">
    <div class="cart-drawer__empty-state">
      <p>Your cart is empty</p>
      <a href="/collections/all" class="button">Continue Shopping</a>
    </div>
    <div class="cart-drawer__items">
      <!-- Cart items will be loaded here -->
    </div>
  </div>
  <div class="cart-drawer__footer">
    <div class="cart-drawer__subtotal">
      <span>Subtotal</span>
      <span class="cart-drawer__subtotal-price" data-price-cents="{{ cart.total_price }}">{{ cart.total_price | money }}</span>
    </div>
    <div class="cart-drawer__buttons">
      <a href="/cart" class="button button--secondary">View Cart</a>
      <a href="/checkout" class="button">Checkout</a>
    </div>
  </div>
</div>
<div id="cart-drawer-overlay" class="cart-drawer-overlay"></div>

<style>
  /* Cart Drawer Styles */
  .cart-drawer {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    max-width: 100%;
    height: 100%;
    background-color: var(--color-background);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: var(--z-index-modal);
    transition: right var(--transition-duration) var(--transition-timing);
    display: flex;
    flex-direction: column;
  }

  .cart-drawer.active {
    right: 0;
  }

  /* Loading state */
  .cart-drawer.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cart-drawer.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border-radius: 50%;
    border: 2px solid var(--color-border);
    border-top-color: var(--color-primary);
    animation: cart-spinner 0.6s linear infinite;
    z-index: 11;
  }

  @keyframes cart-spinner {
    to {
      transform: rotate(360deg);
    }
  }

  /* Error message */
  .cart-drawer__error {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 12;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .cart-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-index-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-duration) var(--transition-timing),
                visibility var(--transition-duration) var(--transition-timing);
  }

  .cart-drawer-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  .cart-drawer__header {
    padding: var(--spacing-base);
    border-bottom: var(--border-width) solid var(--color-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .cart-drawer__title {
    margin: 0;
    font-size: var(--font-size-large);
    font-weight: var(--font-weight-semibold);
  }

  .cart-drawer__close {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: var(--color-text);
  }

  .cart-drawer__content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-base);
  }

  .cart-drawer__empty-state {
    text-align: center;
    padding: var(--spacing-large) 0;
  }

  .cart-drawer__empty-state p {
    margin-bottom: var(--spacing-base);
    color: var(--color-text-light);
  }

  .cart-drawer__items {
    display: none;
  }

  .cart-drawer__item {
    display: flex;
    margin-bottom: var(--spacing-base);
    padding-bottom: var(--spacing-base);
    border-bottom: var(--border-width) solid var(--color-border);
  }

  .cart-drawer__item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-right: var(--spacing-base);
  }

  .cart-drawer__item-details {
    flex: 1;
  }

  .cart-drawer__item-title {
    font-size: var(--font-size-base);
    margin: 0 0 var(--spacing-small);
    font-weight: var(--font-weight-medium);
  }

  .cart-drawer__item-variant {
    font-size: var(--font-size-small);
    color: var(--color-text-light);
    margin-bottom: var(--spacing-small);
  }

  .cart-drawer__item-price {
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-small);
  }

  .cart-drawer__item-quantity {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-small);
  }

  .cart-drawer__item-quantity-button {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-background-light);
    border: var(--border-width) solid var(--color-border);
    cursor: pointer;
  }

  .cart-drawer__item-quantity-input {
    width: 40px;
    height: 24px;
    text-align: center;
    border: var(--border-width) solid var(--color-border);
    margin: 0 var(--spacing-extra-small);
  }

  .cart-drawer__item-remove {
    background: none;
    border: none;
    color: var(--color-text-light);
    text-decoration: underline;
    cursor: pointer;
    font-size: var(--font-size-small);
    padding: 0;
    margin-top: var(--spacing-small);
  }

  .cart-drawer__footer {
    padding: var(--spacing-base);
    border-top: var(--border-width) solid var(--color-border);
  }

  .cart-drawer__subtotal {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-base);
    font-weight: var(--font-weight-semibold);
  }

  .cart-drawer__buttons {
    display: flex;
    gap: var(--spacing-small);
  }

  .cart-drawer__buttons .button {
    flex: 1;
    text-align: center;
  }

  @media screen and (max-width: var(--breakpoint-small)) {
    .cart-drawer {
      width: 100%;
    }
  }
</style>
