{% comment %}
  Simple Hero Carousel Initialization for Mobile
  This snippet provides a basic initialization script for the hero carousel on mobile
{% endcomment %}

<script>
  // Immediately-invoked function to initialize the hero carousel on mobile
  (function() {
    // Check if we're on mobile
    const isMobile = window.matchMedia('(max-width: 749px)').matches;
    if (!isMobile) return; // Only run on mobile

    // Execute immediately without waiting for DOMContentLoaded
    const carousel = document.querySelector('.hero-carousel');
    if (!carousel) return;

    const slides = carousel.querySelectorAll('.hero-carousel__slide');
    if (slides.length <= 1) return;

    // Mark the first slide as active and ensure all others are inactive
    slides.forEach((slide, index) => {
      if (index === 0) {
        slide.classList.add('active');
      } else {
        slide.classList.remove('active');
      }
    });

    // Ensure first slide images are loaded with high priority
    const firstSlide = slides[0];
    if (firstSlide) {
      // Find all images in the first slide
      const images = firstSlide.querySelectorAll('img');
      images.forEach(img => {
        img.setAttribute('loading', 'eager');
        img.setAttribute('fetchpriority', 'high');
      });
    }

    // Simple touch handling for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    function handleSwipe() {
      const swipeThreshold = 50; // Minimum distance for a swipe
      const swipeDistance = touchEndX - touchStartX;

      if (Math.abs(swipeDistance) < swipeThreshold) return;

      let activeIndex = 0;
      slides.forEach((slide, index) => {
        if (slide.classList.contains('active')) {
          activeIndex = index;
        }
      });

      // Calculate next slide index based on swipe direction
      let nextIndex;
      if (swipeDistance > 0) {
        // Swipe right - go to previous slide
        nextIndex = (activeIndex - 1 + slides.length) % slides.length;
      } else {
        // Swipe left - go to next slide
        nextIndex = (activeIndex + 1) % slides.length;
      }

      // Update slides
      slides[activeIndex].classList.remove('active');
      slides[nextIndex].classList.add('active');
    }

    // Add touch event listeners
    carousel.addEventListener('touchstart', function(e) {
      touchStartX = e.changedTouches[0].screenX;
    }, { passive: true });

    carousel.addEventListener('touchend', function(e) {
      touchEndX = e.changedTouches[0].screenX;
      handleSwipe();
    }, { passive: true });

    // Simple autoplay for mobile
    if (carousel.getAttribute('data-autoplay') === 'true') {
      const interval = parseInt(carousel.getAttribute('data-autoplay-interval'), 10) || 5000;

      setInterval(function() {
        let activeIndex = 0;
        slides.forEach((slide, index) => {
          if (slide.classList.contains('active')) {
            activeIndex = index;
          }
        });

        const nextIndex = (activeIndex + 1) % slides.length;

        // Update slides
        slides[activeIndex].classList.remove('active');
        slides[nextIndex].classList.add('active');
      }, interval);
    }
  })();
</script>
