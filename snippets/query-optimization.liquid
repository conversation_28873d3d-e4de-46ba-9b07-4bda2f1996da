{% comment %}
  Query Optimization Snippet
  
  This snippet implements techniques to reduce and optimize database queries.
  It uses pagination, limits, and other techniques to reduce the amount of data fetched.
{% endcomment %}

{% comment %}
  Optimized collection product loading
  Instead of loading all products at once, we load a limited number initially
  and then load more as needed via AJAX
{% endcomment %}
{% if template contains 'collection' %}
  <script>
    // Store collection pagination state
    const paginationState = {
      currentPage: {{ paginate.current_page | default: 1 }},
      totalPages: {{ paginate.pages | default: 1 }},
      loading: false,
      complete: {{ paginate.current_page | default: 1 }} >= {{ paginate.pages | default: 1 }}
    };
    
    // Function to load more products
    function loadMoreProducts() {
      if (paginationState.loading || paginationState.complete) {
        return;
      }
      
      const nextPage = paginationState.currentPage + 1;
      const collectionUrl = window.location.pathname;
      const loadMoreUrl = `${collectionUrl}?page=${nextPage}&view=ajax`;
      
      paginationState.loading = true;
      
      // Show loading indicator
      const loadingIndicator = document.querySelector('.collection-loading');
      if (loadingIndicator) {
        loadingIndicator.style.display = 'block';
      }
      
      fetch(loadMoreUrl)
        .then(response => response.text())
        .then(html => {
          // Parse the HTML response
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          const productItems = doc.querySelectorAll('.product-item');
          
          // Append new products to the grid
          const productGrid = document.querySelector('.collection-grid');
          if (productGrid && productItems.length > 0) {
            productItems.forEach(item => {
              productGrid.appendChild(document.importNode(item, true));
            });
            
            // Update pagination state
            paginationState.currentPage = nextPage;
            paginationState.complete = nextPage >= paginationState.totalPages;
            
            // Initialize any product features on the new items
            if (window.initProductFeatures) {
              window.initProductFeatures();
            }
          } else {
            // No more products or error
            paginationState.complete = true;
          }
        })
        .catch(error => {
          console.error('Error loading more products:', error);
        })
        .finally(() => {
          paginationState.loading = false;
          
          // Hide loading indicator
          if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
          }
          
          // Hide load more button if we're done
          const loadMoreButton = document.querySelector('.load-more-button');
          if (loadMoreButton && paginationState.complete) {
            loadMoreButton.style.display = 'none';
          }
        });
    }
    
    // Set up infinite scroll or load more button
    document.addEventListener('DOMContentLoaded', function() {
      const loadMoreButton = document.querySelector('.load-more-button');
      if (loadMoreButton) {
        loadMoreButton.addEventListener('click', loadMoreProducts);
      }
      
      // Optional: Implement infinite scroll
      if ({{ settings.enable_infinite_scroll | default: false }}) {
        const collectionGrid = document.querySelector('.collection-grid');
        if (collectionGrid) {
          const observer = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting && !paginationState.loading && !paginationState.complete) {
              loadMoreProducts();
            }
          }, { rootMargin: '200px' });
          
          observer.observe(document.querySelector('.collection-footer'));
        }
      }
    });
  </script>
{% endif %}

{% comment %}
  Optimized related products loading
  Load related products asynchronously after the main product is loaded
{% endcomment %}
{% if template contains 'product' %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const relatedProductsContainer = document.querySelector('.related-products');
      if (relatedProductsContainer) {
        // Set a placeholder initially
        relatedProductsContainer.innerHTML = '<div class="loading-placeholder">Loading related products...</div>';
        
        // Load related products asynchronously
        const productId = {{ product.id | default: 0 }};
        const relatedProductsUrl = `/recommendations/products.json?product_id=${productId}&limit=4`;
        
        fetch(relatedProductsUrl)
          .then(response => response.json())
          .then(data => {
            if (data.products && data.products.length > 0) {
              // Clear the placeholder
              relatedProductsContainer.innerHTML = '';
              
              // Create HTML for each related product
              data.products.forEach(product => {
                const productHtml = `
                  <div class="related-product">
                    <a href="/products/${product.handle}" class="related-product__link">
                      <img src="${product.featured_image}" alt="${product.title}" class="related-product__image" loading="lazy" width="200" height="200">
                      <h3 class="related-product__title">${product.title}</h3>
                      <p class="related-product__price" data-price-cents="${product.price}"></p>
                    </a>
                  </div>
                `;
                relatedProductsContainer.insertAdjacentHTML('beforeend', productHtml);
              });
              
              // Format prices
              if (window.formatMoney) {
                const priceElements = relatedProductsContainer.querySelectorAll('[data-price-cents]');
                priceElements.forEach(element => {
                  const cents = element.getAttribute('data-price-cents');
                  if (cents) {
                    element.innerHTML = formatMoney(cents);
                  }
                });
              }
            } else {
              relatedProductsContainer.innerHTML = '<p>No related products found</p>';
            }
          })
          .catch(error => {
            console.error('Error loading related products:', error);
            relatedProductsContainer.innerHTML = '<p>Unable to load related products</p>';
          });
      }
    });
  </script>
{% endif %}
