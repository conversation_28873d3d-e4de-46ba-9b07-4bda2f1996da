{% comment %}
  Product Page JavaScript Optimization
  
  This snippet implements optimized JavaScript loading for product pages:
  1. Loads only essential product functionality immediately
  2. Defers non-critical product features until after page load
  3. Uses conditional loading based on visible elements
{% endcomment %}

{% if template contains 'product' %}
<script>
  // Check if we're on mobile
  const isMobile = window.matchMedia('(max-width: 749px)').matches;
  
  document.addEventListener('DOMContentLoaded', function() {
    // Essential product functionality - load immediately
    const productForm = document.querySelector('.product-form');
    if (productForm) {
      // Load variant selector functionality
      if (document.querySelector('.product-form__select')) {
        // On mobile, defer variant selector until user interacts with the form
        if (isMobile) {
          let variantSelectorLoaded = false;
          
          productForm.addEventListener('click', function loadVariantSelector() {
            if (!variantSelectorLoaded) {
              variantSelectorLoaded = true;
              
              // Load variant selector script
              const script = document.createElement('script');
              script.src = '{{ "product-variants.min.js" | asset_url }}';
              document.head.appendChild(script);
              
              // Remove event listener after loading
              productForm.removeEventListener('click', loadVariantSelector);
            }
          });
        } else {
          // On desktop, load immediately but with defer
          const script = document.createElement('script');
          script.src = '{{ "product-variants.min.js" | asset_url }}';
          script.defer = true;
          document.head.appendChild(script);
        }
      }
      
      // Load quantity adjuster functionality
      if (document.querySelector('.quantity-input')) {
        // This is lightweight, so load it on all devices
        const script = document.createElement('script');
        script.src = '{{ "quantity-adjuster.min.js" | asset_url }}';
        script.defer = true;
        document.head.appendChild(script);
      }
    }
    
    // Product gallery - load based on visibility
    const productGallery = document.querySelector('.product-gallery');
    if (productGallery) {
      if (isMobile) {
        // On mobile, use Intersection Observer to load gallery script when it's about to be visible
        if ('IntersectionObserver' in window) {
          const observer = new IntersectionObserver(function(entries) {
            if (entries[0].isIntersecting) {
              const script = document.createElement('script');
              script.src = '{{ "product-gallery.min.js" | asset_url }}';
              document.head.appendChild(script);
              observer.disconnect();
            }
          }, {rootMargin: '200px'}); // Load when within 200px of viewport
          
          observer.observe(productGallery);
        } else {
          // Fallback for browsers without IntersectionObserver
          window.addEventListener('load', function() {
            setTimeout(function() {
              const script = document.createElement('script');
              script.src = '{{ "product-gallery.min.js" | asset_url }}';
              document.head.appendChild(script);
            }, 1000); // 1 second delay after load
          });
        }
      } else {
        // On desktop, load with defer
        const script = document.createElement('script');
        script.src = '{{ "product-gallery.min.js" | asset_url }}';
        script.defer = true;
        document.head.appendChild(script);
      }
    }
    
    // Product recommendations - load when user scrolls near them
    const productRecommendations = document.querySelector('.product-recommendations');
    if (productRecommendations) {
      if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver(function(entries) {
          if (entries[0].isIntersecting) {
            // Queue the script load using our interaction-based loader
            if (window.queueScriptLoad) {
              window.queueScriptLoad('{{ "product-recommendations.min.js" | asset_url }}', 'low');
            } else {
              // Fallback if interaction loader isn't available
              const script = document.createElement('script');
              script.src = '{{ "product-recommendations.min.js" | asset_url }}';
              script.async = true;
              document.head.appendChild(script);
            }
            observer.disconnect();
          }
        }, {rootMargin: '400px'}); // Load when within 400px of viewport
        
        observer.observe(productRecommendations);
      } else {
        // Fallback for browsers without IntersectionObserver
        window.addEventListener('load', function() {
          setTimeout(function() {
            const script = document.createElement('script');
            script.src = '{{ "product-recommendations.min.js" | asset_url }}';
            script.async = true;
            document.head.appendChild(script);
          }, 2000); // 2 second delay after load
        });
      }
    }
  });
</script>
{% endif %}
