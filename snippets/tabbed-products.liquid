{%- comment -%}
  Tabbed Products Snippet

  Usage:
  {% render 'tabbed-products',
    heading: 'Shop by Category',
    collection_handles: 'collection-1,collection-2,collection-3',
    products_per_row: section.settings.products_per_row,
    products_limit: section.settings.products_limit,
    show_view_all: section.settings.show_view_all,
    unique_id: section.id
  %}

  Parameters:
  - heading: Heading text above the tabs (optional)
  - collection_handles: Comma-separated list of collection handles
  - products_per_row: Number of products to show per row (default: 4)
  - products_limit: Maximum number of products to show per tab (default: 8)
  - show_view_all: Show 'View all' button for each collection (default: true)
  - unique_id: Unique identifier for the tabs (required for multiple instances on one page)
{%- endcomment -%}

{%- liquid
  assign unique_id = unique_id | default: 'tabbed-products'
  assign products_per_row = products_per_row | default: 4
  assign products_per_row_tablet = products_per_row_tablet | default: 2
  assign products_per_row_mobile = products_per_row_mobile | default: 1
  assign products_limit = products_limit | default: 8
  assign show_view_all = show_view_all | default: true

  # Tab style settings
  assign tab_text_color = tab_text_color | default: '#333333'
  assign tab_border_color = tab_border_color | default: '#e8e8e8'
  assign tab_hover_background = tab_hover_background | default: '#f5f5f5'
  assign tab_hover_text_color = tab_hover_text_color | default: '#000000'
  assign tab_active_background = tab_active_background | default: '#000000'
  assign tab_active_text_color = tab_active_text_color | default: '#ffffff'

  # Process collection_handles parameter
  assign has_collections = false
  assign collections_array = nil
  assign single_collection = nil

  # If collection_handles is provided, split it into an array
  if collection_handles != blank
    assign collection_handles_array = collection_handles | split: ','
    if collection_handles_array.size > 0
      assign has_collections = true
      assign collections_array = collection_handles_array
    endif
  endif
-%}

{{ 'product-card-styled.css' | asset_url | stylesheet_tag }}

<div class="tabbed-products" data-tabbed-products-id="{{ unique_id }}">
  <div class="tabbed-products__header">
    <div class="tabbed-products__heading-container">
      {% if heading_first_word != blank and heading_second_word != blank %}
        {% render 'section-heading',
          first_word: heading_first_word,
          second_word: heading_second_word,
          outline_color: heading_outline_color,
          filled_color: heading_filled_color
        %}
      {% elsif heading != blank %}
        <div class="section-header">
          <h2 class="section-title">{{ heading }}</h2>
        </div>
      {% endif %}
    </div>

    {% if show_view_all and collection_handles.size > 0 %}
      {% assign first_collection = collections[collection_handles.first] %}
      <div class="tabbed-products__view-all-container">
        <a href="{{ first_collection.url }}" class="tabbed-products__view-all-button button button--outline">
          View All
        </a>
      </div>
    {% endif %}
  </div>

  {% if has_collections %}
    <div class="tabbed-products__tabs-container">
      <div class="tabbed-products__tabs" role="tablist">
        {% if collections_array != nil and collections_array.size > 0 %}
          {% for collection_item in collections_array %}
            {%- liquid
              # The collection_item is now a direct handle string
              assign collection_handle = collection_item
              assign collection_object = collections[collection_handle]
            -%}

            {% if collection_object and collection_object.products.size > 0 %}
              <button
                id="tab-{{ unique_id }}-{{ forloop.index }}"
                class="tabbed-products__tab{% if forloop.first %} tabbed-products__tab--active{% endif %}"
                role="tab"
                aria-selected="{% if forloop.first %}true{% else %}false{% endif %}"
                aria-controls="tabpanel-{{ unique_id }}-{{ forloop.index }}"
                data-collection="{{ collection_handle }}"
              >
                {{ collection_object.title }}
              </button>
            {% endif %}
          {% endfor %}
        {% elsif single_collection != blank %}
          {% assign collection_object = collections[single_collection] %}
          {% if collection_object and collection_object.products.size > 0 %}
            <button
              id="tab-{{ unique_id }}-1"
              class="tabbed-products__tab tabbed-products__tab--active"
              role="tab"
              aria-selected="true"
              aria-controls="tabpanel-{{ unique_id }}-1"
              data-collection="{{ collection_object.handle }}"
            >
              {{ collection_object.title }}
            </button>
          {% endif %}
        {% else %}
          {% assign collection_object = collections.first %}
          {% if collection_object and collection_object.products.size > 0 %}
            <button
              id="tab-{{ unique_id }}-1"
              class="tabbed-products__tab tabbed-products__tab--active"
              role="tab"
              aria-selected="true"
              aria-controls="tabpanel-{{ unique_id }}-1"
              data-collection="{{ collection_object.handle }}"
            >
              {{ collection_object.title }}
            </button>
          {% endif %}
        {% endif %}
      </div>
    </div>

    <div class="tabbed-products__content">
      {% if collections_array != nil and collections_array.size > 0 %}
        {% for collection_item in collections_array %}
          {%- liquid
            # The collection_item is now a direct handle string
            assign collection_handle = collection_item
            assign collection_object = collections[collection_handle]
          -%}

          {% if collection_object and collection_object.products.size > 0 %}
            <div
              id="tabpanel-{{ unique_id }}-{{ forloop.index }}"
              class="tabbed-products__panel{% if forloop.first %} tabbed-products__panel--active{% endif %}"
              role="tabpanel"
              aria-labelledby="tab-{{ unique_id }}-{{ forloop.index }}"
              {% unless forloop.first %}hidden{% endunless %}
            >
              {% render 'product-grid-styled',
                collection: collection_object,
                products_per_row: products_per_row,
                products_per_row_tablet: products_per_row_tablet,
                products_per_row_mobile: products_per_row_mobile,
                products_limit: products_limit
              %}

              {% comment %}View all button moved to header{% endcomment %}
            </div>
          {% endif %}
        {% endfor %}
      {% else %}
        {% if single_collection != blank %}
          {% assign collection_object = collections[single_collection] %}
        {% else %}
          {% assign collection_object = collections.first %}
        {% endif %}

        {% if collection_object and collection_object.products.size > 0 %}
          <div
            id="tabpanel-{{ unique_id }}-1"
            class="tabbed-products__panel tabbed-products__panel--active"
            role="tabpanel"
            aria-labelledby="tab-{{ unique_id }}-1"
          >
            {% render 'product-grid-styled',
              collection: collection_object,
              products_per_row: products_per_row,
              products_per_row_tablet: products_per_row_tablet,
              products_per_row_mobile: products_per_row_mobile,
              products_limit: products_limit
            %}

            {% comment %}View all button moved to header{% endcomment %}
          </div>
        {% endif %}
      {% endif %}
    </div>
  {% else %}
    {% comment %}
      If no collections are selected, try to use the first few collections from the store as a fallback
    {% endcomment %}
    {% if collections.size > 0 %}
      <div class="tabbed-products__tabs-container">
        <div class="tabbed-products__tabs" role="tablist">
          {% for collection in collections limit: 5 %}
            {% if collection.products.size > 0 %}
              <button
                id="tab-{{ unique_id }}-{{ forloop.index }}"
                class="tabbed-products__tab{% if forloop.first %} tabbed-products__tab--active{% endif %}"
                role="tab"
                aria-selected="{% if forloop.first %}true{% else %}false{% endif %}"
                aria-controls="tabpanel-{{ unique_id }}-{{ forloop.index }}"
                data-collection="{{ collection.handle }}"
              >
                {{ collection.title }}
              </button>
            {% endif %}
          {% endfor %}
        </div>
      </div>

      <div class="tabbed-products__content">
        {% for collection in collections limit: 5 %}
          {% if collection.products.size > 0 %}
            <div
              id="tabpanel-{{ unique_id }}-{{ forloop.index }}"
              class="tabbed-products__panel{% if forloop.first %} tabbed-products__panel--active{% endif %}"
              role="tabpanel"
              aria-labelledby="tab-{{ unique_id }}-{{ forloop.index }}"
              {% unless forloop.first %}hidden{% endunless %}
            >
              {% render 'product-grid-styled',
                collection: collection,
                products_per_row: products_per_row,
                products_per_row_tablet: products_per_row_tablet,
                products_per_row_mobile: products_per_row_mobile,
                products_limit: products_limit
              %}

              {% comment %}View all button moved to header{% endcomment %}
            </div>
          {% endif %}
        {% endfor %}
      </div>
    {% else %}
      <div class="tabbed-products__empty">
        <p>{{ 'homepage.onboarding.no_collections_selected' | t }}</p>
      </div>
    {% endif %}
  {% endif %}
</div>

{% comment %}
  Add styles for the tabbed products component
{% endcomment %}
<style>
  .tabbed-products {
    margin: var(--spacing-extra-loose) 0;
  }

  .tabbed-products__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-base);
  }

  .tabbed-products__heading-container {
    text-align: left;
  }

  .tabbed-products__view-all-container {
    margin-left: var(--spacing-base);
  }

  .tabbed-products__view-all-button {
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid currentColor;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
  }

  .tabbed-products__view-all-button:hover {
    background-color: var(--color-button);
    color: var(--color-button-text);
    border-color: var(--color-button);
  }

  /* Section header styles - consistent with other sections */
  .section-header {
    margin-bottom: var(--spacing-base);
  }

  .section-title {
    font-family: var(--font-heading);
    font-size: var(--font-size-h2);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-tight);
  }

  .text-center {
    text-align: center;
  }

  .tabbed-products__tabs-container {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-base);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .tabbed-products__tabs-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .tabbed-products__tabs {
    display: flex;
    flex-wrap: nowrap;
    gap: var(--spacing-tight);
    padding-bottom: var(--spacing-tight);
  }

  .tabbed-products__tab {
    padding: var(--spacing-tight) var(--spacing-base);
    background: transparent;
    border: 1px solid {{ tab_border_color }};
    border-radius: var(--border-radius);
    font-family: var(--font-body);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: {{ tab_text_color }};
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }

  .tabbed-products__tab:hover {
    background-color: {{ tab_hover_background }};
    color: {{ tab_hover_text_color }};
  }

  .tabbed-products__tab--active {
    background-color: {{ tab_active_background }};
    color: {{ tab_active_text_color }};
    border-color: {{ tab_active_background }};
  }

  .tabbed-products__panel {
    display: none;
  }

  .tabbed-products__panel--active {
    display: block;
  }

  /* Using product-grid-styled instead of tabbed-products__grid */

  .tabbed-products__view-all {
    margin-top: var(--spacing-loose);
    text-align: center;
  }

  .tabbed-products__view-all .button {
    background-color: var(--color-button);
    color: var(--color-button-text);
    font-family: var(--font-body);
    font-size: var(--font-size-small);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-tight) var(--spacing-base);
    border-radius: var(--border-radius);
    transition: background-color 0.2s ease;
  }

  .tabbed-products__view-all .button:hover {
    background-color: var(--color-primary);
  }

  .tabbed-products__empty {
    padding: var(--spacing-extra-loose);
    text-align: center;
    background-color: #f7f7f7;
    border-radius: var(--border-radius);
  }

  /* Media queries for grid handled by product-grid-styled */
</style>

{% comment %}
  Add JavaScript for the tabbed products component
{% endcomment %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    initTabbedProducts('{{ unique_id }}');
  });

  function initTabbedProducts(uniqueId) {
    const container = document.querySelector(`[data-tabbed-products-id="${uniqueId}"]`);
    if (!container) return;

    const tabs = container.querySelectorAll('.tabbed-products__tab');
    const panels = container.querySelectorAll('.tabbed-products__panel');

    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Deactivate all tabs and panels
        tabs.forEach(t => {
          t.classList.remove('tabbed-products__tab--active');
          t.setAttribute('aria-selected', 'false');
        });

        panels.forEach(p => {
          p.classList.remove('tabbed-products__panel--active');
          p.setAttribute('hidden', '');
        });

        // Activate clicked tab and its panel
        tab.classList.add('tabbed-products__tab--active');
        tab.setAttribute('aria-selected', 'true');

        const panelId = tab.getAttribute('aria-controls');
        const panel = container.querySelector(`#${panelId}`);

        if (panel) {
          panel.classList.add('tabbed-products__panel--active');
          panel.removeAttribute('hidden');

          // Update the View All button URL
          const collectionHandle = tab.getAttribute('data-collection');
          if (collectionHandle) {
            const viewAllButton = container.querySelector('.tabbed-products__view-all-button');
            if (viewAllButton) {
              viewAllButton.href = `/collections/${collectionHandle}`;
            }
          }
        }
      });
    });
  }
</script>
