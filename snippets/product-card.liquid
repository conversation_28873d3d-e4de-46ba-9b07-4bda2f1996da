{%- comment -%}
  Product Card Snippet

  Usage:
  {% render 'product-card',
    product: product,
    show_vendor: true,
    show_rating: true
  %}

  Parameters:
  - product: Product object to display
  - show_vendor: Whether to show the product vendor (default: false)
  - show_rating: Whether to show the product rating (default: false)
{%- endcomment -%}

{%- liquid
  assign show_vendor = show_vendor | default: false
  assign show_rating = show_rating | default: false
-%}

<div class="product-card">
  <a href="{{ product.url }}" class="product-card__link">
    <div class="product-card__image-wrapper">
      {% if product.featured_media %}
        <img
          src="{{ product.featured_media | img_url: '450x450', crop: 'center' }}"
          srcset="{{ product.featured_media | img_url: '450x450', crop: 'center' }} 1x, {{ product.featured_media | img_url: '900x900', crop: 'center' }} 2x"
          alt="{{ product.featured_media.alt | escape }}"
          loading="lazy"
          width="450"
          height="450"
          class="product-card__image"
        >
      {% else %}
        {{ 'product-1' | placeholder_svg_tag: 'product-card__image placeholder-svg' }}
      {% endif %}

      {% if product.compare_at_price > product.price %}
        <span class="product-card__badge">{{ 'products.product.on_sale' | t }}</span>
      {% endif %}
    </div>

    <div class="product-card__info">
      {% if show_vendor and product.vendor %}
        <p class="product-card__vendor">{{ product.vendor }}</p>
      {% endif %}

      <h3 class="product-card__title">{{ product.title }}</h3>

      {% comment %}
        Rating display is disabled to fix syntax errors
        To re-enable, implement a proper rating system with metafields
      {% endcomment %}
      {% if false and show_rating %}
        <div class="product-card__rating">
          <span class="product-card__rating-stars" aria-hidden="true">
            <span class="product-card__rating-star">★★★★★</span>
          </span>
          <span class="product-card__rating-count">(0)</span>
        </div>
      {% endif %}

      <div class="product-card__price">
        {% if product.compare_at_price > product.price %}
          <span class="product-card__price--sale">{{ product.price | money }}</span>
          <span class="product-card__price--compare">{{ product.compare_at_price | money }}</span>
        {% else %}
          <span class="product-card__price--regular">{{ product.price | money }}</span>
        {% endif %}
      </div>
    </div>
  </a>

  {% if product.available %}
    <form method="post" action="/cart/add" class="product-card__form" data-ajax-cart-form>
      <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
      <button type="submit" class="product-card__add-to-cart button button--small">
        {{ 'products.product.add_to_cart' | t }}
      </button>
    </form>
  {% else %}
    <button type="button" class="product-card__add-to-cart button button--small button--disabled" disabled>
      {{ 'products.product.sold_out' | t }}
    </button>
  {% endif %}
</div>

<style>
  .product-card {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .product-card__link {
    display: block;
    text-decoration: none;
    color: inherit;
    flex: 1;
  }

  .product-card__image-wrapper {
    position: relative;
    padding-bottom: 100%;
    overflow: hidden;
    background-color: var(--color-background-light);
  }

  .product-card__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .product-card:hover .product-card__image {
    transform: scale(1.05);
  }

  .product-card__badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    padding: 4px 8px;
    font-size: 12px;
    border-radius: var(--border-radius);
    z-index: 1;
  }

  .product-card__info {
    padding: var(--spacing-small);
    background-color: var(--color-background);
  }

  .product-card__vendor {
    font-size: 12px;
    color: var(--color-text-light);
    margin: 0 0 5px;
  }

  .product-card__title {
    font-size: 16px;
    margin: 0 0 10px;
    font-weight: 500;
  }

  .product-card__rating {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .product-card__rating-stars {
    color: var(--color-primary);
    letter-spacing: 2px;
    margin-right: 5px;
  }

  .product-card__rating-star--empty {
    opacity: 0.3;
  }

  .product-card__rating-count {
    font-size: 12px;
    color: var(--color-text-light);
  }

  .product-card__price {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
  }

  .product-card__price--regular {
    font-weight: 600;
  }

  .product-card__price--sale {
    font-weight: 600;
    color: var(--color-error);
  }

  .product-card__price--compare {
    text-decoration: line-through;
    color: var(--color-text-light);
    font-size: 14px;
  }

  .product-card__add-to-cart {
    width: 100%;
    margin-top: auto;
  }
</style>
