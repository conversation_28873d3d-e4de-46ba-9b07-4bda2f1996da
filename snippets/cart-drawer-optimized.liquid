{% comment %}
  Cart Drawer Optimization for Mobile
  
  This snippet implements optimized loading of cart drawer functionality:
  1. On mobile, loads cart drawer JavaScript only when user interacts with cart icon
  2. On desktop, loads cart drawer with defer
  3. Uses a lightweight placeholder until the full cart drawer is loaded
{% endcomment %}

<script>
  // Check if we're on mobile
  const isMobile = window.matchMedia('(max-width: 749px)').matches;
  
  document.addEventListener('DOMContentLoaded', function() {
    // Get cart icons
    const cartIcons = document.querySelectorAll('.cart-icon');
    if (cartIcons.length === 0) return;
    
    // Function to load cart drawer script
    function loadCartDrawerScript() {
      // Check if already loaded
      if (window.cartDrawerLoaded) return;
      window.cartDrawerLoaded = true;
      
      const script = document.createElement('script');
      script.src = '{{ "cart-drawer.min.js" | asset_url }}';
      document.head.appendChild(script);
    }
    
    if (isMobile) {
      // On mobile, load cart drawer only when user clicks cart icon
      cartIcons.forEach(icon => {
        icon.addEventListener('click', function(event) {
          // Prevent default action until script is loaded
          event.preventDefault();
          
          // Show loading indicator
          const cartCount = icon.querySelector('[data-cart-count]');
          if (cartCount) {
            cartCount.classList.add('loading');
          }
          
          // Load cart drawer script
          loadCartDrawerScript();
          
          // After script is loaded, it will handle the cart drawer opening
          // We'll add a small delay to ensure the script has time to initialize
          setTimeout(function() {
            // Remove loading indicator
            if (cartCount) {
              cartCount.classList.remove('loading');
            }
            
            // Trigger click again to open cart drawer
            if (window.openCartDrawer) {
              window.openCartDrawer();
            }
          }, 500); // 500ms delay
        }, {once: true}); // Only handle the first click
      });
      
      // Also load cart drawer if there are items in the cart
      const cartCount = document.querySelector('[data-cart-count]');
      if (cartCount && cartCount.textContent && parseInt(cartCount.textContent, 10) > 0) {
        // Load after a short delay to prioritize other resources
        setTimeout(loadCartDrawerScript, 2000); // 2 second delay
      }
    } else {
      // On desktop, load cart drawer with defer
      const script = document.createElement('script');
      script.src = '{{ "cart-drawer.min.js" | asset_url }}';
      script.defer = true;
      document.head.appendChild(script);
    }
    
    // Handle add to cart forms
    const addToCartForms = document.querySelectorAll('[data-ajax-cart-form]');
    if (addToCartForms.length > 0) {
      addToCartForms.forEach(form => {
        form.addEventListener('submit', function(event) {
          // Load cart drawer script when user adds to cart
          loadCartDrawerScript();
        });
      });
    }
  });
  
  // Add CSS for loading indicator
  const style = document.createElement('style');
  style.textContent = `
    [data-cart-count].loading {
      animation: pulse 1s infinite;
    }
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
  `;
  document.head.appendChild(style);
</script>
