{% comment %}
  Performance Optimizations Snippet
  
  This snippet contains various performance optimizations to reduce server response time.
  Include this snippet in the theme.liquid file to apply these optimizations.
{% endcomment %}

{% comment %}
  Preconnect to critical domains to establish early connections
{% endcomment %}
<link rel="preconnect" href="{{ shop.secure_url }}">
<link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="dns-prefetch" href="https://cdn.shopify.com">

{% comment %}
  Cache control headers for static assets
  These are implemented via meta tags since we can't directly set HTTP headers
{% endcomment %}
<meta http-equiv="Cache-Control" content="public, max-age=86400">

{% comment %}
  Preload critical resources based on template
{% endcomment %}
{% if template.name == 'index' %}
  {% if section.settings.logo != blank %}
    <link rel="preload" as="image" href="{{ section.settings.logo | img_url: '200x' }}" imagesizes="200px" fetchpriority="high">
  {% endif %}
  
  {% comment %}
    Preload hero image only if it's the first section
    This is already handled in theme.liquid, so we don't duplicate it here
  {% endcomment %}
{% endif %}

{% comment %}
  Implement resource hints for third-party domains that will be used
{% endcomment %}
{% if settings.enable_analytics %}
  <link rel="preconnect" href="https://www.google-analytics.com">
  <link rel="dns-prefetch" href="https://www.google-analytics.com">
{% endif %}

{% comment %}
  Implement server-side caching for cart data to reduce API calls
  This uses the cart object which is already cached by Shopify
{% endcomment %}
{% if cart.item_count > 0 %}
  <script>
    // Store cart data in sessionStorage to reduce API calls
    try {
      sessionStorage.setItem('cart_item_count', {{ cart.item_count }});
      sessionStorage.setItem('cart_total_price', {{ cart.total_price }});
    } catch (e) {
      // Handle potential quota errors
      console.warn('Unable to cache cart data:', e);
    }
  </script>
{% endif %}

{% comment %}
  Implement client-side caching for product data
  This helps reduce subsequent page loads by caching product data
{% endcomment %}
{% if template contains 'product' and product.id %}
  <script>
    // Store current product data in localStorage
    try {
      const productData = {
        id: {{ product.id }},
        title: {{ product.title | json }},
        handle: {{ product.handle | json }},
        price: {{ product.price }},
        available: {{ product.available }},
        timestamp: Date.now()
      };
      localStorage.setItem('product_{{ product.id }}', JSON.stringify(productData));
    } catch (e) {
      console.warn('Unable to cache product data:', e);
    }
  </script>
{% endif %}

{% comment %}
  Implement client-side caching for collection data
{% endcomment %}
{% if template contains 'collection' and collection.id %}
  <script>
    // Store collection metadata in sessionStorage
    try {
      const collectionData = {
        id: {{ collection.id }},
        title: {{ collection.title | json }},
        handle: {{ collection.handle | json }},
        product_count: {{ collection.products_count }},
        timestamp: Date.now()
      };
      sessionStorage.setItem('collection_{{ collection.id }}', JSON.stringify(collectionData));
    } catch (e) {
      console.warn('Unable to cache collection data:', e);
    }
  </script>
{% endif %}
