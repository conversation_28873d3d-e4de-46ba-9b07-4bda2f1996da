{% comment %}
  Static Rendering Optimization Snippet
  
  This snippet implements static rendering techniques to reduce server processing time.
  It uses JavaScript to dynamically update content after the initial page load,
  allowing the server to send a more static response initially.
{% endcomment %}

{% comment %}
  Static cart count rendering
  Instead of rendering the cart count server-side, we render a placeholder
  and update it client-side to reduce Liquid processing time
{% endcomment %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Update cart count elements
    const cartCountElements = document.querySelectorAll('[data-cart-count]');
    
    // Try to get cart count from sessionStorage first
    let cartCount = 0;
    try {
      const storedCount = sessionStorage.getItem('cart_item_count');
      if (storedCount !== null) {
        cartCount = parseInt(storedCount, 10);
      } else {
        // If not in sessionStorage, use the server-rendered value
        cartCount = {{ cart.item_count }};
      }
    } catch (e) {
      cartCount = {{ cart.item_count }};
    }
    
    // Update all cart count elements
    cartCountElements.forEach(function(element) {
      element.textContent = cartCount;
      
      // Show/hide based on count
      if (cartCount > 0) {
        element.classList.add('has-items');
      } else {
        element.classList.remove('has-items');
      }
    });
  });
</script>

{% comment %}
  Static price formatting
  Instead of using the money filter which can be expensive,
  we can format prices client-side for non-critical elements
{% endcomment %}
<script>
  // Format money values client-side
  function formatMoney(cents, format) {
    if (typeof cents === 'string') {
      cents = cents.replace('.', '');
    }
    
    const value = parseInt(cents || 0, 10);
    const formatString = format || '{{ shop.money_format | default: "${{amount}}" }}';
    
    function formatWithDelimiters(number, precision, thousands, decimal) {
      precision = precision || 2;
      thousands = thousands || ',';
      decimal = decimal || '.';
      
      if (isNaN(number) || number == null) {
        return 0;
      }
      
      number = (number / 100.0).toFixed(precision);
      
      const parts = number.split('.');
      const dollars = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1' + thousands);
      const cents = parts[1] ? (decimal + parts[1]) : '';
      
      return dollars + cents;
    }
    
    const formatMappings = {
      'amount': formatWithDelimiters(value, 2),
      'amount_no_decimals': formatWithDelimiters(value, 0),
      'amount_with_comma_separator': formatWithDelimiters(value, 2, '.', ','),
      'amount_no_decimals_with_comma_separator': formatWithDelimiters(value, 0, '.', ','),
      'amount_with_apostrophe_separator': formatWithDelimiters(value, 2, "'", '.')
    };
    
    return formatString.replace(/\{\{\s*(\w+)\s*\}\}/, function(match, key) {
      return formatMappings[key] || '';
    });
  }
  
  // Update price elements on page load
  document.addEventListener('DOMContentLoaded', function() {
    const priceElements = document.querySelectorAll('[data-price-cents]');
    
    priceElements.forEach(function(element) {
      const cents = element.getAttribute('data-price-cents');
      if (cents) {
        element.innerHTML = formatMoney(cents);
      }
    });
  });
</script>

{% comment %}
  Deferred content loading
  For non-critical content, we can defer loading until after the initial page render
{% endcomment %}
<script>
  // Load deferred content after page load
  document.addEventListener('DOMContentLoaded', function() {
    const deferredElements = document.querySelectorAll('[data-deferred-content]');
    
    deferredElements.forEach(function(element) {
      const url = element.getAttribute('data-deferred-content');
      if (url) {
        fetch(url)
          .then(response => response.text())
          .then(html => {
            element.innerHTML = html;
            // Dispatch event to notify that content has been loaded
            element.dispatchEvent(new CustomEvent('content-loaded'));
          })
          .catch(error => {
            console.error('Error loading deferred content:', error);
          });
      }
    });
  });
</script>
