# Sample Products and Collections from Wanderlooms.com

This guide will help you add sample products and collections from wanderlooms.com to your Shopify store.

## Collections to Create

Based on wanderlooms.com, here are the main collections you should create:

1. **Apparel**
   - Oversized T-Shirts
   - Allover Print T-Shirts
   - Crew Neck T-Shirts
   - Hoodies
   - Jackets

2. **Accessories**
   - Enamel Mugs
   - Caps
   - Keytags
   - Stickers
   - Bandanas

3. **Adventure Gear**
   - Riding Jerseys
   - Waterproof Dry Bags
   - Travel Essentials

## Sample Products

Here are sample products you can add to your store based on wanderlooms.com:

### Oversized T-Shirts

1. **Adventure Calling Oversized T-Shirt**
   - Price: $39.99
   - Compare at Price: $59.99
   - Description: Comfortable oversized t-shirt with adventure-themed graphic print. Perfect for casual outings and travel enthusiasts.
   - Options: Size (S, M, L, XL, XXL)
   - Tags: apparel, t-shirt, oversized, adventure

2. **Ride Wild Oversized T-Shirt**
   - Price: $42.99
   - Compare at Price: $64.99
   - Description: Stone washed oversized t-shirt with motorcycle-inspired design. Features a distressed print for a vintage look.
   - Options: Size (S, M, L, XL, XXL)
   - Tags: apparel, t-shirt, oversized, motorcycle

3. **Wander Women Oversized T-Shirt**
   - Price: $39.99
   - Compare at Price: $59.99
   - Description: Specially designed oversized t-shirt for women who love to travel and explore. Features a stylish graphic print.
   - Options: Size (S, M, L, XL, XXL)
   - Tags: apparel, t-shirt, oversized, women

### Accessories

1. **Around the World Enamel Mug**
   - Price: $24.99
   - Compare at Price: $34.99
   - Description: Durable enamel mug with a world map design. Perfect for camping, hiking, and outdoor adventures.
   - Options: Color (Black, White)
   - Tags: accessories, mug, camping, travel

2. **Wanderlust Bandana**
   - Price: $14.99
   - Compare at Price: $19.99
   - Description: Versatile bandana with wanderlust-inspired design. Can be used as a face covering, headband, or neck wrap.
   - Options: Color (Blue, Black, Red)
   - Tags: accessories, bandana, outdoor

3. **Adventure Keytag**
   - Price: $9.99
   - Compare at Price: $14.99
   - Description: Durable keytag with adventure-themed design. Perfect for keeping your keys organized while showing off your passion for travel.
   - Tags: accessories, keytag

4. **Motorcycle Sticker Pack**
   - Price: $7.99
   - Compare at Price: $12.99
   - Description: Set of 5 high-quality vinyl stickers with motorcycle-themed designs. Waterproof and UV-resistant.
   - Tags: accessories, stickers, motorcycle

### Adventure Gear

1. **Mountain Explorer Riding Jersey**
   - Price: $49.99
   - Compare at Price: $69.99
   - Description: Breathable, quick-dry riding jersey with mountain design. Perfect for cycling, mountain biking, and outdoor activities.
   - Options: Size (S, M, L, XL, XXL)
   - Tags: adventure gear, jersey, cycling

2. **Waterproof Adventure Dry Bag**
   - Price: $29.99
   - Compare at Price: $39.99
   - Description: 10L waterproof dry bag to keep your essentials safe during water activities. Features adjustable shoulder strap and roll-top closure.
   - Options: Color (Blue, Black, Yellow)
   - Tags: adventure gear, dry bag, waterproof

## How to Add These to Your Shopify Store

1. **Create Collections First**
   - Go to Products > Collections in your Shopify admin
   - Click "Create collection"
   - Add the collection title, description, and collection image
   - Set collection type to "Manual" to add products yourself
   - Save the collection

2. **Add Products**
   - Go to Products > All Products in your Shopify admin
   - Click "Add product"
   - Fill in the product details (title, description, images, price)
   - Add variants if needed (sizes, colors)
   - In the "Product organization" section, add the product to the appropriate collection
   - Save the product

3. **Add Product Images**
   - You can use placeholder images or create your own using tools like Canva or Adobe Express
   - For a more professional look, consider using stock photos from sites like Unsplash or Pexels
   - Make sure all images have the same aspect ratio for a consistent look

4. **Set Up Navigation**
   - Go to Online Store > Navigation in your Shopify admin
   - Edit your main menu to include your new collections
   - Consider creating a dropdown menu structure for better organization

## Sample Product Images

For product images, you can use these free stock photo websites:

1. Unsplash: https://unsplash.com/
2. Pexels: https://www.pexels.com/
3. Pixabay: https://pixabay.com/

Search for terms like:
- "t-shirt mockup"
- "enamel mug"
- "bandana"
- "stickers"
- "cycling jersey"
- "dry bag"

## Additional Tips

1. **Use Consistent Sizing**
   Make sure all your product images have the same aspect ratio for a clean, professional look.

2. **Write Compelling Descriptions**
   Focus on benefits, not just features. Tell a story about how the product enhances the customer's life.

3. **Use Tags Effectively**
   Tags help with internal organization and can improve searchability on your store.

4. **Set Up Product Recommendations**
   Use Shopify's product recommendation feature to suggest related items to customers.

5. **Create a Featured Collection**
   Highlight your best products in a "Featured" or "Best Sellers" collection on your homepage.
