# Levo Shopify Theme

Levo is a high-performance Shopify theme focused on page speed optimization and minimal library usage.

## Features

- Optimized for speed and performance
- Minimal JavaScript and CSS footprint
- Lazy loading of images
- Responsive design
- Customizable through theme settings
- Modular section-based architecture

## Theme Structure

```
├── assets/
│   ├── theme.css
│   └── theme.js
├── config/
│   ├── settings_data.json
│   └── settings_schema.json
├── layout/
│   └── theme.liquid
├── locales/
│   └── en.default.json
├── sections/
│   ├── cart-template.liquid
│   ├── collection-header.liquid
│   ├── collection-template.liquid
│   ├── featured-collection.liquid
│   ├── footer.liquid
│   ├── header.liquid
│   ├── hero-banner.liquid
│   ├── image-with-text.liquid
│   ├── newsletter.liquid
│   ├── product-recommendations.liquid
│   └── product-template.liquid
├── snippets/
│   ├── icon-account.liquid
│   ├── icon-cart.liquid
│   └── icon-search.liquid
└── templates/
    ├── cart.liquid
    ├── collection.liquid
    ├── index.liquid
    └── product.liquid
```

## Performance Optimizations

1. **Minimal CSS Framework**: Custom lightweight CSS with only what's needed
2. **Lazy Loading**: Images load only when they enter the viewport
3. **Code Splitting**: JavaScript is modular and loads only when needed
4. **Critical CSS**: Essential styles are inlined in the `<head>`
5. **Deferred JavaScript**: Non-critical scripts load after page content
6. **Optimized Assets**: Minified CSS and JavaScript

## Development

### Local Development

To work on this theme locally, you'll need to use Shopify CLI or Theme Kit:

#### Using Shopify CLI

```bash
# Install Shopify CLI
npm install -g @shopify/cli @shopify/theme

# Login to your Shopify store
shopify login

# Create a development theme
shopify theme dev

# Push changes to your development theme
shopify theme push
```

#### Using Theme Kit

```bash
# Install Theme Kit
curl -s https://shopify.github.io/themekit/scripts/install.py | python

# Configure Theme Kit
theme configure --password=[your-api-password] --store=[your-store.myshopify.com] --themeid=[your-theme-id]

# Download the theme
theme download

# Watch for changes
theme watch
```

## Customization

### Theme Settings

The theme can be customized through the Shopify Theme Editor. The following settings are available:

1. **Colors**: Primary, secondary, text, and background colors
2. **Typography**: Heading and body fonts
3. **Layout**: Maximum page width
4. **Social Media**: Social media links
5. **Favicon**: Site favicon

### Sections

The theme uses Shopify's section architecture, allowing for easy customization of the homepage and other pages through the Theme Editor.

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Opera (latest)
- iOS Safari (latest)
- Android Chrome (latest)

## License

This theme is copyright protected and cannot be redistributed without permission.
