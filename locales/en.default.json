{"homepage": {"onboarding": {"no_collection_selected": "No collection selected", "no_collections_selected": "No collections selected", "collection_not_exist": "Collection does not exist"}}, "general": {"accessibility": {"skip_to_content": "Skip to content"}, "navigation": {"menu": "<PERSON><PERSON>"}, "errors": {"form_error": "There was a problem with your submission. Please check the fields below.", "field_error": "{{ field }}: {{ error }}", "login_error": "Incorrect email or password."}, "continue_shopping": "Continue shopping", "meta": {"tags": "Tagged \"{{ tags }}\"", "page": "Page {{ page }}"}, "404": {"title": "404 Page Not Found", "subtext": "The page you requested does not exist.", "link": "Continue shopping"}, "pagination": {"previous": "Previous", "next": "Next", "current_page": "Page {{ current }} of {{ total }}", "page": "Page {{ number }}", "label": "Pagination"}, "password_page": {"opening_soon": "Opening Soon", "login_form_heading": "Enter store using password", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_submit": "Enter", "signup_form_email_label": "Email", "signup_form_success": "We will send you an email right before we open!", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"text-link\">Log in here</a>", "password_link": "Enter using password"}, "social": {"share_on_facebook": "Share", "share_on_twitter": "Tweet", "share_on_pinterest": "Pin it", "alt_text": {"share_on_facebook": "Share on Facebook", "share_on_twitter": "Tweet on Twitter", "share_on_pinterest": "Pin on Pinterest"}}, "search": {"no_results_html": "Your search for \"{{ terms }}\" did not yield any results.", "results_with_count": {"one": "{{ count }} result for \"{{ terms }}\"", "other": "{{ count }} results for \"{{ terms }}\""}, "title": "Search our site", "placeholder": "Search", "submit": "Search", "close": "Close search", "search": "Search", "loading": "Searching..."}, "newsletter_form": {"newsletter_email": "Join our mailing list", "email_placeholder": "Email address", "confirmation": "Thanks for subscribing", "submit": "Subscribe"}, "filters": {"show_more": "Show More", "show_less": "Show Less"}}, "collections": {"general": {"view_all": "View all", "no_matches": "No products found", "collection_not_exist": "Collection does not exist", "filter_by": "Filter by", "sort_by": "Sort by", "products_count": "{{ count }} products", "filter_by_label": "Filter by:", "sort_by_label": "Sort by:", "featured": "Featured", "best_selling": "Best selling", "alphabetically_a_to_z": "Alphabetically, A-Z", "alphabetically_z_to_a": "Alphabetically, Z-A", "price_low_to_high": "Price, low to high", "price_high_to_low": "Price, high to low", "date_new_to_old": "Date, new to old", "date_old_to_new": "Date, old to new", "from": "From", "to": "To", "active_filters": "Active filters", "clear_all": "Clear all"}}, "sections": {"header": {"menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} item", "other": "{{ count }} items"}}, "video": {"placeholder": "Add a video URL", "upload_placeholder": "Upload a video file"}, "cart": {"title": "Your cart", "caption": "Cart items", "remove_title": "Remove {{ title }}", "remove": "Remove", "subtotal": "Subtotal", "new_subtotal": "New subtotal", "note": "Order special instructions", "checkout": "Check out", "empty": "Your cart is empty", "cart_error": "There was an error while updating your cart. Please try again.", "cart_quantity_error_html": "You can only add {{ quantity }} of this item to your cart.", "taxes_and_shipping_policy_at_checkout_html": "Taxes and <a href=\"{{ link }}\">shipping</a> calculated at checkout", "taxes_included_but_shipping_at_checkout": "Tax included and shipping calculated at checkout", "taxes_included_and_shipping_policy_html": "Tax included. <a href=\"{{ link }}\">Shipping</a> calculated at checkout.", "taxes_and_shipping_at_checkout": "Taxes and shipping calculated at checkout", "update": "Update cart", "continue_shopping": "Continue shopping", "headings": {"product": "Product", "price": "Price", "total": "Total", "quantity": "Quantity"}, "login": {"title": "Have an account?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Log in</a> to check out faster."}}, "footer": {"payment": "Payment methods"}, "featured_blog": {"view_all": "View all", "onboarding_title": "Blog post", "onboarding_content": "Give your customers a summary of your blog post"}, "featured_collection": {"view_all": "View all", "view_all_label": "View all products in the {{ collection_name }} collection"}, "collection_list": {"view_all": "View all"}, "collection_template": {"title": "Collection", "sort_by_label": "Sort by:", "sort_button": "Sort", "empty": "No products found", "apply": "Apply", "clear": "Clear", "clear_all": "Clear all", "from": "From", "filter_and_sort": "Filter and sort", "filter_by_label": "Filter:", "filter_button": "Filter", "max_price": "The highest price is {{ price }}", "reset": "Reset", "to": "To", "use_fewer_filters_html": "Use fewer filters or <a class=\"{{ class }}\" href=\"{{ link }}\">clear all</a>", "product_count": {"one": "{{ product_count }} of {{ count }} product", "other": "{{ product_count }} of {{ count }} products"}, "filters_selected": {"one": "{{ count }} selected", "other": "{{ count }} selected"}, "product_count_simple": {"one": "{{ count }} product", "other": "{{ count }} products"}}}, "products": {"product": {"add_to_cart": "Add to cart", "description": "Description", "on_sale": "Sale", "product_variants": "Product variants", "quantity": {"label": "Quantity", "input_label": "Quantity for {{ product }}", "increase": "Increase quantity for {{ product }}", "decrease": "Decrease quantity for {{ product }}"}, "items": "Items", "price": {"from_price_html": "From {{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}, "share": "Share this product", "sold_out": "Sold out", "unavailable": "Unavailable", "vendor": "<PERSON><PERSON><PERSON>", "video_exit_message": "{{ title }} opens full screen video in same window.", "xr_button": "View in your space", "xr_button_label": "View in your space, loads item in augmented reality window", "pickup_availability": {"view_store_info": "View store information", "check_other_stores": "Check availability at other stores", "pick_up_available": "Pickup available", "pick_up_available_at_html": "Pickup available at <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "Pickup currently unavailable at <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "Couldn't load pickup availability", "refresh": "Refresh"}}}, "customer": {"account": {"title": "Account", "details": "Account details", "view_addresses": "View addresses", "return": "Return to Account details"}, "account_fallback": "Account", "activate_account": {"title": "Activate account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Activate account", "cancel": "Decline invitation"}, "addresses": {"title": "Addresses", "default": "<PERSON><PERSON><PERSON>", "add_new": "Add a new address", "edit_address": "Edit address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address 1", "address2": "Address 2", "city": "City", "country": "Country/region", "province": "Province", "zip": "Postal/ZIP code", "phone": "Phone", "set_default": "Set as default address", "add": "Add address", "update": "Update address", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "delete_confirm": "Are you sure you wish to delete this address?"}, "log_in": "Log in", "log_out": "Log out", "sign_up": "Sign up", "login_page": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "forgot_password": "Forgot your password?", "sign_in": "Sign in", "guest_title": "Continue as a guest", "guest_continue": "Continue"}, "order": {"title": "Order {{ name }}", "date_html": "Placed on {{ date }}", "cancelled_html": "Order Cancelled on {{ date }}", "cancelled_reason": "Reason: {{ reason }}", "billing_address": "Billing Address", "payment_status": "Payment Status", "shipping_address": "Shipping Address", "fulfillment_status": "Fulfillment Status", "discount": "Discount", "shipping": "Shipping", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "fulfilled_at_html": "Fulfilled {{ date }}", "track_shipment": "Track shipment", "tracking_url": "Tracking link", "tracking_company": "Carrier", "tracking_number": "Tracking number", "subtotal": "Subtotal", "items": "Order Items"}, "orders": {"title": "Order history", "order_number": "Order", "order_number_link": "Order number {{ number }}", "date": "Date", "payment_status": "Payment status", "fulfillment_status": "Fulfillment status", "total": "Total", "none": "You haven't placed any orders yet."}, "recover_password": {"title": "Reset your password", "email": "Email", "submit": "Submit", "cancel": "Cancel", "subtext": "We will send you an email to reset your password.", "success": "We've sent you an email with a link to update your password."}, "register": {"title": "Create account", "first_name": "First name", "last_name": "Last name", "email": "Email", "password": "Password", "submit": "Create"}, "reset_password": {"title": "Reset account password", "subtext": "Enter a new password for {{ email }}", "password": "Password", "password_confirm": "Confirm password", "submit": "Reset password"}}, "gift_cards": {"issued": {"title": "Here's your {{ value }} gift card for {{ shop }}!", "subtext": "Your gift card", "gift_card_code": "Gift card code", "shop_link": "Continue shopping", "remaining_html": "Remaining {{ balance }}", "add_to_apple_wallet": "Add to Apple Wallet", "qr_image_alt": "QR code — scan to redeem gift card", "copy_code": "Copy code", "expired": "Expired", "copy_code_success": "Code copied successfully", "print_gift_card": "Print"}}}