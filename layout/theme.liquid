<!DOCTYPE html>
<html lang="{{ request.locale.iso_code }}">
<head>
  <!-- Disable Shopify Hydrogen CSP - Inlined for performance -->
  <script>
    // This script runs immediately to disable Hydrogen CSP
    (function() {
      // Override the Content-Security-Policy header
      // This must run before any other scripts
      var originalSetAttribute = Element.prototype.setAttribute;
      Element.prototype.setAttribute = function(name, value) {
        // If this is a CSP meta tag, prevent it from being set
        if (this.tagName === 'META' &&
            name.toLowerCase() === 'http-equiv' &&
            value.toLowerCase() === 'content-security-policy') {
          return;
        }
        // Otherwise, proceed normally
        return originalSetAttribute.call(this, name, value);
      };

      // Remove any existing CSP meta tags
      if (document.head) {
        var metaTags = document.head.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
        metaTags.forEach(function(tag) {
          tag.parentNode.removeChild(tag);
        });
      }
    })();
  </script>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.color_primary }}">
  <link rel="canonical" href="{{ canonical_url }}">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  {% comment %}Load selected fonts from Google Fonts{% endcomment %}
  {% assign heading_font = settings.font_heading | remove: "'" | split: "," | first %}
  {% assign body_font = settings.font_body | remove: "'" | split: "," | first %}

  {% assign unique_fonts = "" %}
  {% if heading_font != body_font %}
    {% assign unique_fonts = heading_font | append: "|" | append: body_font %}
  {% else %}
    {% assign unique_fonts = heading_font %}
  {% endif %}

  <!-- Optimized font loading strategy -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Only preload the font weights we need immediately (400 and 700) -->
  <link rel="preload" as="font" type="font/woff2" href="https://fonts.gstatic.com/s/{{ heading_font | handleize }}/v1/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-mu0SC55I.woff2" crossorigin>

  <!-- Load fonts with font-display: swap to prevent render blocking -->
  <link href="https://fonts.googleapis.com/css2?family={{ unique_fonts | replace: ' ', '+' }}:wght@400;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">

  <!-- Load additional weights asynchronously -->
  <link href="https://fonts.googleapis.com/css2?family={{ unique_fonts | replace: ' ', '+' }}:wght@300;500;600&display=swap" rel="stylesheet" media="print" onload="this.media='all'">

  <!-- Fallback for browsers that don't support JS -->
  <noscript>
    <link href="https://fonts.googleapis.com/css2?family={{ unique_fonts | replace: ' ', '+' }}:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </noscript>

  {%- if settings.favicon != blank -%}
    <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png">
  {%- endif -%}

  {%- capture seo_title -%}
    {{ page_title }}
    {%- if current_tags -%}
      {%- assign meta_tags = current_tags | join: ', ' -%} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags -}}
    {%- endif -%}
    {%- if current_page != 1 -%}
      &ndash; {{ 'general.meta.page' | t: page: current_page }}
    {%- endif -%}
    {%- unless page_title contains shop.name -%}
      &ndash; {{ shop.name }}
    {%- endunless -%}
  {%- endcapture -%}
  <title>{{ seo_title }}</title>

  {%- if page_description -%}
    <meta name="description" content="{{ page_description | escape }}">
  {%- endif -%}

  <!-- Theme Variables -->
  <style>
    :root {
      /* Theme-specific variables - these are used by theme-variables.css */
      --theme-color-primary: {{ settings.color_primary }};
      --theme-color-secondary: {{ settings.color_secondary }};
      --theme-color-text-primary: {{ settings.color_text_primary }};
      --theme-color-text-secondary: {{ settings.color_text_secondary }};
      --theme-color-background: {{ settings.color_background }};
      --theme-color-background-secondary: {{ settings.color_background_secondary }};
      --theme-color-button: {{ settings.color_button }};
      --theme-color-button-text: {{ settings.color_button_text }};
      --theme-color-sale: {{ settings.color_sale }};
      --theme-color-badge: {{ settings.color_badge }};
      --theme-font-heading: {{ settings.font_heading }};
      --theme-font-body: {{ settings.font_body }};
      --theme-font-size-h1: {{ settings.font_size_h1 }}px;
      --theme-font-size-h2: {{ settings.font_size_h2 }}px;
      --theme-font-size-h3: {{ settings.font_size_h3 }}px;
      --theme-font-size-h4: {{ settings.font_size_h4 }}px;
      --theme-font-size-h5: {{ settings.font_size_h5 }}px;
      --theme-font-size-base: {{ settings.font_size_base }}px;
      --theme-font-size-small: {{ settings.font_size_small }}px;
      --theme-line-height: {{ settings.line_height }};
      --theme-layout-max-width: {{ settings.layout_max_width }};

      /* These variables are kept for backward compatibility */
      --color-primary: var(--theme-color-primary);
      --color-secondary: var(--theme-color-secondary);
      --color-text-primary: var(--theme-color-text-primary);
      --color-text-secondary: var(--theme-color-text-secondary);
      --color-text: var(--theme-color-text-primary);
      --color-background: var(--theme-color-background);
      --color-background-secondary: var(--theme-color-background-secondary);
      --color-button: var(--theme-color-button);
      --color-button-text: var(--theme-color-button-text);
      --color-sale: var(--theme-color-sale);
      --color-badge: var(--theme-color-badge);
      --color-border: rgba(0, 0, 0, 0.1);
      --font-heading: var(--theme-font-heading);
      --font-body: var(--theme-font-body);
      --font-size-h1: var(--theme-font-size-h1);
      --font-size-h2: var(--theme-font-size-h2);
      --font-size-h3: var(--theme-font-size-h3);
      --font-size-h4: var(--theme-font-size-h4);
      --font-size-h5: var(--theme-font-size-h5);
      --font-size-base: var(--theme-font-size-base);
      --font-size-small: var(--theme-font-size-small);

      /* Font Weights */
      --font-weight-regular: 400;
      --font-weight-medium: 500;
      --font-weight-semibold: 600;
      --font-weight-bold: 700;

      /* Spacing */
      --spacing-unit: 0.25rem; /* 4px */
      --spacing-extra-tight: calc(var(--spacing-unit) * 1);
      --spacing-tight: calc(var(--spacing-unit) * 2);
      --spacing-base: calc(var(--spacing-unit) * 4);
      --spacing-loose: calc(var(--spacing-unit) * 8);
      --spacing-extra-loose: calc(var(--spacing-unit) * 12);

      /* Borders */
      --border-radius: 4px;
      --border-radius-small: 2px;
      --border-radius-button: 50px;

      /* Transitions */
      --transition-duration: 0.3s;
      --transition-timing: ease;

      /* Layout */
      --page-width: 100%;
      --max-content-width: 1400px;
      --gutter: 1.875rem; /* 30px */

      /* Breakpoints */
      --breakpoint-small: 480px;
      --breakpoint-medium: 749px;
      --breakpoint-large: 989px;
    }
  </style>

  <!-- Inline critical CSS -->
  <style>
    {% render 'critical-css' %}
  </style>

  <!-- Preload critical CSS files (minified versions) -->
  {{ 'critical.min.css' | asset_url | stylesheet_tag: preload: true }}
  {{ 'theme-variables.min.css' | asset_url | stylesheet_tag: preload: true }}
  {{ 'base.min.css' | asset_url | stylesheet_tag: preload: true }}

  <!-- Include performance optimization snippets -->
  {% render 'performance-optimizations' %}

  <!-- Include Liquid rendering optimizations -->
  {% render 'liquid-optimization' %}

  <!-- Preload hero carousel images for all device sizes -->
  {% render 'hero-carousel-preload' %}

  <!-- Critical CSS for hero slider on mobile -->
  {% render 'hero-critical-mobile' %}

  <!-- Load theme CSS in a non-blocking way (minified version) -->
  <link rel="preload" href="{{ 'theme.min.css' | asset_url }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript>{{ 'theme.min.css' | asset_url | stylesheet_tag }}</noscript>

  <!-- CSS Optimizer Script (placed before non-critical CSS) -->
  <script>
    // Inline small script to optimize CSS loading
    (function() {
      // Create a non-blocking way to load CSS
      function loadCSS(href) {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = 'print';
        link.onload = function() { this.media = 'all'; };
        document.head.appendChild(link);
      }

      // List of non-critical CSS files (minified versions)
      var cssFiles = [
        "{{ 'keen-slider.min.css' | asset_url }}",
        "{{ 'collection-filters.min.css' | asset_url }}",
        "{{ 'section-headings.min.css' | asset_url }}",
        "{{ 'button-styles.min.css' | asset_url }}",
        "{{ 'collection-page.min.css' | asset_url }}",
        "{{ 'footer-fix.min.css' | asset_url }}",
        "{{ 'image-dimensions.min.css' | asset_url }}"
      ];

      // Load all CSS files in a non-blocking way
      cssFiles.forEach(loadCSS);
    })();
  </script>

  <!-- Fallback for browsers with JavaScript disabled (minified versions) -->
  <noscript>
    {{ 'keen-slider.min.css' | asset_url | stylesheet_tag }}
    {{ 'collection-filters.min.css' | asset_url | stylesheet_tag }}
    {{ 'section-headings.min.css' | asset_url | stylesheet_tag }}
    {{ 'button-styles.min.css' | asset_url | stylesheet_tag }}
    {{ 'collection-page.min.css' | asset_url | stylesheet_tag }}
    {{ 'footer-fix.min.css' | asset_url | stylesheet_tag }}
    {{ 'image-dimensions.min.css' | asset_url | stylesheet_tag }}
  </noscript>



  <!-- Include static rendering optimizations -->
  {% render 'static-rendering' %}

  <!-- Include query optimization techniques -->
  {% render 'query-optimization' %}

  <!-- Include deferred sections loading -->
  {% render 'deferred-sections' %}

  <!-- Interaction-based script loading - critical for performance -->
  <script src="{{ 'interaction-based-loading.min.js' | asset_url }}"></script>

  <!-- Mobile-optimized JavaScript loading -->
  {% render 'mobile-optimized-js' %}

  <!-- Product page optimized JavaScript -->
  {% render 'product-page-js' %}

  <!-- Cart drawer optimized for mobile -->
  {% render 'cart-drawer-optimized' %}

  <!-- Load scripts based on content -->
  <script>
    // Dynamically load scripts when needed - Optimized for performance
    function loadScript(url, callback, attributes) {
      var script = document.createElement('script');
      script.src = url;

      // Apply all attributes
      if (attributes) {
        for (var key in attributes) {
          script.setAttribute(key, attributes[key]);
        }
      } else {
        // Default to defer
        script.defer = true;
      }

      if (callback) {
        script.onload = callback;
      }

      document.head.appendChild(script);
      return script;
    }

    // Use Intersection Observer to load scripts when elements are visible
    function loadScriptWhenVisible(selector, scriptUrl, callback) {
      var elements = document.querySelectorAll(selector);
      if (elements.length === 0) return;

      var loaded = false;
      var observer = new IntersectionObserver(function(entries) {
        if (loaded) return;

        // If any element is visible or about to be visible
        if (entries.some(entry => entry.isIntersecting || entry.intersectionRatio > 0)) {
          loaded = true;
          loadScript(scriptUrl, callback);
          observer.disconnect();
        }
      }, {rootMargin: '200px'}); // Load when within 200px of viewport

      elements.forEach(function(element) {
        observer.observe(element);
      });
    }

    // Check if element exists before loading related script
    if ('IntersectionObserver' in window) {
      // Load scripts when elements are visible
      document.addEventListener('DOMContentLoaded', function() {
        // Load keen-slider only if there's a slider on the page and when it's about to be visible
        loadScriptWhenVisible('.keen-slider', "{{ 'keen-slider.min.js' | asset_url }}", function() {
          loadScript("{{ 'carousel-init.min.js' | asset_url }}");
        });

        // Load cart-drawer script when needed
        loadScript("{{ 'cart-drawer.min.js' | asset_url }}", null, {defer: true, async: true});
      });
    } else {
      // Fallback for browsers without IntersectionObserver
      document.addEventListener('DOMContentLoaded', function() {
        if (document.querySelector('.keen-slider')) {
          loadScript("{{ 'keen-slider.min.js' | asset_url }}", function() {
            loadScript("{{ 'carousel-init.min.js' | asset_url }}");
          });
        }

        loadScript("{{ 'cart-drawer.min.js' | asset_url }}");
      });
    }
  </script>

  <script>
    // Disable any redirects when in the Shopify Theme Editor
    window.addEventListener('DOMContentLoaded', function() {
      if (window.Shopify && window.Shopify.designMode === true) {
        // This code runs only in the theme editor
        // Disable any redirects or location changes here

        // Example: Override any window.location redirects
        const originalAssign = window.location.assign;
        window.location.assign = function(url) {
          if (window.Shopify.designMode) {
            console.log('Redirect prevented in Theme Editor:', url);
            return;
          }
          return originalAssign.apply(this, arguments);
        };

        const originalReplace = window.location.replace;
        window.location.replace = function(url) {
          if (window.Shopify.designMode) {
            console.log('Redirect prevented in Theme Editor:', url);
            return;
          }
          return originalReplace.apply(this, arguments);
        };

        // Also prevent direct property assignments
        let locationHref = window.location.href;
        Object.defineProperty(window.location, 'href', {
          get: function() { return locationHref; },
          set: function(value) {
            if (window.Shopify.designMode) {
              console.log('Redirect prevented in Theme Editor:', value);
              return locationHref;
            }
            locationHref = value;
            originalAssign.call(window.location, value);
          }
        });
      }
    });
  </script>

  {{ content_for_header }}

  <!-- Additional CSP disabling for Hydrogen -->
  <script>
    // This script runs after content_for_header to catch any CSP headers added by Shopify
    document.addEventListener('DOMContentLoaded', function() {
      // Remove any CSP meta tags that might have been added
      var metaTags = document.querySelectorAll('meta[http-equiv]');
      metaTags.forEach(function(tag) {
        if (tag.getAttribute('http-equiv') &&
            tag.getAttribute('http-equiv').toLowerCase() === 'content-security-policy') {
          tag.parentNode.removeChild(tag);
          console.log('Removed CSP meta tag after page load');
        }
      });

      // Set up a MutationObserver to catch any CSP meta tags added dynamically
      if (typeof MutationObserver !== 'undefined') {
        var observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length) {
              // Check each added node
              mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1 && node.tagName === 'META') {
                  if (node.getAttribute('http-equiv') &&
                      node.getAttribute('http-equiv').toLowerCase() === 'content-security-policy') {
                    node.parentNode.removeChild(node);
                    console.log('Removed dynamically added CSP meta tag');
                  }
                }
              });
            }
          });
        });

        // Start observing the document head
        observer.observe(document.head, {
          childList: true,
          subtree: true
        });
      }
    });
  </script>
</head>

<body class="template-{{ template.name | handle }}">
  <!-- Skip to content link for accessibility -->
  <a href="#MainContent" class="skip-to-content-link visually-hidden focusable">
    {{ 'accessibility.skip_to_content' | t | default: 'Skip to content' }}
  </a>

  <div class="page-container">
    {% section 'header' %}

    <main role="main" id="MainContent" class="main-content full-width" tabindex="-1">
      {{ content_for_layout }}
    </main>

    {% section 'footer' %}

    {% render 'cart-drawer' %}
  </div>
</body>
</html>
