<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="#000000">
  <link rel="canonical" href="https://dyrect-test.myshopify.com/">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  
  
  

  
  

  <link href="https://fonts.googleapis.com/css2?family=Raleway|Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"><title>Dyrect Test
</title><!-- Critical CSS -->
  <style>
    /* Critical CSS will be inlined here */
    :root {
      /* Theme-specific variables - these are used by theme-variables.css */
      --theme-color-primary: #000000;
      --theme-color-secondary: #4A4A4A;
      --theme-color-text-primary: #333333;
      --theme-color-text-secondary: #6A6A6A;
      --theme-color-background: #FFFFFF;
      --theme-color-background-secondary: #F5F5F5;
      --theme-color-button: #000000;
      --theme-color-button-text: #FFFFFF;
      --theme-color-sale: #e32c2b;
      --theme-color-badge: #000000;
      --theme-font-heading: 'Raleway', sans-serif;
      --theme-font-body: 'Inter', sans-serif;
      --theme-font-size-h1: 40px;
      --theme-font-size-h2: 32px;
      --theme-font-size-h3: 28px;
      --theme-font-size-h4: 24px;
      --theme-font-size-h5: 20px;
      --theme-font-size-base: 16px;
      --theme-font-size-small: 14px;
      --theme-line-height: 1.5;
      --theme-layout-max-width: 1200px;

      /* These variables are kept for backward compatibility */
      --color-primary: var(--theme-color-primary);
      --color-secondary: var(--theme-color-secondary);
      --color-text-primary: var(--theme-color-text-primary);
      --color-text-secondary: var(--theme-color-text-secondary);
      --color-text: var(--theme-color-text-primary);
      --color-background: var(--theme-color-background);
      --color-background-secondary: var(--theme-color-background-secondary);
      --color-button: var(--theme-color-button);
      --color-button-text: var(--theme-color-button-text);
      --color-sale: var(--theme-color-sale);
      --color-badge: var(--theme-color-badge);
      --color-border: rgba(0, 0, 0, 0.1);
      --font-heading: var(--theme-font-heading);
      --font-body: var(--theme-font-body);
      --font-size-h1: var(--theme-font-size-h1);
      --font-size-h2: var(--theme-font-size-h2);
      --font-size-h3: var(--theme-font-size-h3);
      --font-size-h4: var(--theme-font-size-h4);
      --font-size-h5: var(--theme-font-size-h5);
      --font-size-base: var(--theme-font-size-base);
      --font-size-small: var(--theme-font-size-small);

      /* Font Weights */
      --font-weight-regular: 400;
      --font-weight-medium: 500;
      --font-weight-semibold: 600;
      --font-weight-bold: 700;

      /* Spacing */
      --spacing-unit: 0.25rem; /* 4px */
      --spacing-extra-tight: calc(var(--spacing-unit) * 1);
      --spacing-tight: calc(var(--spacing-unit) * 2);
      --spacing-base: calc(var(--spacing-unit) * 4);
      --spacing-loose: calc(var(--spacing-unit) * 8);
      --spacing-extra-loose: calc(var(--spacing-unit) * 12);

      /* Borders */
      --border-radius: 4px;
      --border-radius-small: 2px;
      --border-radius-button: 50px;

      /* Transitions */
      --transition-duration: 0.3s;
      --transition-timing: ease;

      /* Layout */
      --page-width: 100%;
      --max-content-width: 1400px;
      --gutter: 1.875rem; /* 30px */

      /* Breakpoints */
      --breakpoint-small: 480px;
      --breakpoint-medium: 749px;
      --breakpoint-large: 989px;
    }

    *, *::before, *::after {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: var(--font-body);
      font-weight: var(--font-weight-regular);
      font-size: var(--font-size-base);
      line-height: 1.5;
      color: var(--color-text-primary);
      background-color: var(--color-background);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    p {
      margin: 0 0 var(--spacing-base);
      font-size: var(--font-size-base);
    }

    small {
      font-size: var(--font-size-small);
      color: var(--color-text-secondary);
    }

    h1, h2, h3, h4, h5, h6 {
      font-family: var(--font-heading);
      font-weight: var(--font-weight-semibold);
      margin: 0 0 var(--spacing-base);
      line-height: 1.2;
      color: var(--color-text-primary);
    }

    h1 {
      font-size: var(--font-size-h1);
    }

    h2 {
      font-size: var(--font-size-h2);
    }

    h3 {
      font-size: var(--font-size-h3);
    }

    h4 {
      font-size: var(--font-size-h4);
    }

    h5, h6 {
      font-size: var(--font-size-h5);
    }

    a {
      color: var(--color-text-primary);
      text-decoration: none;
      transition: color var(--transition-duration) var(--transition-timing);
    }

    a:hover {
      color: var(--color-primary);
    }

    img {
      max-width: 100%;
      height: auto;
      display: block;
    }

    .page-container {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    .main-content {
      flex: 1 0 auto;
    }

    .page-width {
      width: var(--page-width);
      max-width: var(--max-content-width);
      margin: 0 auto;
      padding: 0 var(--gutter);
    }

    .full-width {
      width: 100%;
      margin: 0;
      padding: 0;
    }

    .visually-hidden {
      position: absolute !important;
      overflow: hidden;
      width: 1px;
      height: 1px;
      margin: -1px;
      padding: 0;
      border: 0;
      clip: rect(0 0 0 0);
      word-wrap: normal !important;
    }

    .button {
      display: inline-block;
      padding: 12px 24px;
      font-family: var(--font-body);
      font-weight: var(--font-weight-medium);
      font-size: 14px;
      line-height: 1;
      text-align: center;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      background-color: var(--color-button);
      color: var(--color-button-text);
      border: none;
      cursor: pointer;
      transition: background-color var(--transition-duration) var(--transition-timing),
                  color var(--transition-duration) var(--transition-timing);
    }

    .button:hover {
      background-color: var(--color-primary);
      color: white;
    }
  </style>

  <!-- Critical CSS preloaded -->
  <link href="/cdn/shop/t/4/assets/theme-variables.css?v=125730948915397852701746989614" rel="stylesheet" type="text/css" media="all" />
  <link href="/cdn/shop/t/4/assets/base.css?v=172784788930083753171746989613" rel="stylesheet" type="text/css" media="all" />
  <link href="/cdn/shop/t/4/assets/theme.css?v=31844958523035412261746876915" rel="stylesheet" type="text/css" media="all" />

  <!-- Non-critical CSS deferred -->
  <link rel="preload" href="/cdn/shop/t/4/assets/keen-slider.min.css?v=138070519334752040901746796290" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="/cdn/shop/t/4/assets/keen-slider.min.css?v=138070519334752040901746796290"></noscript>

  <link rel="preload" href="/cdn/shop/t/4/assets/collection-filters.css?v=114496094579248919981746825083" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="/cdn/shop/t/4/assets/collection-filters.css?v=114496094579248919981746825083"></noscript>

  <link rel="preload" href="/cdn/shop/t/4/assets/section-headings.css?v=12785046028592184481746989615" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="/cdn/shop/t/4/assets/section-headings.css?v=12785046028592184481746989615"></noscript>

  <link rel="preload" href="/cdn/shop/t/4/assets/button-styles.css?v=108077516912498803171746989614" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="/cdn/shop/t/4/assets/button-styles.css?v=108077516912498803171746989614"></noscript>

  <link rel="preload" href="/cdn/shop/t/4/assets/collection-page.css?v=4735857307190550121746989615" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="/cdn/shop/t/4/assets/collection-page.css?v=4735857307190550121746989615"></noscript>

  <!-- CSS loading fallback -->
  <script>
    // Fallback for browsers that don't support preload
    !function(n){"use strict";n.loadCSS||(n.loadCSS=function(){});var o=loadCSS.relpreload={};if(o.support=function(){var e;try{e=n.document.createElement("link").relList.supports("preload")}catch(t){e=!1}return function(){return e}}(),o.bindMediaToggle=function(t){var e=t.media||"all";function a(){t.addEventListener?t.removeEventListener("load",a):t.attachEvent&&t.detachEvent("onload",a),t.setAttribute("onload",null),t.media=e}t.addEventListener?t.addEventListener("load",a):t.attachEvent&&t.attachEvent("onload",a),setTimeout(function(){t.rel="stylesheet",t.media="only x"}),setTimeout(a,3e3)},o.poly=function(){if(!o.support())for(var t=n.document.getElementsByTagName("link"),e=0;e<t.length;e++){var a=t[e];"preload"!==a.rel||"style"!==a.getAttribute("as")||a.getAttribute("data-loadcss")||(a.setAttribute("data-loadcss",!0),o.bindMediaToggle(a))}},!o.support()){o.poly();var t=n.setInterval(o.poly,500);n.addEventListener?n.addEventListener("load",function(){o.poly(),n.clearInterval(t)}):n.attachEvent&&n.attachEvent("onload",function(){o.poly(),n.clearInterval(t)})}"undefined"!=typeof exports?exports.loadCSS=loadCSS:n.loadCSS=loadCSS}("undefined"!=typeof global?global:this);
  </script>

  <!-- Deferred JavaScript -->
  <script src="/cdn/shop/t/4/assets/theme.js?v=4543255985136857351746796290" defer></script>

  <!-- Load scripts based on template -->
  

  <!-- Load scripts based on content -->
  <script>
    // Dynamically load scripts when needed
    function loadScript(url, callback) {
      var script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = url;
      script.defer = true;

      if (callback) {
        script.onload = callback;
      }

      document.head.appendChild(script);
    }

    // Check if element exists before loading related script
    document.addEventListener('DOMContentLoaded', function() {
      // Load keen-slider only if there's a slider on the page
      if (document.querySelector('.keen-slider')) {
        loadScript("/cdn/shop/t/4/assets/keen-slider.min.js?v=35018666428963806041746796291");
      }

      // Load cart-drawer script
      loadScript("/cdn/shop/t/4/assets/cart-drawer.js?v=78206501845529517591747034489");
    });
  </script>

  <script>
    // Disable any redirects when in the Shopify Theme Editor
    window.addEventListener('DOMContentLoaded', function() {
      if (window.Shopify && window.Shopify.designMode === true) {
        // This code runs only in the theme editor
        // Disable any redirects or location changes here

        // Example: Override any window.location redirects
        const originalAssign = window.location.assign;
        window.location.assign = function(url) {
          if (window.Shopify.designMode) {
            console.log('Redirect prevented in Theme Editor:', url);
            return;
          }
          return originalAssign.apply(this, arguments);
        };

        const originalReplace = window.location.replace;
        window.location.replace = function(url) {
          if (window.Shopify.designMode) {
            console.log('Redirect prevented in Theme Editor:', url);
            return;
          }
          return originalReplace.apply(this, arguments);
        };

        // Also prevent direct property assignments
        let locationHref = window.location.href;
        Object.defineProperty(window.location, 'href', {
          get: function() { return locationHref; },
          set: function(value) {
            if (window.Shopify.designMode) {
              console.log('Redirect prevented in Theme Editor:', value);
              return locationHref;
            }
            locationHref = value;
            originalAssign.call(window.location, value);
          }
        });
      }
    });
  </script>

  <script>window.performance && window.performance.mark && window.performance.mark('shopify.content_for_header.start');</script><meta id="shopify-digital-wallet" name="shopify-digital-wallet" content="/***********/digital_wallets/dialog">
<link rel="alternate" hreflang="x-default" href="https://dyrect-test.myshopify.com/">
<link rel="alternate" hreflang="en-IN" href="https://dyrect-test.myshopify.com/">
<link rel="alternate" hreflang="en-MX" href="https://dyrect-test.myshopify.com/en-mx">
<script async="async" src="/checkouts/internal/preloads.js?locale=en-IN"></script>
<script id="shopify-features" type="application/json">{"accessToken":"92a93d67a792921a594d0fdb7eeb75df","betas":["rich-media-storefront-analytics"],"domain":"dyrect-test.myshopify.com","predictiveSearch":true,"shopId":***********,"locale":"en"}</script>
<script>var Shopify = Shopify || {};
Shopify.shop = "dyrect-test.myshopify.com";
Shopify.locale = "en";
Shopify.currency = {"active":"INR","rate":"1.0"};
Shopify.country = "IN";
Shopify.theme = {"name":"Levo Theme","id":177723310383,"schema_name":"Levo","schema_version":"2.0.0","theme_store_id":null,"role":"development"};
Shopify.theme.handle = "null";
Shopify.theme.style = {"id":null,"handle":null};
Shopify.cdnHost = "dyrect-test.myshopify.com/cdn";
Shopify.routes = Shopify.routes || {};
Shopify.routes.root = "/";</script>
<script type="module">!function(o){(o.Shopify=o.Shopify||{}).modules=!0}(window);</script>
<script>!function(o){function n(){var o=[];function n(){o.push(Array.prototype.slice.apply(arguments))}return n.q=o,n}var t=o.Shopify=o.Shopify||{};t.loadFeatures=n(),t.autoloadFeatures=n()}(window);</script>
<script id="shop-js-analytics" type="application/json">{"pageType":"index"}</script>
<script id="__st">var __st={"a":***********,"offset":-14400,"reqid":"92fab36e-e7e4-46d2-bd77-4ed613fb2d67-**********","pageurl":"dyrect-test.myshopify.com\/?_fd=0\u0026pb=0","u":"ba8d8d8e2e27","p":"home"};</script>
<script>window.ShopifyPaypalV4VisibilityTracking = true;</script>
<script id="captcha-bootstrap">!function(){'use strict';const t='contact',e='account',n='new_comment',o=[[t,t],['blogs',n],['comments',n],[t,'customer']],c=[[e,'customer_login'],[e,'guest_login'],[e,'recover_customer_password'],[e,'create_customer']],r=t=>t.map((([t,e])=>`form[action*='/${t}']:not([data-nocaptcha='true']) input[name='form_type'][value='${e}']`)).join(','),a=t=>()=>t?[...document.querySelectorAll(t)].map((t=>t.form)):[];function s(){const t=[...o],e=r(t);return a(e)}const i='password',u='form_key',d=['recaptcha-v3-token','g-recaptcha-response','h-captcha-response',i],f=()=>{try{return window.sessionStorage}catch{return}},m='__shopify_v',_=t=>t.elements[u];function p(t,e,n=!1){try{const o=window.sessionStorage,c=JSON.parse(o.getItem(e)),{data:r}=function(t){const{data:e,action:n}=t;return t[m]||n?{data:e,action:n}:{data:t,action:n}}(c);for(const[e,n]of Object.entries(r))t.elements[e]&&(t.elements[e].value=n);n&&o.removeItem(e)}catch(o){console.error('form repopulation failed',{error:o})}}const l='form_type',E='cptcha';function T(t){t.dataset[E]=!0}const w=window,h=w.document,L='Shopify',v='ce_forms',y='captcha';let A=!1;((t,e)=>{const n=(g='f06e6c50-85a8-45c8-87d0-21a2b65856fe',I='https://cdn.shopify.com/shopifycloud/storefront-forms-hcaptcha/ce_storefront_forms_captcha_hcaptcha.v1.5.2.iife.js',D={infoText:'Protected by hCaptcha',privacyText:'Privacy',termsText:'Terms'},(t,e,n)=>{const o=w[L][v],c=o.bindForm;if(c)return c(t,g,e,D).then(n);var r;o.q.push([[t,g,e,D],n]),r=I,A||(h.body.append(Object.assign(h.createElement('script'),{id:'captcha-provider',async:!0,src:r})),A=!0)});var g,I,D;w[L]=w[L]||{},w[L][v]=w[L][v]||{},w[L][v].q=[],w[L][y]=w[L][y]||{},w[L][y].protect=function(t,e){n(t,void 0,e),T(t)},Object.freeze(w[L][y]),function(t,e,n,w,h,L){const[v,y,A,g]=function(t,e,n){const i=e?o:[],u=t?c:[],d=[...i,...u],f=r(d),m=r(i),_=r(d.filter((([t,e])=>n.includes(e))));return[a(f),a(m),a(_),s()]}(w,h,L),I=t=>{const e=t.target;return e instanceof HTMLFormElement?e:e&&e.form},D=t=>v().includes(t);t.addEventListener('submit',(t=>{const e=I(t);if(!e)return;const n=D(e)&&!e.dataset.hcaptchaBound&&!e.dataset.recaptchaBound,o=_(e),c=g().includes(e)&&(!o||!o.value);(n||c)&&t.preventDefault(),c&&!n&&(function(t){try{if(!f())return;!function(t){const e=f();if(!e)return;const n=_(t);if(!n)return;const o=n.value;o&&e.removeItem(o)}(t);const e=Array.from(Array(32),(()=>Math.random().toString(36)[2])).join('');!function(t,e){_(t)||t.append(Object.assign(document.createElement('input'),{type:'hidden',name:u})),t.elements[u].value=e}(t,e),function(t,e){const n=f();if(!n)return;const o=[...t.querySelectorAll(`input[type='${i}']`)].map((({name:t})=>t)),c=[...d,...o],r={};for(const[a,s]of new FormData(t).entries())c.includes(a)||(r[a]=s);n.setItem(e,JSON.stringify({[m]:1,action:t.action,data:r}))}(t,e)}catch(e){console.error('failed to persist form',e)}}(e),e.submit())}));const S=(t,e)=>{t&&!t.dataset[E]&&(n(t,e.some((e=>e===t))),T(t))};for(const o of['focusin','change'])t.addEventListener(o,(t=>{const e=I(t);D(e)&&S(e,y())}));const B=e.get('form_key'),M=e.get(l),P=B&&M;t.addEventListener('DOMContentLoaded',(()=>{const t=y();if(P)for(const e of t)e.elements[l].value===M&&p(e,B);[...new Set([...A(),...v().filter((t=>'true'===t.dataset.shopifyCaptcha))])].forEach((e=>S(e,t)))}))}(h,new URLSearchParams(w.location.search),n,t,e,['guest_login'])})(!0,!0)}();</script>
<script integrity="sha256-w1TMG8bx+vw+BuOfT7Dh2avfdjByyjlNYGyp9vJB5oo=" data-source-attribution="shopify.loadfeatures" defer="defer" src="/cdn/shopifycloud/shopify/assets/storefront/load_feature-c354cc1bc6f1fafc3e06e39f4fb0e1d9abdf763072ca394d606ca9f6f241e68a.js" crossorigin="anonymous"></script>
<script data-source-attribution="shopify.dynamic_checkout.dynamic.init">var Shopify=Shopify||{};Shopify.PaymentButton=Shopify.PaymentButton||{isStorefrontPortableWallets:!0,init:function(){window.Shopify.PaymentButton.init=function(){};var t=document.createElement("script");t.src="/cdn/shopifycloud/portable-wallets/latest/portable-wallets.en.js",t.type="module",document.head.appendChild(t)}};
</script>
<script data-source-attribution="shopify.dynamic_checkout.buyer_consent">
  function portableWalletsHideBuyerConsent(e){var t=document.getElementById("shopify-buyer-consent"),n=document.getElementById("shopify-subscription-policy-button");t&&n&&(t.classList.add("hidden"),t.setAttribute("aria-hidden","true"),n.removeEventListener("click",e))}function portableWalletsShowBuyerConsent(e){var t=document.getElementById("shopify-buyer-consent"),n=document.getElementById("shopify-subscription-policy-button");t&&n&&(t.classList.remove("hidden"),t.removeAttribute("aria-hidden"),n.addEventListener("click",e))}window.Shopify?.PaymentButton&&(window.Shopify.PaymentButton.hideBuyerConsent=portableWalletsHideBuyerConsent,window.Shopify.PaymentButton.showBuyerConsent=portableWalletsShowBuyerConsent);
</script>
<script data-source-attribution="shopify.dynamic_checkout.cart.bootstrap">document.addEventListener("DOMContentLoaded",(function(){function t(){return document.querySelector("shopify-accelerated-checkout-cart, shopify-accelerated-checkout")}if(t())Shopify.PaymentButton.init();else{new MutationObserver((function(e,n){t()&&(Shopify.PaymentButton.init(),n.disconnect())})).observe(document.body,{childList:!0,subtree:!0})}}));
</script>

<script>window.performance && window.performance.mark && window.performance.mark('shopify.content_for_header.end');</script>
<link href="https://monorail-edge.shopifysvc.com" rel="dns-prefetch">
<script>(function(){if ("sendBeacon" in navigator && "performance" in window) {var session_token = document.cookie.match(/_shopify_s=([^;]*)/);function handle_abandonment_event(e) {var entries = performance.getEntries().filter(function(entry) {return /monorail-edge.shopifysvc.com/.test(entry.name);});if (!window.abandonment_tracked && entries.length === 0) {window.abandonment_tracked = true;var currentMs = Date.now();var navigation_start = performance.timing.navigationStart;var payload = {shop_id: ***********,url: window.location.href,navigation_start,duration: currentMs - navigation_start,session_token: session_token && session_token.length === 2 ? session_token[1] : "",page_type: "index"};window.navigator.sendBeacon("https://monorail-edge.shopifysvc.com/v1/produce", JSON.stringify({schema_id: "online_store_buyer_site_abandonment/1.1",payload: payload,metadata: {event_created_at_ms: currentMs,event_sent_at_ms: currentMs}}));}}window.addEventListener('pagehide', handle_abandonment_event);}}());</script>
<script id="web-pixels-manager-setup">(function e(e,d,r,n,o,i){if(void 0===i&&(i={}),!Boolean(null===(t=null===(a=window.Shopify)||void 0===a?void 0:a.analytics)||void 0===t?void 0:t.replayQueue)){var a,t;window.Shopify=window.Shopify||{};var s=window.Shopify;s.analytics=s.analytics||{};var l=s.analytics;l.replayQueue=[],l.publish=function(e,d,r){return l.replayQueue.push([e,d,r]),!0};try{self.performance.mark("wpm:start")}catch(e){}var u=function(){var e={modern:/Edge?\/(1{2}[4-9]|1[2-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Firefox\/(1{2}[4-9]|1[2-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Chrom(ium|e)\/(9{2}|\d{3,})\.\d+(\.\d+|)|(Maci|X1{2}).+ Version\/(15\.\d+|(1[6-9]|[2-9]\d|\d{3,})\.\d+)([,.]\d+|)( \(\w+\)|)( Mobile\/\w+|) Safari\/|Chrome.+OPR\/(9{2}|\d{3,})\.\d+\.\d+|(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone|CPU IPhone OS|CPU iPad OS)[ +]+(15[._]\d+|(1[6-9]|[2-9]\d|\d{3,})[._]\d+)([._]\d+|)|Android:?[ /-](13[1-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})(\.\d+|)(\.\d+|)|Android.+Firefox\/(13[2-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Android.+Chrom(ium|e)\/(13[1-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|SamsungBrowser\/([2-9]\d|\d{3,})\.\d+/,legacy:/Edge?\/(1[6-9]|[2-9]\d|\d{3,})\.\d+(\.\d+|)|Firefox\/(5[4-9]|[6-9]\d|\d{3,})\.\d+(\.\d+|)|Chrom(ium|e)\/(5[1-9]|[6-9]\d|\d{3,})\.\d+(\.\d+|)([\d.]+$|.*Safari\/(?![\d.]+ Edge\/[\d.]+$))|(Maci|X1{2}).+ Version\/(10\.\d+|(1[1-9]|[2-9]\d|\d{3,})\.\d+)([,.]\d+|)( \(\w+\)|)( Mobile\/\w+|) Safari\/|Chrome.+OPR\/(3[89]|[4-9]\d|\d{3,})\.\d+\.\d+|(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone|CPU IPhone OS|CPU iPad OS)[ +]+(10[._]\d+|(1[1-9]|[2-9]\d|\d{3,})[._]\d+)([._]\d+|)|Android:?[ /-](13[1-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})(\.\d+|)(\.\d+|)|Mobile Safari.+OPR\/([89]\d|\d{3,})\.\d+\.\d+|Android.+Firefox\/(13[2-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Android.+Chrom(ium|e)\/(13[1-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Android.+(UC? ?Browser|UCWEB|U3)[ /]?(15\.([5-9]|\d{2,})|(1[6-9]|[2-9]\d|\d{3,})\.\d+)\.\d+|SamsungBrowser\/(5\.\d+|([6-9]|\d{2,})\.\d+)|Android.+MQ{2}Browser\/(14(\.(9|\d{2,})|)|(1[5-9]|[2-9]\d|\d{3,})(\.\d+|))(\.\d+|)|K[Aa][Ii]OS\/(3\.\d+|([4-9]|\d{2,})\.\d+)(\.\d+|)/},d=e.modern,r=e.legacy,n=navigator.userAgent;return n.match(d)?"modern":n.match(r)?"legacy":"unknown"}(),c="modern"===u?"modern":"legacy",f=(null!=o?o:{modern:"",legacy:""})[c],m=function(e){return[e.baseUrl,"/wpm","/b",e.hashVersion,"modern"===e.buildTarget?"m":"l",".js"].join("")}({baseUrl:r,hashVersion:n,buildTarget:c}),p=function(e){var d=e.version,r=e.bundleTarget,n=e.surface,o=e.pageUrl,i=e.monorailEndpoint;return{emit:function(e){var a=e.status,t=e.errorMsg,s=(new Date).getTime(),l=JSON.stringify({metadata:{event_sent_at_ms:s},events:[{schema_id:"web_pixels_manager_load/3.1",payload:{version:d,bundle_target:r,page_url:o,status:a,surface:n,error_msg:t},metadata:{event_created_at_ms:s}}]});if(!i)return console&&console.warn&&console.warn("[Web Pixels Manager] No Monorail endpoint provided, skipping logging."),!1;try{return self.navigator.sendBeacon.bind(self.navigator)(i,l)}catch(e){}var u=new XMLHttpRequest;try{return u.open("POST",i,!0),u.setRequestHeader("Content-Type","text/plain"),u.send(l),!0}catch(e){return console&&console.warn&&console.warn("[Web Pixels Manager] Got an unhandled error while logging to Monorail."),!1}}}}({version:n,bundleTarget:u,surface:e.surface,pageUrl:self.location.href,monorailEndpoint:e.monorailEndpoint});try{i.browserTarget=u,function(e){var d=e.src,r=e.async,n=void 0===r||r,o=e.onload,i=e.onerror,a=e.sri,t=e.scriptDataAttributes,s=void 0===t?{}:t,l=document.createElement("script"),u=document.querySelector("head"),c=document.querySelector("body");if(l.async=n,l.src=d,a&&(l.integrity=a,l.crossOrigin="anonymous"),s)for(var f in s)if(Object.prototype.hasOwnProperty.call(s,f))try{l.dataset[f]=s[f]}catch(e){}if(o&&l.addEventListener("load",o),i&&l.addEventListener("error",i),u)u.appendChild(l);else{if(!c)throw new Error("Did not find a head or body element to append the script");c.appendChild(l)}}({src:m,async:!0,onload:function(){if(!function(){var e,d;return Boolean(null===(d=null===(e=window.Shopify)||void 0===e?void 0:e.analytics)||void 0===d?void 0:d.initialized)}()){var r=window.webPixelsManager.init(e)||void 0;if(r){d(r);var n=window.Shopify.analytics;n.replayQueue.forEach((function(e){var d=e[0],n=e[1],o=e[2];r.publishCustomEvent(d,n,o)})),n.replayQueue=[],n.publish=r.publishCustomEvent,n.visitor=r.visitor,n.initialized=!0}}},onerror:function(){return p.emit({status:"failed",errorMsg:"".concat(m," has failed to load")})},sri:function(e){var d=/^sha384-[A-Za-z0-9+/=]+$/;return"string"==typeof e&&d.test(e)}(f)?f:"",scriptDataAttributes:i}),p.emit({status:"loading"})}catch(e){p.emit({status:"failed",errorMsg:(null==e?void 0:e.message)||"Unknown error"})}}})({shopId: ***********,storefrontBaseUrl: "https://dyrect-test.myshopify.com",extensionsBaseUrl: "https://extensions.shopifycdn.com/cdn/shopifycloud/web-pixels-manager",monorailEndpoint: "https://monorail-edge.shopifysvc.com/unstable/produce_batch",surface: "storefront-renderer",enabledBetaFlags: [],webPixelsConfigList: [{"id":"shopify-app-pixel","configuration":"{}","eventPayloadVersion":"v1","runtimeContext":"STRICT","scriptVersion":"0411","apiClientId":"shopify-pixel","type":"APP","privacyPurposes":["ANALYTICS","MARKETING"]},{"id":"shopify-custom-pixel","eventPayloadVersion":"v1","runtimeContext":"LAX","scriptVersion":"0411","apiClientId":"shopify-pixel","type":"CUSTOM","privacyPurposes":["ANALYTICS","MARKETING"]}],isMerchantRequest: false,effectiveTopLevelDomain: "myshopify.com",initData: {"shop":{"name":"Dyrect Test","paymentSettings":{"currencyCode":"INR"},"myshopifyDomain":"dyrect-test.myshopify.com","countryCode":"IN","storefrontUrl":"https://dyrect-test.myshopify.com"},"customer":null,"cart":null,"checkout":null,"productVariants":[],"purchasingCompany":null},},function pageEvents(webPixelsManagerAPI) {webPixelsManagerAPI.publish("page_viewed", {});},"https://dyrect-test.myshopify.com/cdn","9f94c53cwe611d86fp8ced7fbdmadbd84f4",{"modern":"","legacy":""},{"shopId":"***********","storefrontBaseUrl":"https://dyrect-test.myshopify.com","extensionBaseUrl":"https://extensions.shopifycdn.com/cdn/shopifycloud/web-pixels-manager","surface":"storefront-renderer","enabledBetaFlags":"[]","isMerchantRequest":"false","hashVersion":"9f94c53cwe611d86fp8ced7fbdmadbd84f4"});</script><script>
  window.ShopifyAnalytics = window.ShopifyAnalytics || {};
  window.ShopifyAnalytics.meta = window.ShopifyAnalytics.meta || {};
  window.ShopifyAnalytics.meta.currency = 'INR';
  var meta = {"page":{"pageType":"home"}};
  for (var attr in meta) {
    window.ShopifyAnalytics.meta[attr] = meta[attr];
  }
</script>
<script class="analytics">
  (function () {
    var customDocumentWrite = function(content) {
      var jquery = null;

      if (window.jQuery) {
        jquery = window.jQuery;
      } else if (window.Checkout && window.Checkout.$) {
        jquery = window.Checkout.$;
      }

      if (jquery) {
        jquery('body').append(content);
      }
    };

    var hasLoggedConversion = function(token) {
      if (token) {
        return document.cookie.indexOf('loggedConversion=' + token) !== -1;
      }
      return false;
    }

    var setCookieIfConversion = function(token) {
      if (token) {
        var twoMonthsFromNow = new Date(Date.now());
        twoMonthsFromNow.setMonth(twoMonthsFromNow.getMonth() + 2);

        document.cookie = 'loggedConversion=' + token + '; expires=' + twoMonthsFromNow;
      }
    }

    var trekkie = window.ShopifyAnalytics.lib = window.trekkie = window.trekkie || [];
    if (trekkie.integrations) {
      return;
    }
    trekkie.methods = [
      'identify',
      'page',
      'ready',
      'track',
      'trackForm',
      'trackLink'
    ];
    trekkie.factory = function(method) {
      return function() {
        var args = Array.prototype.slice.call(arguments);
        args.unshift(method);
        trekkie.push(args);
        return trekkie;
      };
    };
    for (var i = 0; i < trekkie.methods.length; i++) {
      var key = trekkie.methods[i];
      trekkie[key] = trekkie.factory(key);
    }
    trekkie.load = function(config) {
      trekkie.config = config || {};
      trekkie.config.initialDocumentCookie = document.cookie;
      var first = document.getElementsByTagName('script')[0];
      var script = document.createElement('script');
      script.type = 'text/javascript';
      script.onerror = function(e) {
        var scriptFallback = document.createElement('script');
        scriptFallback.type = 'text/javascript';
        scriptFallback.onerror = function(error) {
                var Monorail = {
      produce: function produce(monorailDomain, schemaId, payload) {
        var currentMs = new Date().getTime();
        var event = {
          schema_id: schemaId,
          payload: payload,
          metadata: {
            event_created_at_ms: currentMs,
            event_sent_at_ms: currentMs
          }
        };
        return Monorail.sendRequest("https://" + monorailDomain + "/v1/produce", JSON.stringify(event));
      },
      sendRequest: function sendRequest(endpointUrl, payload) {
        // Try the sendBeacon API
        if (window && window.navigator && typeof window.navigator.sendBeacon === 'function' && typeof window.Blob === 'function' && !Monorail.isIos12()) {
          var blobData = new window.Blob([payload], {
            type: 'text/plain'
          });

          if (window.navigator.sendBeacon(endpointUrl, blobData)) {
            return true;
          } // sendBeacon was not successful

        } // XHR beacon

        var xhr = new XMLHttpRequest();

        try {
          xhr.open('POST', endpointUrl);
          xhr.setRequestHeader('Content-Type', 'text/plain');
          xhr.send(payload);
        } catch (e) {
          console.log(e);
        }

        return false;
      },
      isIos12: function isIos12() {
        return window.navigator.userAgent.lastIndexOf('iPhone; CPU iPhone OS 12_') !== -1 || window.navigator.userAgent.lastIndexOf('iPad; CPU OS 12_') !== -1;
      }
    };
    Monorail.produce('monorail-edge.shopifysvc.com',
      'trekkie_storefront_load_errors/1.1',
      {shop_id: ***********,
      theme_id: 177723310383,
      app_name: "storefront",
      context_url: window.location.href,
      source_url: "/cdn/s/trekkie.storefront.7dd5a1f776e0762aa90f8d934b8ac8a05d3d42a2.min.js"});

        };
        scriptFallback.async = true;
        scriptFallback.src = '/cdn/s/trekkie.storefront.7dd5a1f776e0762aa90f8d934b8ac8a05d3d42a2.min.js';
        first.parentNode.insertBefore(scriptFallback, first);
      };
      script.async = true;
      script.src = '/cdn/s/trekkie.storefront.7dd5a1f776e0762aa90f8d934b8ac8a05d3d42a2.min.js';
      first.parentNode.insertBefore(script, first);
    };
    trekkie.load(
      {"Trekkie":{"appName":"storefront","development":false,"defaultAttributes":{"shopId":***********,"isMerchantRequest":null,"themeId":177723310383,"themeCityHash":"16151615585268435797","contentLanguage":"en","currency":"INR"},"isServerSideCookieWritingEnabled":true,"monorailRegion":"shop_domain"},"Session Attribution":{},"S2S":{"facebookCapiEnabled":false,"source":"trekkie-storefront-renderer","apiClientId":580111}}
    );

    var loaded = false;
    trekkie.ready(function() {
      if (loaded) return;
      loaded = true;

      window.ShopifyAnalytics.lib = window.trekkie;

      var originalDocumentWrite = document.write;
      document.write = customDocumentWrite;
      try { window.ShopifyAnalytics.merchantGoogleAnalytics.call(this); } catch(error) {};
      document.write = originalDocumentWrite;

      window.ShopifyAnalytics.lib.page(null,{"pageType":"home","shopifyEmitted":true});

      var match = window.location.pathname.match(/checkouts\/(.+)\/(thank_you|post_purchase)/)
      var token = match? match[1]: undefined;
      if (!hasLoggedConversion(token)) {
        setCookieIfConversion(token);
        
      }
    });


        var eventsListenerScript = document.createElement('script');
        eventsListenerScript.async = true;
        eventsListenerScript.src = "/cdn/shopifycloud/shopify/assets/shop_events_listener-f55dd2979ec32029c7d9e0b454ab8b33f79c01ca039d17a6f5c9b95647564b19.js";
        document.getElementsByTagName('head')[0].appendChild(eventsListenerScript);

})();</script>
<script
  defer
  src="/cdn/shopifycloud/perf-kit/shopify-perf-kit-1.6.2.min.js"
  data-application="storefront-renderer"
  data-shop-id="***********"
  data-render-region="gcp-us-central1"
  data-page-type="index"
  data-theme-instance-id="177723310383"
  data-theme-name="Levo"
  data-theme-version="2.0.0"
  data-monorail-region="shop_domain"
  data-resource-timing-sampling-rate="10"
  data-shs="true"
></script>
<script id="hot-reload-client" src="/cdn/shopifycloud/theme-hot-reload/theme-hot-reload.js" defer></script></head>

<body class="template-index">
  <div class="page-container">
    <div id="shopify-section-header" class="shopify-section">

<header class="site-header" data-section-id="header" data-section-type="header">
  <div class="header-top-container" style="background-color: #000000; color: #ffffff;">
    <div class="header-wrapper">
      <div class="header-left">
        <button type="button" class="mobile-nav-toggle medium-down--show" aria-controls="MobileNav" aria-expanded="false" style="color: #ffffff;">
          <span class="mobile-nav-toggle-bar" style="background-color: #ffffff;"></span>
          <span class="mobile-nav-toggle-bar" style="background-color: #ffffff;"></span>
          <span class="mobile-nav-toggle-bar" style="background-color: #ffffff;"></span>
          <span class="visually-hidden">Translation missing: en.general.navigation.menu</span>
        </button>

        
          <div class="header-sale-announcement" data-rotation-speed="5000">
            

            
              
              

              
                
                <div class="sale-announcement__item active" data-announcement-index="0">
                  
                    <span class="sale-announcement__text" style="color: #ffffff;">SALE: Up to 50% off</span>
                  
                </div>
              
            
              
              

              
                
                <div class="sale-announcement__item" data-announcement-index="1">
                  
                    <span class="sale-announcement__text" style="color: #ffffff;">Free shipping on all orders</span>
                  
                </div>
              
            
              
              

              
                
                <div class="sale-announcement__item" data-announcement-index="2">
                  
                    <span class="sale-announcement__text" style="color: #ffffff;">New arrivals - Shop now</span>
                  
                </div>
              
            

            
          </div>
        
      </div>

      <div class="header-logo">
        
          <a href="/" class="logo-image">
            
            
            <img src="/cdn/shop/files/wanderlooms-logo-dark_180x.svg?v=1746881947"
                 srcset="/cdn/shop/files/wanderlooms-logo-dark_180x.svg?v=1746881947 1x, /cdn/shop/files/<EMAIL>?v=1746881947 2x"
                 alt="Dyrect Test"
                 width="180"
                 height="0"
                 loading="eager">
          </a>
        
      </div>

      <div class="header-icons">
        <button type="button" class="header-icon" data-search-toggle aria-controls="SearchOverlay" aria-expanded="false" style="color: #ffffff;">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-search" viewBox="0 0 24 24">
  <path d="M21.71 20.29l-5.01-5.01A8.96 8.96 0 0018 10a8 8 0 10-8 8 8.96 8.96 0 005.28-1.7l5.01 5.01a1 1 0 001.42 0 1 1 0 000-1.42zM10 16a6 6 0 116-6 6 6 0 01-6 6z"/>
</svg>

          <span class="visually-hidden">Search</span>
        </button>
        <a href="/account" class="header-icon" style="color: #ffffff;">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-account" viewBox="0 0 24 24">
  <path d="M12 2a5 5 0 015 5 5 5 0 01-5 5 5 5 0 01-5-5 5 5 0 015-5m0 2a3 3 0 00-3 3 3 3 0 003 3 3 3 0 003-3 3 3 0 00-3-3m0 10c2.67 0 8 1.33 8 4v2H4v-2c0-2.67 5.33-4 8-4m0 2c-2.69 0-5.78 1.28-6 2h12c-.21-.72-3.3-2-6-2z"/>
</svg>

          <span class="visually-hidden">Account</span>
        </a>
        <button type="button" class="header-icon cart-icon" style="color: #ffffff;">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-cart" viewBox="0 0 24 24">
  <path d="M17 18a2 2 0 012 2 2 2 0 01-2 2 2 2 0 01-2-2c0-1.11.89-2 2-2M1 2h3.27l.94 2H20a1 1 0 011 1c0 .17-.05.34-.12.5l-3.58 6.47c-.34.61-1 1.03-1.75 1.03H8.1l-.9 1.63-.03.12a.25.25 0 00.25.25H19v2H7a2 2 0 01-2-2c0-.35.09-.68.24-.96l1.36-2.45L3 4H1V2m6 16a2 2 0 012 2 2 2 0 01-2 2 2 2 0 01-2-2c0-1.11.89-2 2-2m9-7l2.78-5H6.14l2.36 5H16z"/>
</svg>

          <span class="visually-hidden">Translation missing: en.cart.general.title</span>
          <span class="cart-count" data-cart-count style="background-color: #fcc230; color: #000000;">
            0
          </span>
        </button>
      </div>
    </div>
  </div>

  <div class="header-nav-container" id="headerNavContainer">
    <div class="nav-wrapper">
      <div class="nav-logo-container">
        
          <a href="/" class="nav-logo-image">
            
            
            <img src="/cdn/shop/files/wanderlooms-arrow-black_50x.svg?v=1746883389"
                 srcset="/cdn/shop/files/wanderlooms-arrow-black_50x.svg?v=1746883389 1x, /cdn/shop/files/<EMAIL>?v=1746883389 2x"
                 alt="Dyrect Test"
                 width="50"
                 height="50"
                 loading="eager">
          </a>
        
      </div>

      <nav class="header-nav" role="navigation">
        
          <ul class="site-nav medium-down--hide">
            
              <li class="site-nav__item">
                <a href="/collections" class="site-nav__link">
                  Riding Jerseys
                  
                </a>
                
              </li>
            
              <li class="site-nav__item">
                <a href="/collections" class="site-nav__link">
                  Polygiene Bandana
                  
                </a>
                
              </li>
            
              <li class="site-nav__item">
                <a href="/pages/contact" class="site-nav__link">
                  Contact
                  
                </a>
                
              </li>
            
              <li class="site-nav__item">
                <a href="/collections" class="site-nav__link">
                  Super Saver Combos
                  
                </a>
                
              </li>
            
              <li class="site-nav__item">
                <a href="/collections" class="site-nav__link">
                  Ladakh Collection
                  
                </a>
                
              </li>
            
              <li class="site-nav__item">
                <a href="/collections" class="site-nav__link">
                  Apparels
                  
                </a>
                
              </li>
            
              <li class="site-nav__item site-nav__item--has-dropdown">
                <a href="/collections" class="site-nav__link site-nav__link--has-dropdown">
                  Accessories
                  
                    <span class="site-nav__dropdown-toggle">
                      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-chevron-down" viewBox="0 0 24 24">
                        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                      </svg>
                    </span>
                  
                </a>
                
                  


  <div class="mega-menu" data-dropdown-menu>
    <div class="mega-menu__inner">
      <div class="mega-menu__links">
        <ul class="mega-menu__list">
          
            <li class="mega-menu__item">
              <a href="/collections" class="mega-menu__link">
                Enamel Mugs
              </a>
            </li>
          
            <li class="mega-menu__item">
              <a href="/collections" class="mega-menu__link">
                Caps
              </a>
            </li>
          
            <li class="mega-menu__item">
              <a href="/collections" class="mega-menu__link">
                Stickers
              </a>
            </li>
          
        </ul>
      </div>
    </div>
  </div>


                
              </li>
            
              <li class="site-nav__item">
                <a href="/collections" class="site-nav__link">
                  Deal of the Day
                  
                </a>
                
              </li>
            
              <li class="site-nav__item">
                <a href="/collections" class="site-nav__link">
                  Official Merchandise
                  
                </a>
                
              </li>
            
          </ul>
        
      </nav>

      <div class="nav-icons">
        <button type="button" class="header-icon" data-search-toggle aria-controls="SearchOverlay" aria-expanded="false">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-search" viewBox="0 0 24 24">
  <path d="M21.71 20.29l-5.01-5.01A8.96 8.96 0 0018 10a8 8 0 10-8 8 8.96 8.96 0 005.28-1.7l5.01 5.01a1 1 0 001.42 0 1 1 0 000-1.42zM10 16a6 6 0 116-6 6 6 0 01-6 6z"/>
</svg>

          <span class="visually-hidden">Search</span>
        </button>
        <a href="/account" class="header-icon">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-account" viewBox="0 0 24 24">
  <path d="M12 2a5 5 0 015 5 5 5 0 01-5 5 5 5 0 01-5-5 5 5 0 015-5m0 2a3 3 0 00-3 3 3 3 0 003 3 3 3 0 003-3 3 3 0 00-3-3m0 10c2.67 0 8 1.33 8 4v2H4v-2c0-2.67 5.33-4 8-4m0 2c-2.69 0-5.78 1.28-6 2h12c-.21-.72-3.3-2-6-2z"/>
</svg>

          <span class="visually-hidden">Account</span>
        </a>
        <button type="button" class="header-icon cart-icon">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-cart" viewBox="0 0 24 24">
  <path d="M17 18a2 2 0 012 2 2 2 0 01-2 2 2 2 0 01-2-2c0-1.11.89-2 2-2M1 2h3.27l.94 2H20a1 1 0 011 1c0 .17-.05.34-.12.5l-3.58 6.47c-.34.61-1 1.03-1.75 1.03H8.1l-.9 1.63-.03.12a.25.25 0 00.25.25H19v2H7a2 2 0 01-2-2c0-.35.09-.68.24-.96l1.36-2.45L3 4H1V2m6 16a2 2 0 012 2 2 2 0 01-2 2 2 2 0 01-2-2c0-1.11.89-2 2-2m9-7l2.78-5H6.14l2.36 5H16z"/>
</svg>

          <span class="visually-hidden">Translation missing: en.cart.general.title</span>
          <span class="cart-count" data-cart-count style="background-color: #fcc230; color: #000000;">
            0
          </span>
        </button>
      </div>
    </div>
  </div>

  <div class="mobile-nav medium-down--show" id="MobileNav" aria-hidden="true">
    <div class="mobile-nav__inner">
      <button type="button" class="mobile-nav__close" aria-controls="MobileNav" aria-expanded="true">
        <span class="mobile-nav__close-icon"></span>
        <span class="visually-hidden">Translation missing: en.general.navigation.close_menu</span>
      </button>

      
        <ul class="mobile-nav__list">
          
            <li class="mobile-nav__item">
              <a href="/collections" class="mobile-nav__link">
                Riding Jerseys
              </a>
              
            </li>
          
            <li class="mobile-nav__item">
              <a href="/collections" class="mobile-nav__link">
                Polygiene Bandana
              </a>
              
            </li>
          
            <li class="mobile-nav__item">
              <a href="/pages/contact" class="mobile-nav__link">
                Contact
              </a>
              
            </li>
          
            <li class="mobile-nav__item">
              <a href="/collections" class="mobile-nav__link">
                Super Saver Combos
              </a>
              
            </li>
          
            <li class="mobile-nav__item">
              <a href="/collections" class="mobile-nav__link">
                Ladakh Collection
              </a>
              
            </li>
          
            <li class="mobile-nav__item">
              <a href="/collections" class="mobile-nav__link">
                Apparels
              </a>
              
            </li>
          
            <li class="mobile-nav__item">
              <a href="/collections" class="mobile-nav__link">
                Accessories
              </a>
              
                <ul class="mobile-nav__dropdown">
                  
                    <li class="mobile-nav__dropdown-item">
                      <a href="/collections" class="mobile-nav__dropdown-link">
                        Enamel Mugs
                      </a>
                    </li>
                  
                    <li class="mobile-nav__dropdown-item">
                      <a href="/collections" class="mobile-nav__dropdown-link">
                        Caps
                      </a>
                    </li>
                  
                    <li class="mobile-nav__dropdown-item">
                      <a href="/collections" class="mobile-nav__dropdown-link">
                        Stickers
                      </a>
                    </li>
                  
                </ul>
              
            </li>
          
            <li class="mobile-nav__item">
              <a href="/collections" class="mobile-nav__link">
                Deal of the Day
              </a>
              
            </li>
          
            <li class="mobile-nav__item">
              <a href="/collections" class="mobile-nav__link">
                Official Merchandise
              </a>
              
            </li>
          
        </ul>
      
    </div>
  </div>

  <div id="SearchOverlay" class="search-overlay" aria-hidden="true">
  <div class="search-overlay__inner">
    <div class="page-width">
      <div class="search-overlay__header">
        <h2 class="search-overlay__title">Search our site</h2>
        <button type="button" class="search-overlay__close" aria-controls="SearchOverlay" aria-expanded="true">
          <span class="search-overlay__close-icon"></span>
          <span class="visually-hidden">Close search</span>
        </button>
      </div>
      
      <div class="search-overlay__content">
        <form action="/search" method="get" role="search" class="search-overlay__form">
          <div class="search-overlay__input-wrapper">
            <input type="search" 
                  name="q" 
                  id="SearchOverlayInput" 
                  class="search-overlay__input"
                  value=""
                  placeholder="Search"
                  aria-label="Search"
                  autocomplete="off"
                  autocorrect="off"
                  autocapitalize="off"
                  spellcheck="false">
            <button type="submit" class="search-overlay__submit">
              <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-search" viewBox="0 0 24 24">
  <path d="M21.71 20.29l-5.01-5.01A8.96 8.96 0 0018 10a8 8 0 10-8 8 8.96 8.96 0 005.28-1.7l5.01 5.01a1 1 0 001.42 0 1 1 0 000-1.42zM10 16a6 6 0 116-6 6 6 0 01-6 6z"/>
</svg>

              <span class="visually-hidden">Search</span>
            </button>
          </div>
        </form>
        
        <div id="PredictiveResults" class="predictive-results" aria-live="polite"></div>
      </div>
    </div>
  </div>
</div>

<style>
  .search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.98);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    overflow-y: auto;
  }
  
  .search-overlay[aria-hidden="false"] {
    opacity: 1;
    visibility: visible;
  }
  
  .search-overlay__inner {
    padding: 60px 0;
  }
  
  .search-overlay__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }
  
  .search-overlay__title {
    font-size: 24px;
    margin: 0;
  }
  
  .search-overlay__close {
    background: transparent;
    border: none;
    padding: 10px;
    cursor: pointer;
  }
  
  .search-overlay__close-icon {
    position: relative;
    display: block;
    width: 20px;
    height: 20px;
  }
  
  .search-overlay__close-icon::before,
  .search-overlay__close-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--color-text);
  }
  
  .search-overlay__close-icon::before {
    transform: rotate(45deg);
  }
  
  .search-overlay__close-icon::after {
    transform: rotate(-45deg);
  }
  
  .search-overlay__form {
    margin-bottom: 30px;
  }
  
  .search-overlay__input-wrapper {
    position: relative;
    display: flex;
    border-bottom: 2px solid var(--color-text);
  }
  
  .search-overlay__input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 15px 50px 15px 0;
    font-size: 18px;
    outline: none;
    width: 100%;
  }
  
  .search-overlay__submit {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    padding: 10px;
    cursor: pointer;
  }
  
  .search-overlay__submit .icon {
    width: 20px;
    height: 20px;
  }
  
  .predictive-results {
    margin-top: 20px;
  }
  
  .predictive-result-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--color-border);
    transition: opacity 0.2s ease;
  }
  
  .predictive-result-item:hover {
    opacity: 0.7;
  }
  
  .predictive-result-item__image {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    object-fit: cover;
  }
  
  .predictive-result-item__info {
    flex: 1;
  }
  
  .predictive-result-item__title {
    font-weight: var(--font-weight-medium);
    margin: 0 0 5px;
  }
  
  .predictive-result-item__price {
    font-size: 14px;
  }
  
  .predictive-result-item__price--sale {
    color: var(--color-sale);
  }
  
  .predictive-result-item__price--compare {
    text-decoration: line-through;
    opacity: 0.7;
    margin-left: 5px;
  }
  
  .predictive-results__empty {
    padding: 20px 0;
    text-align: center;
    color: var(--color-secondary);
  }
  
  .predictive-results__loading {
    padding: 20px 0;
    text-align: center;
  }
  
  @media screen and (max-width: 749px) {
    .search-overlay__inner {
      padding: 30px 0;
    }
    
    .search-overlay__title {
      font-size: 20px;
    }
    
    .search-overlay__input {
      font-size: 16px;
      padding: 10px 40px 10px 0;
    }
  }
</style>

<script>
  class PredictiveSearch {
    constructor() {
      this.searchInput = document.getElementById('SearchOverlayInput');
      this.resultsContainer = document.getElementById('PredictiveResults');
      this.searchTimeout = null;
      this.isLoading = false;
      
      this.init();
    }
    
    init() {
      if (!this.searchInput || !this.resultsContainer) return;
      
      this.searchInput.addEventListener('input', this.handleInput.bind(this));
      this.searchInput.addEventListener('focus', this.handleInput.bind(this));
    }
    
    handleInput(event) {
      const searchTerm = event.target.value.trim();
      
      clearTimeout(this.searchTimeout);
      
      if (searchTerm.length < 3) {
        this.resultsContainer.innerHTML = '';
        return;
      }
      
      this.showLoading();
      
      this.searchTimeout = setTimeout(() => {
        this.performSearch(searchTerm);
      }, 500);
    }
    
    showLoading() {
      this.isLoading = true;
      this.resultsContainer.innerHTML = '<div class="predictive-results__loading">Searching...</div>';
    }
    
    async performSearch(searchTerm) {
      try {
        const response = await fetch(`/search/suggest.json?q=${encodeURIComponent(searchTerm)}&resources[type]=product&resources[limit]=4&resources[options][unavailable_products]=last`);
        const data = await response.json();
        
        this.displayResults(data.resources.results);
      } catch (error) {
        console.error('Error fetching search results:', error);
        this.resultsContainer.innerHTML = '<div class="predictive-results__empty">An error occurred. Please try again.</div>';
      } finally {
        this.isLoading = false;
      }
    }
    
    displayResults(results) {
      const products = results.products || [];
      
      if (products.length === 0) {
        this.resultsContainer.innerHTML = '<div class="predictive-results__empty">No results found</div>';
        return;
      }
      
      let html = '';
      
      products.forEach(product => {
        const image = product.image ? `<img src="${product.image}" alt="${product.title}" class="predictive-result-item__image">` : '';
        const price = this.formatPrice(product.price);
        const comparePrice = product.compare_at_price ? this.formatPrice(product.compare_at_price) : '';
        const compareHtml = comparePrice ? `<span class="predictive-result-item__price--compare">${comparePrice}</span>` : '';
        const priceClass = comparePrice ? 'predictive-result-item__price--sale' : '';
        
        html += `
          <a href="${product.url}" class="predictive-result-item">
            ${image}
            <div class="predictive-result-item__info">
              <h3 class="predictive-result-item__title">${product.title}</h3>
              <div class="predictive-result-item__price">
                <span class="${priceClass}">${price}</span>
                ${compareHtml}
              </div>
            </div>
          </a>
        `;
      });
      
      this.resultsContainer.innerHTML = html;
    }
    
    formatPrice(price) {
      return price ? '$' + (parseFloat(price) / 100).toFixed(2) : '';
    }
  }
  
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize search overlay toggle
    const searchToggle = document.querySelector('.header-icon[data-search-toggle]');
    const searchOverlay = document.getElementById('SearchOverlay');
    const searchOverlayClose = document.querySelector('.search-overlay__close');
    
    if (searchToggle && searchOverlay && searchOverlayClose) {
      searchToggle.addEventListener('click', function(e) {
        e.preventDefault();
        searchOverlay.setAttribute('aria-hidden', 'false');
        document.body.style.overflow = 'hidden';
        
        // Focus on search input
        setTimeout(() => {
          document.getElementById('SearchOverlayInput').focus();
        }, 100);
      });
      
      searchOverlayClose.addEventListener('click', function() {
        searchOverlay.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
      });
      
      // Close on escape key
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && searchOverlay.getAttribute('aria-hidden') === 'false') {
          searchOverlay.setAttribute('aria-hidden', 'true');
          document.body.style.overflow = '';
        }
      });
    }
    
    // Initialize predictive search
    new PredictiveSearch();
  });
</script>

</header>

<style>
  /* Header styles */
  .site-header {
    padding: 0;
    border-bottom: none;
    position: relative;
    z-index: 100;
    width: 100%;
  }

  .header-top-container {
    padding: 15px 0;
    width: 100%;
  }

  .header-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
  }

  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .header-sale-announcement {
    margin-left: 20px;
    position: relative;
    min-height: 20px;
    overflow: hidden;
    min-width: 150px;
    max-width: 250px;
  }

  .sale-announcement__item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease, visibility 0.5s ease;
  }

  .sale-announcement__item.active {
    opacity: 1;
    visibility: visible;
    position: relative;
  }

  .sale-announcement__link {
    display: inline-block;
    font-weight: var(--font-weight-medium);
    font-size: 14px;
    transition: opacity 0.2s ease;
    white-space: normal;
    line-height: 1.2;
  }

  .sale-announcement__link:hover {
    opacity: 0.8;
  }

  .sale-announcement__text {
    display: inline-block;
    font-weight: var(--font-weight-medium);
    font-size: 14px;
    white-space: normal;
    line-height: 1.2;
  }

  .header-logo {
    flex: 0 0 auto;
    text-align: center;
    margin: 0 auto;
  }

  .header-logo img {
    max-height: 50px;
    width: auto;
  }

  .header-icons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
  }

  .header-icon {
    background: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
    margin-left: 20px;
    color: var(--color-text-primary);
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .icon {
    width: 20px;
    height: 20px;
  }

  .cart-icon {
    position: relative;
  }

  .cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 10px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
  }

  /* Navigation styles */
  .header-nav-container {
    background-color: var(--color-background);
    border-top: 1px solid var(--color-border);
    border-bottom: 1px solid var(--color-border);
    width: 100%;
    position: relative;
    z-index: 100;
    transition: position 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
    padding: 5px 0;
  }

  .header-nav-container.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease;
  }

  @keyframes slideDown {
    from {
      transform: translateY(-100%);
    }
    to {
      transform: translateY(0);
    }
  }

  .nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 40px;
  }

  .nav-logo-container {
    opacity: 0;
    visibility: hidden;
    width: 0;
    overflow: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease, width 0.3s ease;
    flex: 0 0 auto;
  }

  .fixed .nav-logo-container {
    opacity: 1;
    visibility: visible;
    width: auto;
    margin-right: 30px;
    display: flex;
    align-items: center;
  }

  .nav-logo-image img {
    max-height: 40px;
    width: auto;
    object-fit: contain;
  }

  .nav-logo-text {
    font-weight: var(--font-weight-bold);
    font-size: 16px;
  }

  .nav-icons {
    display: none;
  }

  .fixed .nav-icons {
    display: flex;
    align-items: center;
  }

  .header-nav {
    display: flex;
    justify-content: center;
    flex: 1;
  }

  .site-nav {
    display: flex;
    justify-content: center;
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
  }

  .site-nav__item {
    position: relative;
    margin: 0 10px;
  }

  .site-nav__link {
    display: block;
    padding: 15px 0;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: var(--font-weight-bold);
    position: relative;
    color: var(--color-text-primary);
  }

  .site-nav__link--has-dropdown {
    display: flex;
    align-items: center;
  }

  .site-nav__dropdown-toggle {
    margin-left: 5px;
    display: flex;
    align-items: center;
  }

  .icon-chevron-down {
    width: 12px;
    height: 12px;
    transition: transform 0.3s ease;
  }

  /* Mega menu dropdown */
  .site-nav__item--has-dropdown {
    position: relative;
  }

  .mega-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    width: 220px;
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
    z-index: 100;
  }

  .site-nav__item--has-dropdown:hover .mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
  }

  .site-nav__item--has-dropdown:hover .icon-chevron-down {
    transform: rotate(180deg);
  }

  .mega-menu__inner {
    padding: 20px;
  }

  .mega-menu__list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .mega-menu__item {
    margin-bottom: 10px;
  }

  .mega-menu__link {
    font-size: 12px;
    color: var(--color-text-primary);
    transition: color 0.2s ease;
    display: block;
    padding: 5px 0;
  }

  .mega-menu__link:hover {
    color: var(--color-primary);
  }

  /* Mobile navigation */
  .mobile-nav-toggle {
    display: none;
    background: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
  }

  .mobile-nav-toggle-bar {
    display: block;
    width: 20px;
    height: 2px;
    background-color: var(--color-text-primary);
    margin: 4px 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
  }

  .mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-background);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    display: none;
  }

  .mobile-nav[aria-hidden="false"] {
    transform: translateX(0);
  }

  .mobile-nav__inner {
    padding: 30px 20px;
    height: 100%;
    overflow-y: auto;
  }

  .mobile-nav__close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: transparent;
    border: none;
    padding: 10px;
    cursor: pointer;
  }

  .mobile-nav__close-icon {
    position: relative;
    display: block;
    width: 20px;
    height: 20px;
  }

  .mobile-nav__close-icon::before,
  .mobile-nav__close-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--color-text-primary);
  }

  .mobile-nav__close-icon::before {
    transform: rotate(45deg);
  }

  .mobile-nav__close-icon::after {
    transform: rotate(-45deg);
  }

  .mobile-nav__list {
    list-style: none;
    padding: 0;
    margin: 40px 0 0;
  }

  .mobile-nav__item {
    margin-bottom: 20px;
  }

  .mobile-nav__link {
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-text-primary);
  }

  .mobile-nav__dropdown {
    list-style: none;
    padding: 0 0 0 20px;
    margin: 10px 0 0;
  }

  .mobile-nav__dropdown-item {
    margin-bottom: 10px;
  }

  .mobile-nav__dropdown-link {
    font-size: 14px;
    color: var(--color-text-primary);
  }

  @media screen and (max-width: 989px) {
    .medium-down--show {
      display: block;
    }

    .medium-down--hide {
      display: none;
    }

    .mobile-nav-toggle {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
    }

    .mobile-nav {
      display: block;
    }

    .header-nav-container {
      display: none;
    }

    .header-top-container {
      padding: 15px 0;
    }

    .header-wrapper,
    .nav-wrapper {
      padding: 0 30px;
    }

    .header-sale-announcement {
      margin-left: 15px;
      min-width: 120px;
      min-height: 18px;
      max-width: 200px;
    }

    .sale-announcement__text {
      font-size: 13px;
    }
  }

  @media screen and (max-width: 749px) {
    .header-wrapper,
    .nav-wrapper {
      padding: 0 20px;
    }

    .header-sale-announcement {
      margin-left: 10px;
      min-width: 100px;
      min-height: 16px;
      max-width: 150px;
    }

    .sale-announcement__text {
      font-size: 12px;
    }
  }

  @media screen and (max-width: 480px) {
    .header-sale-announcement {
      display: none;
    }

    .header-wrapper,
    .nav-wrapper {
      padding: 0 15px;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Mobile navigation toggle
    const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
    const mobileNavClose = document.querySelector('.mobile-nav__close');
    const mobileNav = document.getElementById('MobileNav');

    if (mobileNavToggle && mobileNavClose && mobileNav) {
      mobileNavToggle.addEventListener('click', function() {
        const expanded = mobileNav.getAttribute('aria-hidden') === 'true' ? 'false' : 'true';
        mobileNav.setAttribute('aria-hidden', expanded === 'false' ? 'false' : 'true');
        mobileNavToggle.setAttribute('aria-expanded', expanded);
      });

      mobileNavClose.addEventListener('click', function() {
        mobileNav.setAttribute('aria-hidden', 'true');
        mobileNavToggle.setAttribute('aria-expanded', 'false');
      });
    }

    // Mobile dropdown toggles
    const mobileNavItems = document.querySelectorAll('.mobile-nav__item');

    mobileNavItems.forEach(item => {
      const dropdown = item.querySelector('.mobile-nav__dropdown');
      if (dropdown) {
        const link = item.querySelector('.mobile-nav__link');

        link.addEventListener('click', function(e) {
          e.preventDefault();
          dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        });
      }
    });

    // Rotating sale announcements
    const saleAnnouncementContainer = document.querySelector('.header-sale-announcement');
    if (saleAnnouncementContainer) {
      const announcements = saleAnnouncementContainer.querySelectorAll('.sale-announcement__item');
      if (announcements.length > 1) {
        const rotationSpeed = parseInt(saleAnnouncementContainer.getAttribute('data-rotation-speed')) || 5000;
        let currentIndex = 0;

        // Function to rotate announcements
        function rotateAnnouncements() {
          // Hide current announcement
          announcements[currentIndex].classList.remove('active');

          // Move to next announcement
          currentIndex = (currentIndex + 1) % announcements.length;

          // Show next announcement
          announcements[currentIndex].classList.add('active');
        }

        // Start rotation
        setInterval(rotateAnnouncements, rotationSpeed);
      }
    }

    // Fixed header on scroll
    const headerTopContainer = document.querySelector('.header-top-container');
    const headerNavContainer = document.getElementById('headerNavContainer');
    let lastScrollTop = 0;
    let headerHeight = headerTopContainer ? headerTopContainer.offsetHeight : 0;

    function handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // Add fixed class when scrolling down past the header height
      if (scrollTop > headerHeight) {
        headerNavContainer.classList.add('fixed');
        document.body.style.paddingTop = headerNavContainer.offsetHeight + 'px';
      } else {
        headerNavContainer.classList.remove('fixed');
        document.body.style.paddingTop = '0';
      }

      lastScrollTop = scrollTop;
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Recalculate header height on resize
    window.addEventListener('resize', function() {
      headerHeight = headerTopContainer ? headerTopContainer.offsetHeight : 0;
      if (!headerNavContainer.classList.contains('fixed')) {
        document.body.style.paddingTop = '0';
      }
    });
  });
</script>


</div>

    <main role="main" id="MainContent" class="main-content full-width">
      <div id="shopify-section-hero-carousel" class="shopify-section"><div class="hero-carousel"
     data-section-id="hero-carousel"
     data-section-type="hero-carousel"
     data-slides-per-view="1"
     data-slides-spacing="0"
     data-transition-type="fade"
     data-transition-speed="500">
  <div class="hero-carousel__wrapper">
    <div id="hero-carousel-hero-carousel" class="hero-carousel__slider">
      
        
          <div class="hero-carousel__slide"  data-block-id="slide_ayBV3E">
            <div class="hero-carousel__slide-wrapper hero-carousel__slide-wrapper--center" style="--desktop-height: 390px; --mobile-height: 300px;">
              
                <div class="hero-carousel__image-container">
                  <img
                    srcset="/cdn/shop/files/a-dirt-road-below-the-mountains_375x.jpg?v=1746913445 375w,/cdn/shop/files/a-dirt-road-below-the-mountains_750x.jpg?v=1746913445 750w,/cdn/shop/files/a-dirt-road-below-the-mountains_1100x.jpg?v=1746913445 1100w,/cdn/shop/files/a-dirt-road-below-the-mountains_1500x.jpg?v=1746913445 1500w,/cdn/shop/files/a-dirt-road-below-the-mountains_2200x.jpg?v=1746913445 2200w,"
                    src="/cdn/shop/files/a-dirt-road-below-the-mountains_1500x.jpg?v=1746913445"
                    sizes="100vw"
                    alt=""
                    width="2731"
                    height="4096"
                    loading="eager"
                    class="hero-carousel__image hero-carousel__image--overlay"
                  >
                </div>
              

              <div class="hero-carousel__content">
                
                  <div class="hero-carousel__subtitle" style="color: #ffffff;">
                    New
                  </div>
                

                
                  <h2 class="hero-carousel__heading" style="color: #ffffff;">
                    Welcome to our store
                  </h2>
                

                
                  <div class="hero-carousel__text" style="color: #ffffff;">
                    <p>Add a welcome message to your customers</p>
                  </div>
                

                
              </div>
            </div>
          </div>
        
          <div class="hero-carousel__slide"  data-block-id="slide_MbnAPr">
            <div class="hero-carousel__slide-wrapper hero-carousel__slide-wrapper--center" style="--desktop-height: 390px; --mobile-height: 300px;">
              
                <div class="hero-carousel__image-container">
                  <img
                    srcset="/cdn/shop/files/WEBSITE-OFFER-BANNER_375x.jpg?v=1746821705 375w,/cdn/shop/files/WEBSITE-OFFER-BANNER_750x.jpg?v=1746821705 750w,/cdn/shop/files/WEBSITE-OFFER-BANNER_1100x.jpg?v=1746821705 1100w,/cdn/shop/files/WEBSITE-OFFER-BANNER_1500x.jpg?v=1746821705 1500w,"
                    src="/cdn/shop/files/WEBSITE-OFFER-BANNER_1500x.jpg?v=1746821705"
                    sizes="100vw"
                    alt=""
                    width="1920"
                    height="600"
                    loading="lazy"
                    class="hero-carousel__image hero-carousel__image--overlay"
                  >
                </div>
              

              <div class="hero-carousel__content">
                

                

                

                
              </div>
            </div>
          </div>
        
      
    </div>

    
      <div class="hero-carousel__controls">
        <div class="hero-carousel__arrows">
          <button type="button" class="hero-carousel__arrow hero-carousel__arrow--prev" data-carousel-prev aria-label="Previous slide">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/>
            </svg>
          </button>
          <button type="button" class="hero-carousel__arrow hero-carousel__arrow--next" data-carousel-next aria-label="Next slide">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"/>
            </svg>
          </button>
        </div>
        <div class="hero-carousel__dots" data-carousel-dots></div>
      </div>
    
  </div>
</div>

<style>
  .hero-carousel {
    margin-bottom: 0;
    overflow: hidden;
  }

  .hero-carousel__wrapper {
    position: relative;
  }

  .hero-carousel__slider {
    display: flex;
    transition: transform 0.5s ease;
    width: 100%;
    height: auto;
  }

  .hero-carousel__slide {
    flex: 0 0 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 0;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    z-index: 1;
    pointer-events: none;
  }

  .hero-carousel__slide.active {
    opacity: 1;
    z-index: 2;
    position: relative;
    pointer-events: auto;
  }

  /* Transition types */
  .hero-carousel[data-transition-type="fade"] .hero-carousel__slide {
    transition-property: opacity;
    transition-timing-function: ease;
  }

  .hero-carousel[data-transition-type="slide"] .hero-carousel__slide {
    transition-property: transform, opacity;
    transition-timing-function: ease;
    transform: translateX(100%);
  }

  .hero-carousel[data-transition-type="slide"] .hero-carousel__slide.active {
    transform: translateX(0);
  }

  .hero-carousel[data-transition-type="slide"] .hero-carousel__slide.prev {
    transform: translateX(-100%);
  }

  .hero-carousel[data-transition-type="none"] .hero-carousel__slide {
    transition: none;
  }

  /* Slides per view settings */
  .hero-carousel[data-slides-per-view="1"] .hero-carousel__slide.active {
    flex: 0 0 100%;
    width: 100%;
  }

  .hero-carousel[data-slides-per-view="2"] .hero-carousel__slide.active {
    flex: 0 0 50%;
    width: 50%;
    position: relative;
    display: inline-block;
    vertical-align: top;
  }

  .hero-carousel[data-slides-per-view="3"] .hero-carousel__slide.active {
    flex: 0 0 33.333%;
    width: 33.333%;
    position: relative;
    display: inline-block;
    vertical-align: top;
  }

  /* For multiple slides per view, we need to adjust the container */
  .hero-carousel[data-slides-per-view="2"] .hero-carousel__slider,
  .hero-carousel[data-slides-per-view="3"] .hero-carousel__slider {
    display: block;
    white-space: nowrap;
    font-size: 0; /* Remove space between inline-block elements */
  }

  /* Spacing between slides */
  .hero-carousel[data-slides-spacing="0"] .hero-carousel__slide {
    padding: 0;
  }

  .hero-carousel[data-slides-spacing="5"] .hero-carousel__slide {
    padding: 0 2.5px;
  }

  .hero-carousel[data-slides-spacing="10"] .hero-carousel__slide {
    padding: 0 5px;
  }

  .hero-carousel[data-slides-spacing="15"] .hero-carousel__slide {
    padding: 0 7.5px;
  }

  .hero-carousel[data-slides-spacing="20"] .hero-carousel__slide {
    padding: 0 10px;
  }

  .hero-carousel[data-slides-spacing="25"] .hero-carousel__slide {
    padding: 0 12.5px;
  }

  .hero-carousel[data-slides-spacing="30"] .hero-carousel__slide {
    padding: 0 15px;
  }

  /* Mobile always shows 1 slide */
  @media screen and (max-width: 749px) {
    .hero-carousel .hero-carousel__slide {
      flex: 0 0 100%;
      width: 100%;
    }
  }

  .hero-carousel__slide-wrapper {
    position: relative;
    overflow: hidden;
    height: var(--desktop-height);
    width: 100%;
    border-radius: var(--border-radius, 0);
  }

  .hero-carousel__image-container {
    position: relative;
    height: 100%;
    width: 100%;
  }

  .hero-carousel__placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .hero-carousel__placeholder-svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    max-height: 500px;
    opacity: 0.2;
  }

  .hero-carousel__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.8s ease;
  }

  .hero-carousel__slide:hover .hero-carousel__image {
    transform: scale(1.05);
  }

  .hero-carousel__image--overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .hero-carousel__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 90%;
    max-width: 800px;
    z-index: 1;
    color: white;
  }

  .hero-carousel__slide-wrapper--center .hero-carousel__content {
    text-align: center;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .hero-carousel__slide-wrapper--left .hero-carousel__content {
    text-align: left;
    left: 10%;
    transform: translateY(-50%);
  }

  .hero-carousel__slide-wrapper--right .hero-carousel__content {
    text-align: right;
    right: 10%;
    left: auto;
    transform: translateY(-50%);
  }

  .hero-carousel__subtitle {
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 15px;
  }

  .hero-carousel__heading {
    font-size: 3.5rem;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.1;
  }

  .hero-carousel__text {
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.1rem;
  }

  .hero-carousel__buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 30px;
  }

  .hero-carousel__slide-wrapper--center .hero-carousel__buttons {
    justify-content: center;
  }

  .hero-carousel__slide-wrapper--right .hero-carousel__buttons {
    justify-content: flex-end;
  }

  .hero-carousel__controls {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 2;
  }

  .hero-carousel__arrows {
    display: flex;
    gap: 10px;
  }

  .hero-carousel__arrow {
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .hero-carousel__arrow:hover {
    background-color: white;
  }

  .hero-carousel__arrow svg {
    width: 12px;
    height: 12px;
  }

  .hero-carousel__dots {
    display: flex;
    gap: 8px;
  }

  .hero-carousel__dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .hero-carousel__dot--active {
    background-color: white;
  }

  @media screen and (max-width: 989px) {
    .hero-carousel__heading {
      font-size: 2.5rem;
    }
  }

  @media screen and (max-width: 749px) {
    .hero-carousel__slide-wrapper {
      height: var(--mobile-height);
    }

    .hero-carousel__slide-wrapper--left .hero-carousel__content,
    .hero-carousel__slide-wrapper--right .hero-carousel__content {
      left: 50%;
      right: auto;
      transform: translate(-50%, -50%);
      text-align: center;
    }

    .hero-carousel__heading {
      font-size: 2rem;
    }

    .hero-carousel__text {
      font-size: 1rem;
    }

    .hero-carousel__buttons {
      flex-direction: column;
      width: 100%;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    initHeroCarousel('hero-carousel');
  });

  // Global variables
  let autoplayTimer = null;
  let currentSlideIndex = 0;

  function initHeroCarousel(sectionId) {
    const carousel = document.querySelector(`.hero-carousel[data-section-id="${sectionId}"]`);
    if (!carousel) return;

    // Clear any existing autoplay
    if (autoplayTimer) {
      clearInterval(autoplayTimer);
      autoplayTimer = null;
    }

    // Get carousel elements
    const slider = carousel.querySelector('.hero-carousel__slider');
    const slides = Array.from(carousel.querySelectorAll('.hero-carousel__slide'));
    const prevButton = carousel.querySelector('[data-carousel-prev]');
    const nextButton = carousel.querySelector('[data-carousel-next]');
    const dotsContainer = carousel.querySelector('[data-carousel-dots]');

    // If no slides, exit
    if (slides.length === 0) return;

    // Get settings from data attributes
    const slidesPerView = parseInt(carousel.getAttribute('data-slides-per-view')) || 1;
    const slidesSpacing = parseInt(carousel.getAttribute('data-slides-spacing')) || 0;
    const transitionType = carousel.getAttribute('data-transition-type') || 'fade';
    const transitionSpeed = parseInt(carousel.getAttribute('data-transition-speed')) || 500;

    // Apply transition speed to slides
    slides.forEach(slide => {
      slide.style.transitionDuration = `${transitionSpeed}ms`;
    });

    // Check if we're in the theme editor
    const isInThemeEditor = window.Shopify && window.Shopify.designMode;

    // Only enable autoplay if it's enabled in settings and not in the theme editor
    const shouldAutoplay = true && !isInThemeEditor;

    // Set up the carousel
    setupCarousel();
    setupDots();
    setupNavigation();

    // Start autoplay if enabled
    if (shouldAutoplay) {
      startAutoplay();
    }

    // Function to set up the carousel
    function setupCarousel() {
      // Reset current slide index
      currentSlideIndex = 0;

      // Set initial slide positions
      updateSlides();
    }

    // Function to update slides based on current index
    function updateSlides() {
      let prevIndex = currentSlideIndex - 1;
      if (prevIndex < 0) prevIndex = slides.length - 1;

      slides.forEach((slide, index) => {
        // Remove all classes first
        slide.classList.remove('active', 'prev');

        // For visible slides
        if (index >= currentSlideIndex && index < currentSlideIndex + slidesPerView) {
          // Add active class for current slides
          slide.classList.add('active');
        } else if (transitionType === 'slide' && index === prevIndex) {
          // Add prev class for slide transition
          slide.classList.add('prev');
        }
      });

      // Update dots
      updateDots();
    }

    // Function to go to a specific slide
    function goToSlide(index) {
      // Handle loop
      if (index < 0) {
        index = slides.length - slidesPerView;
      } else if (index > slides.length - slidesPerView) {
        index = 0;
      }

      currentSlideIndex = index;
      updateSlides();
    }

    // Function to go to the next slide
    function nextSlide() {
      goToSlide(currentSlideIndex + 1);
    }

    // Function to go to the previous slide
    function prevSlide() {
      goToSlide(currentSlideIndex - 1);
    }

    // Function to set up navigation
    function setupNavigation() {
      if (prevButton) {
        prevButton.addEventListener('click', function() {
          prevSlide();
          if (shouldAutoplay) {
            stopAutoplay();
            startAutoplay();
          }
        });
      }

      if (nextButton) {
        nextButton.addEventListener('click', function() {
          nextSlide();
          if (shouldAutoplay) {
            stopAutoplay();
            startAutoplay();
          }
        });
      }
    }

    // Function to set up dots
    function setupDots() {
      if (!dotsContainer) return;

      // Clear existing dots
      dotsContainer.innerHTML = '';

      // Create dots
      slides.forEach((_, index) => {
        if (index <= slides.length - slidesPerView) {
          const dot = document.createElement('button');
          dot.classList.add('hero-carousel__dot');
          dot.setAttribute('aria-label', `Go to slide ${index + 1}`);
          dot.addEventListener('click', function() {
            goToSlide(index);
            if (shouldAutoplay) {
              stopAutoplay();
              startAutoplay();
            }
          });
          dotsContainer.appendChild(dot);
        }
      });

      updateDots();
    }

    // Function to update dots
    function updateDots() {
      if (!dotsContainer) return;

      const dots = Array.from(dotsContainer.querySelectorAll('.hero-carousel__dot'));
      dots.forEach((dot, index) => {
        if (index === currentSlideIndex) {
          dot.classList.add('hero-carousel__dot--active');
        } else {
          dot.classList.remove('hero-carousel__dot--active');
        }
      });
    }

    // Function to start autoplay
    function startAutoplay() {
      autoplayTimer = setInterval(() => {
        nextSlide();
      }, 5000);
    }

    // Function to stop autoplay
    function stopAutoplay() {
      if (autoplayTimer) {
        clearInterval(autoplayTimer);
        autoplayTimer = null;
      }
    }

    // Public API
    return {
      goToSlide,
      nextSlide,
      prevSlide,
      startAutoplay,
      stopAutoplay
    };
  }

  // Handle Shopify theme editor events
  document.addEventListener('shopify:section:load', function(event) {
    if (event.detail.sectionId === 'hero-carousel') {
      setTimeout(() => {
        initHeroCarousel('hero-carousel');
      }, 50);
    }
  });

  document.addEventListener('shopify:section:unload', function(event) {
    if (event.detail.sectionId === 'hero-carousel' && autoplayTimer) {
      clearInterval(autoplayTimer);
      autoplayTimer = null;
    }
  });

  document.addEventListener('shopify:section:select', function(event) {
    if (event.detail.sectionId === 'hero-carousel') {
      // Stop autoplay when section is selected in the editor
      if (autoplayTimer) {
        clearInterval(autoplayTimer);
        autoplayTimer = null;
      }

      // Reinitialize to apply any setting changes
      initHeroCarousel('hero-carousel');
    }
  });

  document.addEventListener('shopify:block:select', function(event) {
    if (event.detail.sectionId === 'hero-carousel') {
      // Find the slide index
      const carousel = document.querySelector(`.hero-carousel[data-section-id="${event.detail.sectionId}"]`);
      if (!carousel) return;

      const slides = Array.from(carousel.querySelectorAll('.hero-carousel__slide'));
      const selectedSlide = slides.find(slide => slide.getAttribute('data-block-id') === event.detail.blockId);
      if (!selectedSlide) return;

      const slideIndex = slides.indexOf(selectedSlide);

      // Go to the selected slide
      const carouselInstance = initHeroCarousel('hero-carousel');
      if (carouselInstance) {
        carouselInstance.goToSlide(slideIndex);
      }

      // Stop autoplay
      if (autoplayTimer) {
        clearInterval(autoplayTimer);
        autoplayTimer = null;
      }
    }
  });

  // Special handler for settings changes in the theme editor
  if (window.Shopify && window.Shopify.designMode) {
    document.addEventListener('shopify:section:render', function(event) {
      if (event.detail.sectionId === 'hero-carousel') {
        setTimeout(() => {
          initHeroCarousel('hero-carousel');
        }, 100);
      }
    });
  }
</script>


</div>
<div id="shopify-section-logo-carousel" class="shopify-section"><div class="logo-carousel"
  data-section-id="logo-carousel"
  data-section-type="logo-carousel"
  data-slides-per-view="5"
  data-slides-per-view-tablet="3"
  data-slides-per-view-mobile="2"
  data-autoplay-speed="3000">
  <div class="page-width">
    
      




<h2 class="section-heading ">
  <span class="section-heading__outline" style="--heading-outline-color: #000000;">Featured</span>
  <span class="section-heading__filled" style="--heading-filled-color: #000000;">Stories</span>
</h2>

    

    

    <div class="logo-carousel__wrapper">
      <div id="logo-carousel-logo-carousel" class="keen-slider logo-carousel__slider">
        
          <div class="keen-slider__slide logo-carousel__slide" >
            
              <div class="logo-carousel__placeholder">
                <svg class="logo-carousel__placeholder-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 525.5 525.5"><path d="M324.5 212.7H203c-1.6 0-2.8 1.3-2.8 2.8V308c0 1.6 1.3 2.8 2.8 2.8h121.6c1.6 0 2.8-1.3 2.8-2.8v-92.5c0-1.6-1.3-2.8-2.9-2.8zm1.1 95.3c0 .6-.5 1.1-1.1 1.1H203c-.6 0-1.1-.5-1.1-1.1v-92.5c0-.6.5-1.1 1.1-1.1h121.6c.6 0 1.1.5 1.1 1.1V308z"/><path d="M210.4 299.5H240v.1s.1 0 .2-.1h75.2v-76.2h-105v76.2zm1.8-7.2l20-20c1.6-1.6 3.8-2.5 6.1-2.5s4.5.9 6.1 2.5l1.5 1.5 16.8 16.8c-12.9 3.3-20.7 6.3-22.8 7.2h-27.7v-5.5zm101.5-10.1c-20.1 1.7-36.7 4.8-49.1 7.9l-16.9-16.9 26.3-26.3c1.6-1.6 3.8-2.5 6.1-2.5s4.5.9 6.1 2.5l27.5 27.5v7.8zm-68.9 15.5c9.7-3.5 33.9-10.9 68.9-13.8v13.8h-68.9zm68.9-72.7v46.8l-26.2-26.2c-1.9-1.9-4.5-3-7.3-3s-5.4 1.1-7.3 3l-26.3 26.3-.9-.9c-1.9-1.9-4.5-3-7.3-3s-5.4 1.1-7.3 3l-18.8 18.8V225h101.4z"/><path d="M232.8 254c4.6 0 8.3-3.7 8.3-8.3s-3.7-8.3-8.3-8.3-8.3 3.7-8.3 8.3 3.7 8.3 8.3 8.3zm0-14.9c3.6 0 6.6 2.9 6.6 6.6s-2.9 6.6-6.6 6.6-6.6-2.9-6.6-6.6 3-6.6 6.6-6.6z"/></svg>
              </div>
            
          </div>
        
      </div>

      
    </div>
  </div>
</div>

<style>
  .logo-carousel {
    margin: var(--spacing-extra-loose) 0;
  }

  .logo-carousel__wrapper {
    position: relative;
    padding: 0 40px;
  }

  .logo-carousel__slider {
    overflow: visible;
  }

  .logo-carousel__slide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-base);
    height: 100px;
  }

  .logo-carousel__logo-wrapper {
    max-width: 100%;
    max-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo-carousel__logo {
    max-width: 100%;
    max-height: 80px;
    object-fit: contain;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .logo-carousel__slide:hover .logo-carousel__logo {
    opacity: 1;
  }

  .logo-carousel__placeholder {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo-carousel__placeholder-svg {
    width: 100%;
    height: 100%;
    max-width: 120px;
    opacity: 0.3;
  }

  .logo-carousel__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
  }

  .logo-carousel__arrow {
    background-color: white;
    border: 1px solid var(--color-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
  }

  .logo-carousel__arrow:hover {
    background-color: var(--color-background-light);
  }

  .logo-carousel__arrow svg {
    width: 12px;
    height: 12px;
  }

  @media screen and (max-width: 749px) {
    .logo-carousel__wrapper {
      padding: 0 30px;
    }

    .logo-carousel__slide {
      height: 80px;
    }

    .logo-carousel__logo {
      max-height: 60px;
    }

    .logo-carousel__arrow {
      width: 30px;
      height: 30px;
    }
  }
</style>

<script>
  // Logo carousel will be initialized by theme-utilities.js
  // All configuration is passed via data attributes on the carousel element
</script>


</div>
<div id="shopify-section-category-grid" class="shopify-section"><link href="/cdn/shop/t/4/assets/category-grid.css?v=19748940031596291591747033987" rel="stylesheet" type="text/css" media="all" />

<div class="category-grid-section full-width"
     style="background-color: #f9f9f9;
            padding-top: 40px;
            padding-bottom: 40px;">
  <div class="page-width">
    
      <div class="section-header text-center">
        <h2 class="section-title">CATEGORIES</h2>
        
      </div>
    

    
    

    <div class="category-grid category-grid--4-col-desktop category-grid--2-col-mobile" style="gap: 16px;">
      
        
          
            
            <div class="category-item" >
              <a href="/collections/automated-collection" class="category-item__link">
                <div class="category-item__image-container">
                  
                    <img
                      srcset="/cdn/shop/files/model-wearing-leather-jacket-and-tan-pants_400x400_crop_center.jpg?v=1746890540 1x, /cdn/shop/files/model-wearing-leather-jacket-and-tan-pants_800x800_crop_center.jpg?v=1746890540 2x"
                      src="/cdn/shop/files/model-wearing-leather-jacket-and-tan-pants_400x400_crop_center.jpg?v=1746890540"
                      alt="Automated Collection"
                      loading="lazy"
                      width="400"
                      height="400"
                      class="category-item__image"
                    >
                  
                </div>
                <div class="category-item__title">
                  
                    Adventure Ready Jerseys
                  
                </div>
              </a>
            </div>
          
        
          
            
            <div class="category-item" >
              <a href="/collections/hydrogen" class="category-item__link">
                <div class="category-item__image-container">
                  
                    <img
                      srcset="/cdn/shop/files/biker-ties-bandana_400x400_crop_center.jpg?v=1746890463 1x, /cdn/shop/files/biker-ties-bandana_800x800_crop_center.jpg?v=1746890463 2x"
                      src="/cdn/shop/files/biker-ties-bandana_400x400_crop_center.jpg?v=1746890463"
                      alt="Hydrogen"
                      loading="lazy"
                      width="400"
                      height="400"
                      class="category-item__image"
                    >
                  
                </div>
                <div class="category-item__title">
                  
                    Explore Bandanas
                  
                </div>
              </a>
            </div>
          
        
      
    </div>
  </div>
</div>


</div>
<section id="shopify-section-brand-story" class="shopify-section section section-brand-story">

<div class="brand-story" id="brand-story-brand-story" data-section-id="brand-story" data-section-type="brand-story">
  <div class="page-width">
    <div class="brand-story__container">
      

      
        <h2 class="brand-story__title">BRAND NAME</h2>
      

      
        <div class="brand-story__subtitle">BRAND TAGLINE: TRAVEL-INSPIRED SUSTAINABLE CLOTHING & MORE</div>
      

      
        <div class="brand-story__text"><p>Founded in 2013, our brand is a design house for travelers, adventurers and nature lovers. From versatile products to eco-friendly bags, our gear is made for those who roam.</p></div>
      

      
    </div>
  </div>
</div>

<style>
  .brand-story {
    padding: var(--spacing-extra-loose) 0;
    background-color: #f8f8f8;
    color: #333333;
  }

  .brand-story__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .brand-story__logo-container {
    margin-bottom: var(--spacing-base);
  }

  .brand-story__logo {
    max-width: 100%;
    height: auto;
  }

  .brand-story__title {
    font-family: var(--font-heading);
    font-size: var(--font-size-h1);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-tight);
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #000000;
  }

  .brand-story__subtitle {
    font-family: var(--font-body);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-base);
    max-width: 90%;
  }

  .brand-story__text {
    font-family: var(--font-body);
    font-size: var(--font-size-base);
    line-height: 1.6;
    margin-bottom: var(--spacing-base);
    max-width: 90%;
  }

  .brand-story__button-container {
    margin-top: var(--spacing-base);
  }

  .brand-story__button {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #ffffff;
    min-width: 180px;
    transition: all 0.3s ease;
  }

  .brand-story__button:hover {
    background-color: #e6e6e6;
    color: #000000;
  }

  @media screen and (max-width: 749px) {
    .brand-story__title {
      font-size: calc(var(--font-size-h1) * 0.8);
    }

    .brand-story__subtitle,
    .brand-story__text {
      max-width: 100%;
    }
  }
</style>


</section>
<div id="shopify-section-horizontal-scroll-text" class="shopify-section"><div class="horizontal-scroll-section" id="horizontalScrollSection"
  data-section-id="horizontal-scroll-text"
  data-section-type="horizontal-scroll-text"
  style="background-color: #f8f8f8; height: 30vh;">

  <div class="horizontal-scroll-container">
    <div class="horizontal-scroll-content" id="horizontalScrollContent">
      <div class="horizontal-scroll-text">
        
        <h2 class="horizontal-scroll-heading" style="color: #000000;">
          
            <span class="scroll-word">LIFE</span>
          
            <span class="scroll-word">IS</span>
          
            <span class="scroll-word">AN</span>
          
            <span class="scroll-word">ADVENTURE</span>
          
        </h2>
      </div>
    </div>
  </div>
</div>

<style>
  .horizontal-scroll-section {
    position: relative;
    width: 100%;
    min-height: 300px;
    overflow: hidden;
    display: flex;
    align-items: center;
  }

  .horizontal-scroll-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .horizontal-scroll-content {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    will-change: transform;
  }

  .horizontal-scroll-text {
    white-space: nowrap;
    padding: 0 250px;
    min-width: 100%;
  }

  .horizontal-scroll-heading {
    font-family: var(--font-heading);
    font-size: clamp(4rem, 10vw, 12rem);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: -0.02em;
    line-height: 1;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    will-change: transform;
  }

  .scroll-word {
    display: inline-block;
    margin-right: 20px;
    position: relative;
    transition: transform 0.3s ease;
  }

  .scroll-word:last-child {
    margin-right: 0;
  }

  /* Optional: Add a stylized effect to the first word */
  .scroll-word:first-child {
    position: relative;
    color: transparent;
    -webkit-text-stroke: 2px #000000;
    background: linear-gradient(#000000 0 100%) left / 0 no-repeat;
    -webkit-background-clip: text;
    background-clip: text;
  }

  /* Optional: Add a different effect to the last word */
  .scroll-word:last-child {
    color: #000000;
  }

  @media screen and (max-width: var(--breakpoint-medium)) {
    .horizontal-scroll-section {
      min-height: 200px;
    }

    .horizontal-scroll-heading {
      font-size: clamp(3rem, 8vw, 8rem);
    }

    .horizontal-scroll-text {
      padding: 0 150px;
    }

    .scroll-word {
      margin-right: 15px;
    }

    .scroll-word:first-child {
      -webkit-text-stroke: 1.5px #000000;
    }
  }

  @media screen and (max-width: var(--breakpoint-small)) {
    .horizontal-scroll-section {
      min-height: 150px;
    }

    .horizontal-scroll-heading {
      font-size: clamp(2.5rem, 7vw, 6rem);
    }

    .horizontal-scroll-text {
      padding: 0 80px;
    }

    .scroll-word {
      margin-right: 10px;
    }

    .scroll-word:first-child {
      -webkit-text-stroke: 1px #000000;
    }
  }
</style>

<script>
  // Use Intersection Observer for better performance
  document.addEventListener('DOMContentLoaded', function() {
    const section = document.getElementById('horizontalScrollSection');
    const content = document.getElementById('horizontalScrollContent');
    const words = document.querySelectorAll('.scroll-word');

    if (!section || !content) return;

    // Set initial position
    content.style.transform = 'translateX(0)';

    // Variables for scroll calculations
    let textWidth, windowWidth, scrollDistance;

    // Calculate dimensions
    function calculateDimensions() {
      textWidth = content.scrollWidth;
      windowWidth = window.innerWidth;
      scrollDistance = textWidth - windowWidth + 500;
      return { textWidth, windowWidth, scrollDistance };
    }

    // Initial calculation
    calculateDimensions();

    // Function to update horizontal scroll position based on vertical scroll
    function updateHorizontalScroll(entries) {
      // Use the first entry (our section)
      const entry = entries[0];

      if (!entry.isIntersecting && entry.intersectionRatio === 0) {
        // Section is not visible at all
        return;
      }

      // Get the section's position relative to the viewport
      const rect = section.getBoundingClientRect();
      const sectionTop = rect.top;
      const sectionHeight = rect.height;
      const windowHeight = window.innerHeight;

      // Calculate how far through the section we've scrolled (0 to 1)
      let scrollProgress = 1 - (sectionTop + sectionHeight) / (windowHeight + sectionHeight);
      scrollProgress = Math.max(0, Math.min(1, scrollProgress)); // Clamp between 0 and 1

      // Apply the horizontal scroll with easing
      const translateX = -scrollProgress * scrollDistance;

      // Use transform with will-change already set in CSS
      content.style.transform = `translateX(${translateX}px)`;

      // Optimize word animations by batching DOM operations
      // Only animate words every other frame for performance
      if (window.scrollAnimationFrame % 2 === 0) {
        words.forEach((word, index) => {
          // Calculate a slight offset for each word to create a staggered effect
          const wordProgress = Math.max(0, Math.min(1, scrollProgress * 1.5 - index * 0.1));

          // Apply subtle scaling and rotation based on scroll position
          const scale = 1 + (wordProgress * 0.1);
          const yOffset = Math.sin(wordProgress * Math.PI) * 10;

          word.style.transform = `translateY(${yOffset}px) scale(${scale})`;
        });
      }
      window.scrollAnimationFrame++;
    }

    // Initialize animation frame counter
    window.scrollAnimationFrame = 0;

    // Create the Intersection Observer
    const observer = new IntersectionObserver(
      (entries) => {
        // Use requestAnimationFrame for smoother scrolling
        window.requestAnimationFrame(() => {
          updateHorizontalScroll(entries);
        });
      },
      {
        // Watch the section with these options
        root: null, // viewport
        rootMargin: "100px 0px", // start observing slightly before the section comes into view
        threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1] // multiple thresholds for smoother transitions
      }
    );

    // Start observing the section
    observer.observe(section);

    // Also listen for scroll events when the section is in view for smoother animation
    let isInView = false;
    let ticking = false;

    // Check if section is in view
    function checkIfInView() {
      const rect = section.getBoundingClientRect();
      isInView = rect.top < window.innerHeight && rect.bottom > 0;
    }

    // Scroll handler with throttling
    window.addEventListener('scroll', function() {
      checkIfInView();

      if (isInView && !ticking) {
        window.requestAnimationFrame(function() {
          updateHorizontalScroll([{isIntersecting: true, intersectionRatio: 0.5}]);
          ticking = false;
        });
        ticking = true;
      }
    });

    // Efficient resize handler with debouncing
    let resizeTimer;
    window.addEventListener('resize', function() {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(function() {
        // Recalculate dimensions
        calculateDimensions();

        // Update position if in view
        if (isInView) {
          updateHorizontalScroll([{isIntersecting: true, intersectionRatio: 0.5}]);
        }
      }, 250);
    });

    // Initial check
    checkIfInView();
  });
</script>


</div>
<div id="shopify-section-tabbed-collection" class="shopify-section"><div class="tabbed-collection" data-section-id="tabbed-collection" data-section-type="tabbed-collection">
  <div class="page-width">
    

    

    
    
    
    

    <link href="/cdn/shop/t/4/assets/product-card-styled.css?v=143601613340972623931746989614" rel="stylesheet" type="text/css" media="all" />

<div class="tabbed-products" data-tabbed-products-id="tabbed-collection">
  
    <div class="section-header text-center">
      <h2 class="section-title">Shop by Category</h2>
    </div>
  

  
    <div class="tabbed-products__tabs-container">
      <div class="tabbed-products__tabs" role="tablist">
        
          

              <button
                id="tab-tabbed-collection-1"
                class="tabbed-products__tab tabbed-products__tab--active"
                role="tab"
                aria-selected="true"
                aria-controls="tabpanel-tabbed-collection-1"
                data-collection="automated-collection"
              >
                Automated Collection
              </button>
            
          

              <button
                id="tab-tabbed-collection-2"
                class="tabbed-products__tab"
                role="tab"
                aria-selected="false"
                aria-controls="tabpanel-tabbed-collection-2"
                data-collection="hydrogen"
              >
                Hydrogen
              </button>
            
          
        
      </div>
    </div>

    <div class="tabbed-products__content">
      
        

            <div
              id="tabpanel-tabbed-collection-1"
              class="tabbed-products__panel tabbed-products__panel--active"
              role="tabpanel"
              aria-labelledby="tab-tabbed-collection-1"
              
            >
              <div class="product-grid-styled" style="--products-per-row: 4;">
  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-complete-snowboard" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/Main_589fc064-24a2-4236-9eaf-13b2bd35d21d_400x400_crop_center.jpg?v=1684772912"
            srcset="/cdn/shop/products/Main_589fc064-24a2-4236-9eaf-13b2bd35d21d_400x400_crop_center.jpg?v=1684772912 1x, /cdn/shop/products/Main_589fc064-24a2-4236-9eaf-13b2bd35d21d_800x800_crop_center.jpg?v=1684772912 2x"
            alt="Top and bottom view of a snowboard. The top view shows abstract circles and lines in shades of teal.
          The bottom view shows abstract circles and lines in shades of purple and blue with the text “SHOPIFY” in a
          sans serif typeface on top."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-complete-snowboard" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Complete Snowboard</h3>
    </a>

    
      <div class="product-card-styled__description">
        This PREMIUM snowboard is so SUPERDUPER awesome!
      </div>
    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 699
      </span>

      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-multi-location-snowboard" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/Main_0a4e9096-021a-4c1e-8750-24b233166a12_400x400_crop_center.jpg?v=1684772913"
            srcset="/cdn/shop/products/Main_0a4e9096-021a-4c1e-8750-24b233166a12_400x400_crop_center.jpg?v=1684772913 1x, /cdn/shop/products/Main_0a4e9096-021a-4c1e-8750-24b233166a12_800x800_crop_center.jpg?v=1684772913 2x"
            alt="Top and bottom view of a snowboard. The top view shows a pixelated Shopify bag logo and a pixelated
          character reviewing a clipboard with a questioning expression with a bright green-blue background. The bottom
          view is a pattern of many pixel characters with a bright green-blue background."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-multi-location-snowboard" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Multi-location Snowboard</h3>
    </a>

    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 729
      </span>

      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-collection-snowboard-liquid" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/Main_b13ad453-477c-4ed1-9b43-81f3345adfd6_400x400_crop_center.jpg?v=1684772915"
            srcset="/cdn/shop/products/Main_b13ad453-477c-4ed1-9b43-81f3345adfd6_400x400_crop_center.jpg?v=1684772915 1x, /cdn/shop/products/Main_b13ad453-477c-4ed1-9b43-81f3345adfd6_800x800_crop_center.jpg?v=1684772915 2x"
            alt="Top and bottom view of a snowboard. The top view shows a stylized scene of water, trees, mountains,
        sky and a moon in blue colours. The bottom view has a blue liquid, drippy background with the text “liquid” in
        a stylized script typeface."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-collection-snowboard-liquid" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Collection Snowboard: Liquid</h3>
    </a>

    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 749
      </span>

      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-multi-managed-snowboard" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/Main_9129b69a-0c7b-4f66-b6cf-c4222f18028a_400x400_crop_center.jpg?v=1684772913"
            srcset="/cdn/shop/products/Main_9129b69a-0c7b-4f66-b6cf-c4222f18028a_400x400_crop_center.jpg?v=1684772913 1x, /cdn/shop/products/Main_9129b69a-0c7b-4f66-b6cf-c4222f18028a_800x800_crop_center.jpg?v=1684772913 2x"
            alt="Top and bottom view of a snowboard. The top view shows an illustration with varied outlined shapes
          in black. The bottom view shows a black box character with an H pointing, and surrounded by black outlined
          illustrative elements."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-multi-managed-snowboard" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Multi-managed Snowboard</h3>
    </a>

    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 629
      </span>

      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-compare-at-price-snowboard" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/snowboard_sky_400x400_crop_center.png?v=1684772912"
            srcset="/cdn/shop/products/snowboard_sky_400x400_crop_center.png?v=1684772912 1x, /cdn/shop/products/snowboard_sky_800x800_crop_center.png?v=1684772912 2x"
            alt="Top and bottom view of a snowboard. The top view shows pixelated clouds, with the top-most one being
        the shape of the Shopify bag logo. The bottom view has a pixelated cloudy sky with blue, pink and purple
        colours."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        
          <div class="product-card-styled__tag product-card-styled__tag--sale">
            SALE
          </div>
        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-compare-at-price-snowboard" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Compare at Price Snowboard</h3>
    </a>

    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 785
      </span>

      
        <span class="product-card-styled__price-compare">Rs. 885.95</span>
      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-collection-snowboard-hydrogen" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/Main_0a40b01b-5021-48c1-80d1-aa8ab4876d3d_400x400_crop_center.jpg?v=1684772912"
            srcset="/cdn/shop/products/Main_0a40b01b-5021-48c1-80d1-aa8ab4876d3d_400x400_crop_center.jpg?v=1684772912 1x, /cdn/shop/products/Main_0a40b01b-5021-48c1-80d1-aa8ab4876d3d_800x800_crop_center.jpg?v=1684772912 2x"
            alt="Top and bottom view of a snowboard. The top view shows stylized hydrogen bonds and the bottom view
        shows “H2” in a brush script typeface."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-collection-snowboard-hydrogen" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Collection Snowboard: Hydrogen</h3>
    </a>

    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 600
      </span>

      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
</div>

<style>
  .product-grid-styled {
    display: grid;
    grid-template-columns: repeat(var(--products-per-row), 1fr);
    gap: 30px;
    margin: 30px 0;
  }

  @media screen and (max-width: 989px) {
    .product-grid-styled {
      grid-template-columns: repeat(min(var(--products-per-row), 3), 1fr);
      gap: 20px;
    }
  }

  @media screen and (max-width: 749px) {
    .product-grid-styled {
      grid-template-columns: repeat(min(var(--products-per-row), 2), 1fr);
      gap: 15px;
    }
  }

  @media screen and (max-width: 480px) {
    .product-grid-styled {
      grid-template-columns: 1fr;
    }
  }
</style>


              
            </div>
          
        

            <div
              id="tabpanel-tabbed-collection-2"
              class="tabbed-products__panel"
              role="tabpanel"
              aria-labelledby="tab-tabbed-collection-2"
              hidden
            >
              <div class="product-grid-styled" style="--products-per-row: 4;">
  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-collection-snowboard-liquid" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/Main_b13ad453-477c-4ed1-9b43-81f3345adfd6_400x400_crop_center.jpg?v=1684772915"
            srcset="/cdn/shop/products/Main_b13ad453-477c-4ed1-9b43-81f3345adfd6_400x400_crop_center.jpg?v=1684772915 1x, /cdn/shop/products/Main_b13ad453-477c-4ed1-9b43-81f3345adfd6_800x800_crop_center.jpg?v=1684772915 2x"
            alt="Top and bottom view of a snowboard. The top view shows a stylized scene of water, trees, mountains,
        sky and a moon in blue colours. The bottom view has a blue liquid, drippy background with the text “liquid” in
        a stylized script typeface."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-collection-snowboard-liquid" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Collection Snowboard: Liquid</h3>
    </a>

    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 749
      </span>

      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-collection-snowboard-oxygen" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/Main_d624f226-0a89-4fe1-b333-0d1548b43c06_400x400_crop_center.jpg?v=1684772914"
            srcset="/cdn/shop/products/Main_d624f226-0a89-4fe1-b333-0d1548b43c06_400x400_crop_center.jpg?v=1684772914 1x, /cdn/shop/products/Main_d624f226-0a89-4fe1-b333-0d1548b43c06_800x800_crop_center.jpg?v=1684772914 2x"
            alt="Top and bottom view of a snowboard. The top view shows a stylized scene of trees, mountains, sky and
        a sun in red colours. The bottom view has blue wavy lines in the background with the text “Oxygen” in a
        stylized script typeface."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-collection-snowboard-oxygen" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Collection Snowboard: Oxygen</h3>
    </a>

    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 1025
      </span>

      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
    

<div class="product-card-styled">
  <div class="product-card-styled__image-container">
    <a href="/products/the-collection-snowboard-hydrogen" class="product-card-styled__link">
      <div class="product-card-styled__image-wrapper">
        
          <img
            src="/cdn/shop/products/Main_0a40b01b-5021-48c1-80d1-aa8ab4876d3d_400x400_crop_center.jpg?v=1684772912"
            srcset="/cdn/shop/products/Main_0a40b01b-5021-48c1-80d1-aa8ab4876d3d_400x400_crop_center.jpg?v=1684772912 1x, /cdn/shop/products/Main_0a40b01b-5021-48c1-80d1-aa8ab4876d3d_800x800_crop_center.jpg?v=1684772912 2x"
            alt="Top and bottom view of a snowboard. The top view shows stylized hydrogen bonds and the bottom view
        shows “H2” in a brush script typeface."
            loading="lazy"
            width="400"
            height="400"
            class="product-card-styled__image"
          >
        

        
        
        Liquid error (snippets/product-card-styled line 31): comparison of String with 1744454087 failed

        

        
      </div>
    </a>
  </div>

  <div class="product-card-styled__info">
    <a href="/products/the-collection-snowboard-hydrogen" class="product-card-styled__title-link">
      <h3 class="product-card-styled__title">The Collection Snowboard: Hydrogen</h3>
    </a>

    

    <div class="product-card-styled__price-container">
      <span class="product-card-styled__price">
        
        Rs. 600
      </span>

      
    </div>

    <div class="product-card-styled__actions">
      
        <form method="post" action="/cart/add" class="product-card-styled__form" data-ajax-cart-form>
          <input type="hidden" name="id" value="**************">
          <button type="submit" class="button product-card-styled__add-to-cart">
            ADD TO CART
          </button>
        </form>
      
    </div>
  </div>
</div>

  
</div>

<style>
  .product-grid-styled {
    display: grid;
    grid-template-columns: repeat(var(--products-per-row), 1fr);
    gap: 30px;
    margin: 30px 0;
  }

  @media screen and (max-width: 989px) {
    .product-grid-styled {
      grid-template-columns: repeat(min(var(--products-per-row), 3), 1fr);
      gap: 20px;
    }
  }

  @media screen and (max-width: 749px) {
    .product-grid-styled {
      grid-template-columns: repeat(min(var(--products-per-row), 2), 1fr);
      gap: 15px;
    }
  }

  @media screen and (max-width: 480px) {
    .product-grid-styled {
      grid-template-columns: 1fr;
    }
  }
</style>


              
            </div>
          
        
      
    </div>
  
</div>


<style>
  .tabbed-products {
    margin: var(--spacing-extra-loose) 0;
  }

  /* Section header styles - consistent with other sections */
  .section-header {
    margin-bottom: var(--spacing-base);
  }

  .section-title {
    font-family: var(--font-heading);
    font-size: var(--font-size-h2);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-tight);
  }

  .text-center {
    text-align: center;
  }

  .tabbed-products__tabs-container {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-base);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .tabbed-products__tabs-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .tabbed-products__tabs {
    display: flex;
    flex-wrap: nowrap;
    gap: var(--spacing-tight);
    padding-bottom: var(--spacing-tight);
  }

  .tabbed-products__tab {
    padding: var(--spacing-tight) var(--spacing-base);
    background: transparent;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    font-family: var(--font-body);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }

  .tabbed-products__tab:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .tabbed-products__tab--active {
    background-color: var(--color-button);
    color: var(--color-button-text);
    border-color: var(--color-button);
  }

  .tabbed-products__panel {
    display: none;
  }

  .tabbed-products__panel--active {
    display: block;
  }

  /* Using product-grid-styled instead of tabbed-products__grid */

  .tabbed-products__view-all {
    margin-top: var(--spacing-loose);
    text-align: center;
  }

  .tabbed-products__view-all .button {
    background-color: var(--color-button);
    color: var(--color-button-text);
    font-family: var(--font-body);
    font-size: var(--font-size-small);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-tight) var(--spacing-base);
    border-radius: var(--border-radius);
    transition: background-color 0.2s ease;
  }

  .tabbed-products__view-all .button:hover {
    background-color: var(--color-primary);
  }

  .tabbed-products__empty {
    padding: var(--spacing-extra-loose);
    text-align: center;
    background-color: #f7f7f7;
    border-radius: var(--border-radius);
  }

  /* Media queries for grid handled by product-grid-styled */
</style>


<script>
  document.addEventListener('DOMContentLoaded', function() {
    initTabbedProducts('tabbed-collection');
  });

  function initTabbedProducts(uniqueId) {
    const container = document.querySelector(`[data-tabbed-products-id="${uniqueId}"]`);
    if (!container) return;

    const tabs = container.querySelectorAll('.tabbed-products__tab');
    const panels = container.querySelectorAll('.tabbed-products__panel');

    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Deactivate all tabs and panels
        tabs.forEach(t => {
          t.classList.remove('tabbed-products__tab--active');
          t.setAttribute('aria-selected', 'false');
        });

        panels.forEach(p => {
          p.classList.remove('tabbed-products__panel--active');
          p.setAttribute('hidden', '');
        });

        // Activate clicked tab and its panel
        tab.classList.add('tabbed-products__tab--active');
        tab.setAttribute('aria-selected', 'true');

        const panelId = tab.getAttribute('aria-controls');
        const panel = container.querySelector(`#${panelId}`);

        if (panel) {
          panel.classList.add('tabbed-products__panel--active');
          panel.removeAttribute('hidden');
        }
      });
    });
  }
</script>

  </div>
</div>


</div>
<div id="shopify-section-full-width-video" class="shopify-section"><div class="full-width-video" data-section-id="full-width-video" data-section-type="full-width-video">
  <div class="full-width-video__wrapper">
    
    
    <div class="full-width-video__container" data-video-container>
      
        <div class="full-width-video__placeholder">
          <svg class="full-width-video__placeholder-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1052 400"><path d="M727.6 121.3c-8.3 0-15-6.7-15-15s6.7-15 15-15 15 6.7 15 15-6.7 15-15 15zm0-28.1c-7.2 0-13 5.8-13 13s5.8 13 13 13 13-5.8 13-13-5.8-13-13-13zm-183.5 62.1c-8.3 0-15-6.7-15-15s6.7-15 15-15 15 6.7 15 15-6.7 15-15 15zm0-28.1c-7.2 0-13 5.8-13 13s5.8 13 13 13 13-5.8 13-13-5.8-13-13-13zm235.5 236.1c-8.3 0-15-6.7-15-15s6.7-15 15-15 15 6.7 15 15-6.7 15-15 15zm0-28c-7.2 0-13 5.8-13 13s5.8 13 13 13 13-5.8 13-13-5.8-13-13-13zM43.9 47.9c-8.3 0-15-6.7-15-15s6.7-15 15-15 15 6.7 15 15-6.7 15-15 15zm0-28c-7.2 0-13 5.8-13 13s5.8 13 13 13 13-5.8 13-13-5.8-13-13-13zm252.3 236c-8.3 0-15-6.7-15-15s6.7-15 15-15 15 6.7 15 15-6.7 15-15 15zm0-28c-7.2 0-13 5.8-13 13s5.8 13 13 13 13-5.8 13-13-5.8-13-13-13zm706.2-190.7c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6zM764.7 283.5c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6zM525 40.9c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6zm37.7 327.7c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6zM50.9 276.5c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.5 8-8 8zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.6-6-6-6zM93.6 68.7c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6zm194.1-13.8c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm0-14c-3.3 0-6 2.7-6 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6zm534.9 45c-2.3 0-4.2-1.9-4.2-4.2s1.9-4.2 4.2-4.2c2.3 0 4.2 1.9 4.2 4.2s-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.5-3.2-3.2-3.2zm-124 31.9c-2.3 0-4.2-1.9-4.2-4.2 0-2.3 1.9-4.2 4.2-4.2s4.2 1.9 4.2 4.2c0 2.3-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.4-3.2-3.2-3.2zM544.1 44.1c-2.3 0-4.2-1.9-4.2-4.2 0-2.3 1.9-4.2 4.2-4.2 2.3 0 4.2 1.9 4.2 4.2 0 2.3-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.5-3.2-3.2-3.2zM310.2 268.5c-2.3 0-4.2-1.9-4.2-4.2s1.9-4.2 4.2-4.2c2.3 0 4.2 1.9 4.2 4.2s-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.4-3.2-3.2-3.2zm-9.1-213.3c-2.3 0-4.2-1.9-4.2-4.2 0-2.3 1.9-4.2 4.2-4.2 2.3 0 4.2 1.9 4.2 4.2 0 2.3-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.4-3.2-3.2-3.2zM26.6 64.9c-2.3 0-4.2-1.9-4.2-4.2 0-2.3 1.9-4.2 4.2-4.2 2.3 0 4.2 1.9 4.2 4.2 0 2.3-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.4-3.2-3.2-3.2zm776.9 268c-2.3 0-4.2-1.9-4.2-4.2s1.9-4.2 4.2-4.2c2.3 0 4.2 1.9 4.2 4.2s-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.5-3.2-3.2-3.2zm-234.9 13.1c-2.3 0-4.2-1.9-4.2-4.2 0-2.3 1.9-4.2 4.2-4.2 2.3 0 4.2 1.9 4.2 4.2 0 2.3-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.4-3.2-3.2-3.2zm-528.4-75c-2.3 0-4.2-1.9-4.2-4.2s1.9-4.2 4.2-4.2 4.2 1.9 4.2 4.2-1.8 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.4-3.2-3.2-3.2zm980.9-3.2c-2.3 0-4.2-1.9-4.2-4.2 0-2.3 1.9-4.2 4.2-4.2 2.3 0 4.2 1.9 4.2 4.2 0 2.3-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.4-3.2-3.2-3.2zM463.5 272c-2.3 0-4.2-1.9-4.2-4.2 0-2.3 1.9-4.2 4.2-4.2 2.3 0 4.2 1.9 4.2 4.2 0 2.3-1.9 4.2-4.2 4.2zm0-7.4c-1.8 0-3.2 1.4-3.2 3.2s1.4 3.2 3.2 3.2 3.2-1.4 3.2-3.2-1.5-3.2-3.2-3.2zm564.4-88.1L897.9 9.1c-.4-.5-1.2-.5-1.6 0L766.2 176.4c-.2.3-.3.8-.1 1.2.2.3.5.7.9.7h260.1c.4 0 .7-.3.9-.7.2-.4.1-.8-.1-1.1zm-156.5-16.7l-4.8 1.5c-1.8-13.9 2.7-25.4 13.2-34.1.2-.2.2-.5.1-.7-.2-.2-.5-.2-.7-.1-10.9 9-15.4 20.9-13.5 35.2-.5.2-1.1.4-1.7.5-1.2.4-2.3.8-3.5 1.2-.7-3.3-1.4-6.2-2.2-8.7-.1-.3-.4-.4-.6-.3-.3.1-.4.4-.3.6.8 2.5 1.5 5.4 2.2 8.7-1.7.6-3.4 1.1-5.1 1.7-5.6-22.4-9.8-21.8-35.3-18.4-3.2.4-6.8.9-10.8 1.4-4.1.5-7.1-.2-9.1-2.1-1.4-1.4-2.3-3.3-2.7-5.5l1.8-2.3c-.1.2-.2.5-.1.8.2.5.7.7 1.2.5.5-.2.7-.7.5-1.2-.2-.5-.7-.7-1.2-.5-.1 0-.2.1-.3.2l2.5-3.2c.2 2.5.9 4.6 2.4 6.1 1.5 1.4 3.5 2.2 6.3 2.2.8 0 1.7-.1 2.6-.2.3 0 .5-.3.4-.6s-.3-.5-.6-.4c-3.8.5-6.4-.1-8.1-1.7-1.5-1.5-2.1-3.9-2.2-6.6l4.2-5.4c9.4-.2 18.1-.9 26-2.1.3 0 .5-.3.4-.6 0-.3-.3-.5-.6-.4-7.5 1.2-15.9 1.9-25 2.1l1.3-1.6c.4.5 1 .8 1.6.8.2 0 .5 0 .7-.1 1.1-.4 1.6-1.6 1.2-2.6-.2-.6-.6-1-1.2-1.2l1.3-1.6c9.1-.6 19.9-1.8 29.8-4.2 22.2-5.3 29.6-19.6 35.6-31.2 4.8-9.3 8.4-16.1 18-13.8l-1.3 6.7c-.1.5.2 1.1.8 1.2h.2c.5 0 .9-.3 1-.8l1.3-6.7c3.7.9 7.1 1.6 10.3 2.2l-1.3 6.4c-.1.5.2 1.1.8 1.2h.2c.5 0 .9-.3 1-.8l1.3-6.4c3 .6 5.8 1.1 8.3 1.6.7.1 1.4.3 2.1.4l-1.3 6.5c-.1.5.2 1.1.8 1.2h.2c.5 0 .9-.3 1-.8l1.3-6.5c4 .8 7.4 1.5 10.2 2.3l-1.2 6.2c-.1.5.2 1.1.8 1.2h.2c.5 0 .9-.3 1-.8l1.2-6c5 1.8 8.2 4.3 10.2 9 0 .1.1.2.1.3 1.6 3.9 2.5 9.3 2.8 16.9-1.5.1-3.3.2-5.3.4-.3 0-.5.3-.5.5 0 .3.2.5.5.5 2-.2 3.8-.3 5.2-.4.1 1.3.1 2.7.1 4.2-3.3.2-14.3.8-26.8 3.4-20.1 4.2-34.3 11.1-42.2 20.5-5.8 7-8 15.2-6.7 24.3zm-61.6-36.2c.4 0 .7.3.9.7.2.5-.1 1.1-.6 1.3-.5.2-1.1-.1-1.3-.6l1-1.4zm87.3-112.3l61.2 78.8c-4.5-.2-8.8-.7-12.7-1.9-2.3-5.2-6-7.9-11.5-9.9l.5-2.6c.1-.5-.2-1.1-.8-1.2-.5-.1-1.1.2-1.2.8l-.5 2.4c-2.9-.9-6.3-1.6-10.2-2.3l.4-2.1c.1-.5-.2-1.1-.8-1.2-.5-.1-1.1.2-1.2.8l-.4 2.1c-.7-.1-1.4-.3-2.1-.4-2.5-.5-5.2-1-8.2-1.6l.4-2.2c.1-.5-.2-1.1-.8-1.2-.5-.1-1.1.2-1.2.8l-.4 2.2c-3.2-.6-6.6-1.4-10.3-2.2l.4-2c.1-.5-.2-1.1-.8-1.2-.5-.1-1.1.2-1.2.8l-.4 1.9c-4.8-1.1-8.3-.3-11.1 1.8l-7.3-1.5c-.4-.1-.9.1-1.1.5-.2.3-3.9 7.6-1.2 14.9-5.7 11-13.1 24.2-33.8 29.2-9.1 2.2-19 3.4-27.6 4l83.9-107.5zm-14.6 62.2c-2.4 2.5-4.4 5.9-6.3 9.7-.9-4.6.6-9.1 1.3-10.7l5 1zm-2.9 63.5c9.4-11.3 27.5-17 41-19.9 14.7-3.1 27.4-3.4 27.5-3.4.5 0 1-.5 1-1-.2-10.1-1-17.1-2.7-22.2 3.6.9 8.2 1.5 13.4 1.6l30.5 39.3c-10.9 5.2-22 9.7-33.4 13.5-15.6 5.2-25.8 5.8-36.7 6.4-11.7.6-24.8 1.4-46.9 7.9-1.1-8.4 1-15.9 6.3-22.2zm-89.5 10.4c.1.4.1.9.2 1.3l1.8-2.3 2.9-3.8c.6 1.9 1.5 3.7 2.9 5 2.4 2.4 6.1 3.3 10.7 2.7 4-.5 7.6-1 10.8-1.4 25.6-3.5 27.9-3.7 33.1 17.1-15.8 5-27.4 8.2-35.6 10.2h-15.5c-5.7-8-8.2-19.1-9.4-29.6l-1.8 2.2c1.2 9.7 3.6 19.4 8.6 27.4h-29.9l21.2-27.4c.1-.5.1-.9 0-1.4L767 177.2l23.1-29.8zm85.1 28.8l6.8-2.5.9 2.5h-7.7zm-2.9 0h-28.7c7.8-3 14.7-4.9 20.6-6.9 6.1-2.1 11.1-3.8 14.6-4.8l2.9 8.1-9.4 3.6zm152.8 0h-140l-4.4-12.1c20.6-5 33.8-5.7 47.8-6.4 2.1-.1 4.3-.2 6.5-.4 8.8-.5 30.2-5.7 59.6-20.5l-1.2-1.6c-28.9 14.5-49.9 19.6-58.5 20.1-2.2.1-4.4.2-6.5.3-14.3.7-27.8 1.4-49 6.6-.1 0-.2 0-.3.1h-.1c-3.7.9-8.9 2.8-15.4 5-7.4 2.5-16.6 5.8-26.9 8.8h-11.2c9.6-3 22.4-6.4 39.2-12 27.6-9.3 42.6-10.2 55.7-10.9 11-.6 21.4-1.2 37.2-6.5 11.7-3.9 23-8.5 34.1-13.8l1.8 2.3 1.2 1.6c.3-.1.6-.3.9-.4l.1.1 31.5 40.7-31.5-40.6c-.3.2-.6.3-.9.4l30.3 39.2z"/><path d="M855 149.4c.1.2.3.3.4.3.1 0 .2 0 .2-.1.2-.1.3-.4.2-.7-6.3-12.2-16.4-10.7-34.7-8.1-1.4.2-2.9.4-4.4.6-.3 0-.5.3-.4.6 0 .3.3.5.6.4 1.5-.2 3-.4 4.4-.6 17.9-2.6 27.8-4 33.7 7.6zm28.3-25.4c.1.1.3.2.4.2.1 0 .2 0 .3-.1 12.1-8.1 30.3-13.5 53.9-15.9.3 0 .5-.3.4-.5 0-.3-.3-.5-.5-.4-23.8 2.4-42 7.8-54.3 16-.3.2-.3.5-.2.7zm3.1-30.9c.3-.1.4-.4.3-.6-.1-.3-.4-.4-.6-.3-3 1-5.5 2.9-7.2 5.5-1.8 2.8-11.5 16.9-25.4 22.2-4.9 1.8-10.4 3.4-16.4 4.5-.3.1-.4.3-.4.6 0 .*******.4h.1c6.1-1.2 11.6-2.7 16.5-4.6 14.2-5.4 24.1-19.7 25.9-22.6 1.6-2.4 4-4.2 6.7-5.1zm4.3-.6c.4 0 .9 0 1.3.1l22.1 2.6h.1c.2 0 .5-.2.5-.4 0-.3-.2-.5-.4-.6l-22.1-2.7c-.5-.1-.9-.2-1.4-.2-.3 0-.5.3-.5.6-.*******.4.6zm26.5 2.5c0 .*******.6 4.6.5 8.3 3.9 9.2 8.4l.3 1.6c0 .*******.4h.1c.3-.1.4-.3.4-.6l-.3-1.6c-1-4.9-5.1-8.6-10.1-9.2-.2 0-.4.2-.5.4z"/><ellipse transform="rotate(-20.016 944.06 109.69)" cx="944.1" cy="109.7" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 939.31 110.263)" cx="939.4" cy="110.3" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 914.644 113.925)" cx="914.7" cy="113.9" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 924.99 112.103)" cx="925" cy="112.1" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 928.9 111.65)" cx="928.9" cy="111.7" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 909.974 115.297)" cx="910" cy="115.3" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 899.37 118.964)" cx="899.4" cy="119" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 894.725 120.853)" cx="894.7" cy="120.9" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 874.344 136.748)" cx="874.4" cy="136.8" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 881.238 129.294)" cx="881.3" cy="129.3" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 885.095 126.307)" cx="885.1" cy="126.3" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 872.082 140.004)" cx="872.1" cy="140" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 868.997 149.4)" cx="869" cy="149.4" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 868.683 152.79)" cx="868.7" cy="152.8" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 855.214 156.368)" cx="855.2" cy="156.4" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 853.88 152.703)" cx="853.9" cy="152.7" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 847.56 144.56)" cx="847.6" cy="144.6" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 843.473 142.673)" cx="843.5" cy="142.7" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 827.555 143.41)" cx="827.6" cy="143.4" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 832.317 142.79)" cx="832.3" cy="142.8" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 811.065 145.516)" cx="811.1" cy="145.5" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 816.355 144.84)" cx="816.4" cy="144.9" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 800.762 142.79)" cx="800.8" cy="142.8" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 930.208 105.774)" cx="930.3" cy="105.8" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 929.345 101.854)" cx="929.4" cy="101.9" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 927.505 98.355)" cx="927.6" cy="98.4" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 925.4 95.16)" cx="925.4" cy="95.2" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 921.753 93.307)" cx="921.8" cy="93.3" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 917.842 92.26)" cx="917.9" cy="92.3" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 913.847 91.802)" cx="913.8" cy="91.8" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 910.554 91.235)" cx="910.6" cy="91.3" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 906.513 90.77)" cx="906.5" cy="90.8" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 901.752 90.09)" cx="901.8" cy="90.1" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 897.63 89.598)" cx="897.6" cy="89.6" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 893.483 89.146)" cx="893.5" cy="89.2" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 889.072 88.807)" cx="889.1" cy="88.8" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 884.517 89.494)" cx="884.5" cy="89.5" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 880.373 91.976)" cx="880.4" cy="92" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 877.565 94.954)" cx="877.6" cy="95" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 872.024 102.866)" cx="872" cy="102.9" rx=".9" ry=".9"/><ellipse transform="rotate(-20.016 869.188 105.844)" cx="869.2" cy="105.9" rx=".9" ry=".9"/><ellipse transform="rotate(-6.8 861.102 113.444)" cx="861.2" cy="113.5" rx=".9" ry=".9"/><ellipse transform="rotate(-6.8 857.688 115.7)" cx="857.8" cy="115.7" rx=".9" ry=".9"/><ellipse transform="rotate(-6.8 848.15 119.605)" cx="848.2" cy="119.6" rx=".9" ry=".9"/><ellipse transform="rotate(-6.8 843.256 120.825)" cx="843.5" cy="120.9" rx=".9" ry=".9"/><ellipse transform="rotate(-6.8 834.034 122.542)" cx="834.4" cy="122.6" rx=".9" ry=".9"/><ellipse transform="rotate(-6.8 829.758 123.153)" cx="830.1" cy="123.2" rx=".9" ry=".9"/><ellipse transform="rotate(-6.8 818.873 124.066)" cx="819.2" cy="124.1" rx=".9" ry=".9"/><ellipse transform="rotate(-6.8 814.146 124.62)" cx="814.4" cy="124.6" rx=".9" ry=".9"/><path d="M874.7 100.6c.2 0 .5 0 .7-.1.5-.2.9-.6 1.1-1.1.2-.5.3-1 .1-1.6-.2-.5-.6-.9-1.1-1.1-.5-.2-1-.3-1.6-.1-1.1.4-1.6 1.6-1.2 ******* 1.1 1.4 2 1.4zm-.4-3c.1 0 .2-.1.4-.1.1 0 .3 0 .*******.*******.******* 0 .8-.1.2-.3.4-.6.5-.5.2-1.1-.1-1.3-.6-.2-.5.1-1.1.6-1.3zm-10.2 13.5c.*******.9.2.2 0 .5 0 .7-.1 1.1-.4 1.6-1.6 1.2-2.6-.2-.5-.6-.9-1.1-1.1-.5-.2-1-.3-1.6-.1-.5.2-.9.6-1.1 1.1-.2.5-.3 1-.1 *******.6.8 1.1 1zm0-2.3c.1-.2.3-.4.6-.5.1 0 .2-.1.4-.1.1 0 .3 0 .*******.*******.2.5-.1 1.1-.6 1.3-.3.1-.5.1-.8 0-.2-.1-.4-.3-.5-.6-.2-.2-.2-.5 0-.8zm-11.3 10.4c.2 0 .5 0 .7-.1 1.1-.4 1.6-1.6 1.2-2.6-.4-1.1-1.6-1.6-2.6-1.2-.5.2-.9.6-1.1 1.1-.2.5-.3 1-.1 ******* 1.1 1.2 1.9 1.2zm-.9-2.5c.1-.2.3-.4.6-.5.1 0 .2-.1.4-.1.4 0 .8.3 1 .7.2.5-.1 1.1-.6 1.3-.5.2-1.1-.1-1.3-.6-.3-.2-.3-.5-.1-.8zm-13.8 6.4c.*******.9.2.2 0 .5 0 .7-.1.5-.2.9-.6 1.1-1.1.2-.5.3-1 .1-1.6-.2-.5-.6-.9-1.1-1.1-.5-.2-1-.3-1.6-.1-1.1.4-1.6 1.6-1.2 2.6.2.5.6.9 1.1 1.2zm.5-2.8c.1 0 .2-.1.4-.1.1 0 .3 0 .*******.*******.******* 0 .8-.1.2-.3.4-.6.5-.3.1-.5.1-.8 0-.2-.1-.4-.3-.5-.6-.2-.6.1-1.2.6-1.3zm-13.1 1.4c-.5-.2-1-.3-1.6-.1-1.1.4-1.6 1.6-1.2 2.6.3.8 1.1 1.3 1.9 1.3.2 0 .5 0 .7-.1.5-.2.9-.6 1.1-1.1.2-.5.3-1 .1-1.6-.2-.4-.5-.8-1-1zm0 2.3c-.1.2-.3.4-.6.5-.5.2-1.1-.1-1.3-.6-.2-.5.1-1.1.6-1.3.1 0 .2-.1.4-.1.1 0 .3 0 .*******.*******.2.2.2.5 0 .8zm30.3 35.3c-1 .3-1.5 1.4-1.1 2.4.2.5.5.8 1 1 .2.1.5.2.8.2.2 0 .4 0 .6-.1.5-.2.8-.5 1-1 .2-.4.2-1 .1-1.4-.2-.5-.5-.8-1-1-.4-.3-.9-.3-1.4-.1zm1.4 2.1c-.1.2-.3.4-.5.4-.2.1-.4.1-.7 0-.2-.1-.4-.3-.4-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.1 0 .2 0 .4.1s.4.3.4.5c.******* 0 .7zm13.2-4.3c-.4-1-1.4-1.5-2.4-1.1-1 .3-1.5 1.4-1.1 2.4.2.5.5.8 1 1 .2.1.5.2.8.2.2 0 .4 0 .6-.1.5-.2.8-.5 1-1 .2-.5.3-1 .1-1.4zm-1 1c-.1.2-.3.4-.5.4s-.4.1-.7 0c-.2-.1-.4-.3-.4-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.3 0 .7.2.8.6.1.2.1.5 0 .7zm2.5-13.5c-.3-1-1.4-1.5-2.4-1.1-1 .4-1.5 1.4-1.1 2.4.3.8 1 1.2 1.7 1.2.2 0 .4 0 .6-.1 1.1-.4 1.6-1.4 1.2-2.4zm-1.4 1.4c-.2.1-.4.1-.7 0-.2-.1-.4-.3-.4-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.1 0 .2 0 .4.1s.4.3.4.5c.1.5-.1 1-.5 1.1zm8.5-12.4c.2-.4.2-1 .1-1.4-.3-1-1.4-1.5-2.4-1.1-1 .4-1.5 1.4-1.1 2.4.3.8 1 1.2 1.7 1.2.2 0 .4 0 .6-.1.5-.3.9-.6 1.1-1zm-.9-.5c-.1.2-.3.4-.5.4-.2.1-.4.1-.7 0-.2-.1-.4-.3-.4-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.3 0 .7.2.8.6.******* 0 .7zm10.5-11.3c-1 .4-1.5 1.4-1.1 2.4.2.5.5.8 1 1 .2.1.5.2.8.2.2 0 .4 0 .6-.1.5-.2.8-.5 1-1 .2-.4.2-1 .1-1.4-.4-1-1.5-1.5-2.4-1.1zm1.4 2.1c-.1.2-.3.4-.5.4-.2.1-.4.1-.7 0-.2-.1-.4-.3-.4-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.3 0 .7.2.8.6.1.2.1.4 0 .7zm14.9-8.3c-.4-.2-1-.2-1.4-.1-.5.2-.8.5-1 1-.2.4-.2 1-.1 1.4.3.8 1 1.2 1.7 1.2.2 0 .4 0 .6-.1 1-.4 1.5-1.4 1.1-2.4-.1-.4-.4-.8-.9-1zm-.5 2.5c-.4.2-.9-.1-1.1-.5-.1-.2-.1-.4 0-.7.1-.2.3-.4.5-.4.1 0 .2-.1.3-.1.1 0 .2 0 .4.1s.4.3.4.5c.2.4 0 .9-.5 1.1zm17.1-5.7c-.3-1-1.4-1.5-2.4-1.1-1 .4-1.5 1.4-1.1 2.4.3.8 1 1.2 1.7 1.2.2 0 .4 0 .6-.1 1-.3 1.5-1.4 1.2-2.4zm-1.5 1.5c-.4.2-.9-.1-1.1-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.3 0 .7.2.8.6.2.4 0 .9-.5 1.1zm14.4-4.7c-.4-.2-1-.2-1.4-.1-.5.2-.8.5-1 1-.2.4-.2 1-.1 1.4.3.8 1 1.2 1.7 1.2.2 0 .4 0 .6-.1 1-.3 1.5-1.4 1.1-2.4-.1-.4-.4-.7-.9-1zm-.5 2.5c-.4.2-.9-.1-1.1-.5-.1-.2-.1-.4 0-.7.1-.2.3-.4.5-.4.1 0 .2-.1.3-.1.1 0 .2 0 .4.1s.4.3.4.5c.2.5 0 1-.5 1.1zM853 147.5c-.2-.5-.5-.8-1-1-.4-.2-1-.2-1.4-.1-.5.2-.8.5-1 1-.2.4-.2 1-.1 1.4.2.5.5.8 1 1 .2.1.5.2.8.2.2 0 .4 0 .6-.1.9-.4 1.4-1.5 1.1-2.4zm-1.5 1.4c-.2.1-.4.1-.7 0-.2-.1-.4-.3-.4-.5-.1-.2-.1-.4 0-.7.1-.2.3-.4.5-.4.1 0 .2-.1.3-.1.1 0 .2 0 .4.1s.4.3.4.5c.2.4 0 .9-.5 1.1zm-11.4-7.2c-.3-1-1.4-1.5-2.4-1.1-1 .4-1.5 1.4-1.1 2.4.2.5.5.8 1 1 .2.1.5.2.8.2.2 0 .4 0 .6-.1.5-.2.8-.5 1-1 .3-.4.3-.9.1-1.4zm-.9 1c-.1.2-.3.4-.5.4-.2.1-.4.1-.7 0-.2-.1-.4-.3-.4-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.3 0 .7.2.8.6.******* 0 .7zm-17-.4c-.4-.2-1-.2-1.4-.1-1 .3-1.5 1.4-1.1 2.4.2.5.5.8 1 1 .2.1.5.2.8.2.2 0 .4 0 .6-.1 1-.3 1.5-1.4 1.1-2.4-.2-.4-.6-.8-1-1zm-.5 2.5c-.2.1-.4.1-.7 0-.2-.1-.4-.3-.4-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.1 0 .2 0 .4.1s.4.3.4.5c.2.5-.1 1-.5 1.1zm-14.8 0c-.4-1-1.4-1.5-2.4-1.1-1 .3-1.5 1.4-1.1 2.4.2.5.5.8 1 1 .2.1.5.2.8.2.2 0 .4 0 .6-.1.5-.2.8-.5 1-1 .2-.5.2-1 .1-1.4zm-1 1c-.1.2-.3.4-.5.4-.2.1-.4.1-.7 0-.2-.1-.4-.3-.4-.5-.2-.4.1-.9.5-1.1.1 0 .2-.1.3-.1.3 0 .7.2.8.6.1.2.1.5 0 .7zM986.7 210.2h-170c-.6 0-1 .4-1 1v170c0 .6.4 1 1 1h170c.6 0 1-.4 1-1v-170c0-.5-.4-1-1-1zm-126.5 104c2.3.8 33.8 12.3 71.3 30.6 8.6 4.2 16.7 8.4 24.1 12.3v.1c.1.1.3.2.4.2h.1c8.7 4.6 16.4 9 22.8 12.7.1.1.3.2.4.2.1 0 .2 0 .3-.1-.1.1-.1.1-.2.1 2.3 1.3 4.3 2.5 6.3 3.6v6.2h-34.4c-.1 0-.2.1-.3.1h-.2c-.1 0-.3-.1-.4-.1h-10.7c-8.5-3-16.8-6.7-24.7-10.6-22.8-11.2-46.8-27.4-67.3-41.8-.1-.1-.2-.2-.3-.2-2.9-2-5.7-4-8.5-6-8.1-5.7-15.2-10.8-21.2-14.7v-4.6c5 2.8 10.3 6.5 16.5 10.8.1.1.2.1.3.1.2 0 .3-.1.4-.2.2-.2 0-.5-.2-.7-6.5-4.5-12-8.3-17-11.1v-.5c1 .2.9.3 1.3.5 28.5 12.1 39.7 13.1 41.2 13.1zm27.3-46.9c-2.6-3.4-3.9-6.7-4.5-9.6 3.6 4.8 8.4 7.7 9.4 8.3.2 2.1.5 4.2.9 6.5v.1c0 .1 0 .3.1.4.4 2.6 1 5.3 1.7 8.1 0 .1.1.1.1.2.1.1.2 0 .4 0-.1 0-.3.2-.4.1 1.4 5.6 3.4 11.7 6.1 17.7.6.2.9.3 1 .3.2 0 .4.2.4.5 0-.2-.2-.4-.4-.4 0 0-.4-.1-1-.2-2.7-.7-10.6-3.2-18.6-8.3-.6-.8-1.1-1.4-1.5-2l9.7-1.1c.4 0 .8-.4.9-.8s-.1-.9-.5-1.1c-6.8-3.8-10.1-14.1-11.1-17.8l6.5.8c.4 0 .8-.1 1-.5.1-.4 0-.9-.2-1.2zm-4.8-17.2l4.3 2.1c.3.1.6.1.9 0s.5-.4.5-.7c.4-2.1-2.5-8.6-3.7-11 .4-2.1 1.8-4.4 3.5-6.6-1.1 7.1 3.1 13.6 3.7 14.5-.3 4.4-.4 10 .2 16.4-1.8-1.2-6.6-4.5-9.6-9.4-.2-2.3 0-4.2.2-5.3zm14.6 1.6c.4.3.9.3 1.3-.1l8.4-7.9c.2.6.4 1.3.6 2-2.2 2.7-10.5 13-13.7 24.3-1.2-8.8-1.2-16.4-.8-21.9.4-1.5 2.9-9.8 7.1-14.3 1 1.5 1.8 2.9 1.9 3.5.3 1.6-2.7 8.6-5 13.1-.3.5-.2 1 .2 1.3zm42.3-20.1c6.3 3.4 10.1 9.3 14.1 15.5 4.4 6.7 8.9 13.7 16.9 17.6.1.1.3.1.4.1.4 0 .7-.2.9-.6.2-.5 0-1.1-.5-1.3-7.5-3.7-11.9-10.4-16.1-16.9-1.7-2.7-3.5-5.4-5.5-7.9 1.9 1.4 3.7 2.7 5.6 4.1 4.2 3.1 8.4 6.3 13.6 9.3.3.*******.5 1.4.8 2.7 1.6 4.2 2.3.4.2.9.4 1.3.6 3.5 1.7 7.1 3 10.1 4.2V353c-17-9.3-41-22.1-70.2-35.2l-1 .7.9-.7 8.9-6.9c.4-.3.5-.8.3-1.2-.2-.4-.7-.7-1.1-.6l-7.5 1.6 5.2-7.9c.3-.4.2-.9-.1-1.2-.3-.3-.8-.4-1.2-.1l-5.5 3.4c1.4-2.6 3.3-5.8 3.9-6.8 3.7-6.3 1.5-14.7-.9-23.6-.6-2.2-1.2-4.5-1.7-6.7-.1-.4-.4-.7-.9-.8-.4 0-.8.2-1 .5l-6.8 13.4c.3-3.4 1.4-9.2 4.9-17.4 1.8-4.2-2.4-18.4-3.3-21.4.7-.8 1.3-1.5 1.7-2 1.6-1.9 5.8-5.8 11.2-8.3 6.7-3.2 12.8-3.2 18.3-.2zM958 356.2l13.1-9c.8.4 1.6.9 2.5 1.3l-13.1 9c-.9-.4-1.7-.8-2.5-1.3zm-6.9-3.6l13.1-9c2 1.1 4 2.1 5.9 3.1l-13.1 9c-1.9-1-3.9-2.1-5.9-3.1zm-51.6-41.9c0-.2-.1-.4-.3-.5l-1.4-1.2 4.6-.1c.5 0 .8-.3 1-.8.1-.4-.1-.9-.5-1.1-8.7-4.7-14.5-9.9-18-13.7 8.1 4.7 15.6 6.9 16.9 6.9.3 1 1 1.2 1 1.8v.2c.6 1.1 1.1 2.3 1.7 3.5 0 .1 0 .1.1.2 1.7 3.1 3.6 6.3 5.7 9.5-3.6-1.6-7.2-3.2-10.8-4.7zm-5.3-38.2c2.5-11.4 10.8-22.2 13.6-25.7.4 1.4.8 2.9 1.1 4.4-2.5 2.7-11.7 13.8-13.7 26.7-.3-1.8-.7-3.6-1-5.4zm1.8 8.5c.7-13.1 10.1-24.8 13.2-28.5 1 4.4 1.5 8.6.8 10.1-6.5 15.1-5.1 22.2-5 22.5.1.4.4.7.9.8.4 0 .8-.2 1-.5l7.4-14.6c.4 1.4.7 2.8 1.1 4.1.1.2.1.4.2.6-.9 4-4.4 17.4-12.4 25.5-3.3-6.9-5.6-13.6-7.2-20 0 .1 0-.1 0 0zm20.5 16c-1.1 1.8-5.8 10.2-5.8 10.3-.2.4-.2.9.2 1.2.3.3.8.4 1.2.1l5-3.1-4 6.1c-.2.3-.2.8 0 1.1.2.3.6.5 1 .4l5.6-1.2-6.3 4.8c-.6-.3-1.3-.6-1.9-.8.1-.1 0-.3 0-.5-2.2-3.3-4.2-6.6-6-9.8 6.3-5.5 10.6-14.7 12.4-19.2.5 4 .3 7.6-1.4 10.6zm-11.5 7.7c-.5-.9-.9-1.9-1.4-2.8 7.4-7.3 11.1-18.9 12.5-24.4.6 2.4 1.2 4.7 1.5 6.9-1.3 3.4-5.8 14.3-12.6 20.3zm-2.5-3c0-.1.1-.2.1-.2 0 .1-.1.2-.1.2zm9.5 16.6l-13.4 9.2-2.7-1.2 13.4-9.2 2.7 1.2zm1.1.5c.9.4 1.8.8 2.6 1.2l-13.2 9.1c-.1 0-.1.1-.1.1l-2.7-1.2 13.4-9.2zm3.7 1.7c2.1 1 4.2 1.9 6.3 2.9l-13.3 9.1c-2.2-1-4.3-1.9-6.4-2.8l13.4-9.2zm7.4 3.3c.9.4 1.7.8 2.6 1.2l-13.2 9.1c-.9-.4-1.8-.8-2.6-1.2l13.2-9.1zm3.6 1.7c2.1 1 4.2 2 6.2 2.9l-13.2 9.1c-2.1-1-4.2-2-6.3-2.9l13.3-9.1zm7.3 3.5c.9.4 1.7.8 2.6 1.2l-13.1 9.1c-.9-.4-1.7-.8-2.6-1.2l13.1-9.1zm3.6 1.7c2.1 1 4.1 2 6.1 3l-13.1 9c-2.1-1-4.1-2-6.2-3l13.2-9zm7.2 3.5c4.8 2.4 9.4 4.7 13.8 7l-13.1 9c-4.4-2.3-9-4.6-13.8-6.9l13.1-9.1zm14.7 7.5c.8.4 1.7.9 2.5 1.3l-13.1 9c-.8-.4-1.7-.9-2.5-1.3l13.1-9zm-67.1-98.2c.4-3.3.8-5.1.9-5.2 0 0 .8-3.6 3.6-7.7.6.8 1.1 1.6 1.6 2.3-2.9 2.9-4.9 7.3-6.1 10.6zm-.1-5.4s-.1.3-.2.7c-.4-2.7-.7-7.2 1.1-10.8.1-.1.1-.3 0-.4.3-.2.5-.5.7-.6.7.9 1.5 1.9 2.2 2.9-2.9 4.2-3.8 8-3.8 8.2zm-.7 3.5c-.2 1.4-.4 3.1-.6 5.1-1.4-2.5-4.3-8.5-2.4-14.5 1.1-1.2 2.2-2.4 3.2-3.3-2 5.6-.3 12-.2 12.7zm-4.8 44.5l-8.6 1c-.3 0-.6.2-.8.5s-.1.7 0 .9c.2.4 5.6 9.7 20.2 18.4l-3.5.1c-.4 0-.8.3-.9.7-.1.3-.1.6.1.9-8.5-3.5-17.3-7-26.3-10.5 3.2-6.7 7.6-14.6 12.8-21.8 1.6 3.5 3.9 7.2 7 9.8zm20.4 30.6l-13.4 9.3c-17.2-7.3-29.6-12-33.1-13.3.1-.2.2-.5.3-.7.9-2.3 2.7-6.7 5.2-12 14.4 5.4 28.1 11 41 16.7zm-90.6 63.5v-59.5c12 4.2 26.9 8.2 28.8 8.7-1.7-1.2-3.3-2.3-4.9-3.5-5.9-1.7-15.9-4.5-23.9-7.4v-9.2c6 3.9 12.6 8.7 20.1 13.9l3.9 2.7c1.6 1.1 3.2 2.3 4.9 3.4h.5-.5c20.5 14.4 44.6 30.8 67.6 42 6.4 3.2 13.1 5.8 20 8.8H817.7zm0-79.5c-.1 0-.2-.1-.3-.1.1.1.2.1.3.1zm-1 0v-.3l.1.1-.1.2.1 1.2c.2.1.4.3.6.4-.3-.1-.7-.2-.7-.4v-1.2zm170 69l-1 .7v1.3c0-.2-.8-.4-1.1-.6l-1.4.9 1.4-.9 1.1-.7 1-.7v-1.2 1.2zm0-13.9v1.2l-1 .7v3.1l-8.6 5.9.9.6 7.7-5.3v-.1l1-.6V360v1.2l-1 .7v3.1l-5.3 3.6.9.6 4.4-3v-.1l1-.6v-1.2 1.2l-1 .7v2.9l-.1.1-2 1.4c-.8-.4-1.6-.9-2.4-1.4-.3-.2-.6-.4-1-.6-.8-.4-1.6-.9-2.4-1.4-.3-.2-.6-.4-1-.6-.8-.4-1.6-.9-2.4-1.4-.3-.2-.6-.4-1-.5-3.7-2.1-7.8-4.4-12.1-6.7l13.1-9 .4-.3-.4.3c4 2.2 7.1 4.2 11.1 6.1.2.3 1.2.4 1.2.7zm0-96.3l-.1-.1v-.1l-.9-.3v-.1c0 .1 1 .2 1 .3v.3zm0-2.1c0-.1 0-.1 0 0v-.2c0-.1-.7-.2-1-.3v-.1c-3-1-5.7-2.1-8.7-3.5-.6-.3-1.2-.5-1.8-.8-1.4-.7-2.8-1.5-4.1-2.2-3.9-2.3-7.5-5-11.3-7.8-3.4-2.5-6.8-5-10.6-7.4-2.6-1.8-5.5-3.6-8.6-5.3-13.8-7.5-27.7 3.8-31.9 8.8-.5.6-1.2 1.5-2.1 2.5-.1.1-.1.2-.1.2l-5.4 5.1c1.5-3.5 3.2-7.7 2.8-9.5-.5-2.9-7.2-11.2-8-12.1-.2-.2-.4-.3-.7-.4-.3 0-.5.1-.7.2-.4.4-10.6 8.7-11.8 15.8 0 .2 0 .4.1.6 1.4 3 2.8 6.5 3.4 8.5l-3.8-1.9c-.3-.1-.6-.1-.8 0-.3.1-.5.3-.6.6-.1.4-2.7 8.6 3.6 18.3l-5.7-.7c-.3 0-.6.1-.9.3-.2.2-.3.6-.2.9.1.3.7 3.4 2.3 7.2-.2.1-.4.2-.5.3-5.5 7.4-10 15.7-13.3 22.7-.7-.3-1.4-.5-2.1-.8-.5-.2-1.1.1-1.3.6s.1 1.1.6 1.3c.6.2 1.3.5 1.9.7-2.5 5.3-4.2 9.6-5.2 12-.2.4-.3.8-.4 1-2.7-.3-14.3-2-39.9-12.7-.7-.3-1.1-.6-2.1-.8v-86.4h168v44.6c0 .1 1 .2 1 .4v.3z"/><path d="M986.7 357v-1.3c0-.2-1-.4-1-.6v1.4l-12 8.2c.3.*******.5l11.1-7.6v-.1l1-.5zm-140.9-35.6c2.7 1.9 5.4 3.8 8.3 5.8.1.1.2.1.3.1.2 0 .3-.1.4-.2.2-.2.1-.5-.1-.7-2.8-2-5.6-3.9-8.3-5.8l-2.2-1.6c-.2-.2-.5-.1-.7.1-.2.2-.1.5.1.7l2.2 1.6zm105.3 57.9c-12.3-4.3-22.7-8.5-31.8-13-20.5-10-41.2-23.8-57-34.6-.2-.2-.5-.1-.7.1-.2.2-.1.5.1.7 15.8 10.9 36.5 24.7 57.1 34.8 9.1 4.4 19.4 8.9 31.6 12.9h.6c.1 0 .2 0 .2-.1h.1c.1 0 .1-.1.1-.2.1-.1 0-.5-.3-.6zm-56-97.8c0-.1-.1-.3-.1-.4v.1c0 .1 0 .2.1.3zM776.7 224.3L648 60.4c-.4-.5-1.2-.5-1.6 0l-128.7 164c-.3.4-.3.9 0 1.2l128.7 164c.2.2.5.4.8.4s.6-.1.8-.4l128.7-164c.3-.4.3-.9 0-1.3zm-129.5 163l-87.1-111 13.9 4.1h.3c.4 0 .8-.3 1-.7l17.2-57.6c2.9 4.3 10.5 16.8 17.5 35.4 8.5 22.6 13.8 34.7 31.1 41.9 13.2 5.5 34.3 8.1 67 10.4l-60.9 77.5zM543.5 255.2l33.9 10.1-1.9 6.2-25.1-7.5-6.9-8.8zm5.8-61.8l-3.9-1.2 4.3-5.5 24.6 7.3c3.4 2.9 6.8 6.1 10.3 9.8l-24.7-7.4c-.3-.1-.5.1-.6.3-.*******.3.6l26.3 7.8c2.5 2.7 4.9 5.6 7.3 8.7-.1 0-.2.1-.3.1l-50.5-15.1.5-3.7 1.9-2.4 4.3 1.3h.1c.2 0 .4-.1.5-.4.1.2-.1-.1-.4-.2zm1.1-7.5l4.4-5.6c4.8 2.7 10.9 6.7 17.6 12.2l-22-6.6zm38 42.6l-18.7-5.6c-.3-.1-.5.1-.6.3-.*******.3.6l18.7 5.6-1.9 6.2-47.2-14.1 1-6.5 22.6 6.7h.1c.2 0 .4-.1.5-.4.1-.3-.1-.5-.3-.6l-22.7-6.8 1-6.5 49.1 14.7-1.9 6.4zm-8.6 28.7l-44-13.1 1-6.5 37.1 11.1h.1c.2 0 .4-.1.5-.4.1-.3-.1-.5-.3-.6l-37.3-11.1 1-6.5 46 13.7-4.1 13.4zm4.3-14.3L538 229.1l1-6.5 47 14-1.9 6.3zm6.4-21.6l-49.3-14.7 1-6.5 50.2 15-1.9 6.2zm-50.7-21.9l.5.1-1 6.5-.7-.2c-.3-.1-.5.1-.6.3-.*******.3.6l.9.3-1 6.5-.7-.2c-.3-.1-.5.1-.6.3-.*******.3.6l.9.3-1 6.5-.7-.2c-.3-.1-.5.1-.6.3-.*******.3.6l.9.3-1 6.5-.7-.2c-.3-.1-.5.1-.6.3-.*******.3.6l.9.3-1 6.5-.6-.2c-.3-.1-.5.1-.6.3-.*******.3.6l.8.2-.9 6-14.2-17.9 20.1-25.6zm-4.2 45.7l43.9 13.1-1.9 6.2-35.2-10.5-6.8-8.7v-.1zm39.6 27.4l-1.7 5.7-15.6-4.7-6.4-8.1 23.7 7.1zm36.6-15.6c-8-21.3-17-34.7-18.7-37.2l1.2-4.1c5.7 7.6 11 16.5 15.7 26.8 2.4 5.4 4.5 10.4 6.3 14.9 13.6 32.9 17.5 42.5 95.9 47.1l-2.8 3.6c-78-5.6-82.6-11.2-97.6-51.1zm6.4-.4c-1.8-4.4-3.9-9.5-6.4-14.9-16.2-36.1-41.5-54.7-55.8-62.8l41.1-52.4c-1.8 4.7-3.5 8.8-5 12.4-7.8 19.1-10.7 26.2-2.3 39 10.7 16.1 27.2 32.1 41.5 28.4 6.1-1.6 10.1-6.7 10.8-13.9.4-4.2.7-8.8 1-13.6 1-15.5 2.3-34.9 7-55.8 3.8-16.9 12.7-16.1 26-15 2.3.2 4.6.4 6.9.5l3.4 4.3c-1.5 12.4-5.2 44.2-6.2 66.8-3 70 9.2 77.6 32.7 85.8 8.9 3.1 15.6 7.4 20.1 12.7l-19.3 24.6c-78.4-4.7-82.2-13.9-95.5-46.1zm-6.8-148.2c24.6 5.9 34.3 12.3 36.8 14.2-4.8 21.1-6 40.5-7 56.1-.3 4.8-.6 9.3-1 13.6-.6 6.3-3.9 10.8-9.3 12.1-8.7 2.2-23.6-3.9-39.3-27.6-.5-.7-.9-1.4-1.3-2.1 10.6 15.3 22.4 24.1 32.3 24.1h1.1c.3 0 .5-.3.5-.5 0-.3-.3-.5-.5-.5-10.4.7-23.3-9.2-34.5-26.6-.2-.2-.5-.3-.7-.1-3.6-8.7-.7-15.7 5.7-31.3 2.1-5 4.6-11.2 7.3-18.5l9.9-12.9zm70-2.1c-1.7-.1-3.5-.3-5.1-.4-13.1-1.1-23.6-2-27.9 15.5-3-2.2-12.7-8.3-36.3-14l35.1-44.8 34.2 43.7zm32.3 157.1c-23.3-8.2-34.4-15.2-31.4-83.9.9-21.4 4.3-51.1 5.9-64.6L774.6 225l-40.3 51.3c-4.6-5.4-11.5-9.8-20.6-13z"/><path d="M632.9 193.5c.2 0 .3-.1.4-.2 1.5-2.2 2.5-4.9 2.8-8 .3-3.6.6-7.6.9-11.4 0-.3-.2-.5-.5-.5-.2 0-.5.2-.5.5-.3 3.8-.6 7.7-.9 11.4-.3 2.9-1.1 5.4-2.6 7.5-.2.2-.1.5.1.7.1-.1.2 0 .3 0zm4.2-28.6s.1 0 0 0c.3 0 .5-.2.5-.5.7-9.7 1.8-21.4 4-34.1 0-.3-.1-.5-.4-.6-.3 0-.5.1-.6.4-2.3 12.7-3.3 24.4-4.1 34.2.1.3.4.5.6.6zM508.7 10.2h-183c-.6 0-1 .4-1 1v181c0 .6.4 1 1 1h183c.6 0 1-.4 1-1v-181c0-.5-.4-1-1-1zm-1 181h-181v-179h181v16H504c-.6 0-1.3.6-1.3 1.1v3.9h-91c-2.5-3-3.7-4.6-3.7-4.7-.2-.4-.7-.6-1.1-.5l-10.8 3.1c-.3.1-.5.2-.6.5s-.2.6-.1.8c0 .1.1-.3.3.7h-7v-3.9c0-.6-.4-1.1-.9-1.1h-17.3c-.6 0-.7.6-.7 1.1v3.9h-25.1c-1.8 0-2.9 1.3-2.9 3.2v15.2c0 12.1-1.5 24.4-2.8 37.5-1.4 14.3-3 29-3 44.5 0 12.5 2.3 17.8 5.9 25.8 1 2.3 2.3 4.9 3.6 8 0 .1.1.2.1.3.1.2 0 .5.1.7 2.3 5.2 7.6 8.9 13.6 8.9h148.4v14zM417.6 52.5l5.7-7.4 4.1 3.1 4.1 3.1-5.7 7.4-8.2-6.2zm6.9 7.8l-.8 1-3.3-2.5.8-1 3.3 2.5zm-8.1-6.2l3.3 2.5-.8 1-3.3-2.5.8-1zm-18.6-21.3l9-2.6c1.3 1.9 5.8 7.8 14.9 13.8l-5.6 7.3c-12.9-9.2-17.1-16.1-18.3-18.5zm26.7 10.7l.7-1 3.3 2.5-.7 1-3.3-2.5zm4.8 3.7l.7-1 3.3 2.5-.7 1-3.3-2.5zm3.7 5.4l10.4 8-2.1 2.7-3.6 4.7-10.4-8 5.7-7.4zm-.9 14.1c.1-.1.1-.2.2-.3l4.2 3.2-.5.7c-.3.4-.3 1.1.2 1.4.2.1.4.2.6.2.3 0 .6-.1.8-.4l9.1-11.9c.3-.4.3-1.1-.2-1.4-.4-.3-1.1-.3-1.4.2l-.4.6-4.2-3.2.1-.1c.5-.7 1.3-1.1 2.2-1.2.9-.1 1.7.1 2.4.6l49.1 37.7c1.4 1.1 1.7 3.1.6 4.5-1.1-.8-2.2-1.5-3.3-2.3l.4-.5c.3-.4.3-1.1-.2-1.4-.4-.3-1.1-.3-1.4.2l-1 1.3-6.9 9-1.1 1.5c-.3.4-.3 1.1.2 1.4.2.1.4.2.6.2.3 0 .6-.1.8-.4l.6-.7 1.9 1.2c.6.4 1.3.8 1.9 1.2-.3 0-.6.1-.8.4-.5.7-1.3 1.1-2.2 1.2-.9.1-1.7-.1-2.4-.6l-49.1-37.7c-.7-.5-1.1-1.3-1.2-2.2-.4-.9-.1-1.7.4-2.4zm54.3 38.3l-1.9-1.1 5.7-7.4c2.3 1.6 4.7 3.2 6.9 4.7 3.6 2.5 6.6 5 10.6 7.3v10c-7-3.9-13.5-8.4-21.3-13.5zm11.9-5.4c-.6-.4-1.3-.8-1.9-1.2 1.7-2.3 1.2-5.5-1-7.2l-49.1-37.7c-1.1-.9-2.5-1.2-3.9-1-1.4.2-2.6.9-3.5 2 0 .1-.1.1-.1.2l-4.7-3.6 1.3-1.7c.3-.4.3-1.1-.2-1.4l-4.1-3.1.3-.4c.3-.4.3-1.1-.2-1.4-.4-.3-1.1-.3-1.4.2l-.3.4-4.1-3.1c-.2-.2-.5-.2-.7-.2-.3 0-.5.2-.7.4l-1.4 1.8v.1c-3.8-2.5-6.8-5.2-9.1-7.2h94.2V106c-3-2-6.2-4.2-9.4-6.4zm6.7-66.4h-.3v-3h3v3H505zm-118.3-3v3h-15v-3h15zM346.5 169c.1 0 .1 0 0 0zm46.4-32.1c.1.2.3.4.3.6s-.2.4-.3.6c.1-.2.2-.4.2-.6s-.1-.5-.2-.6zm.2 22.3c.1.*******.1s.3-.1.5-.1h12.1c.1.*******.1s.3-.1.5-.1h.6v2h-.4c-.2-.2-.4-.3-.7-.3s-.5.1-.7.3h-11.6c-.2-.2-.4-.3-.7-.3s-.5.1-.7.3h-.2v-2h.3zm6.6-19.4c-.6 0-1 .4-1 1v.4h-4v-19h11v19h-5v-.4c0-.5-.4-1-1-1zm-1 3.4v7.4c-.2-.1-.4-.1-.6.1-1.1 1.1-1.1 2.9 0 4 .6.6 1.3.8 2 .8s1.5-.3 2-.8c1.1-1.1 1.1-2.9 0-4-.2-.2-.5-.2-.7 0s-.2.5 0 .7c.7.7.7 1.9 0 2.6-.7.7-1.9.7-2.6 0-.7-.7-.7-1.8-.1-2.5v.4c0 .6.4 1 1 1s1-.4 1-1v-8.6h5v9.9c-.1.1-.1.3-.1.4s0 .3.1.4v3.3h-11v-3.7c0 .2 0 .4-.2.5.1-.1.2-.3.2-.5v-10.3h4zm-4 15.4c0 .2-.1.3-.3.4.1-.1.2-.3.3-.4zm11.1-5.8c.1-.4.4-.6.8-.6-.3 0-.6.3-.8.6zm3.2 4.4h-1.3v-3h2.6c.6 0 1.4-.1 1.4-.7v-11.7c0-.6-.9-.6-1.4-.6h-2.6v-3.1c0 .2.3.1.7.1h75.1c.1 0 .3.2.3.2v2.8h-2.6c-.6 0-1.4.1-1.4.6v11.7c0 .6.8.7 1.4.7h2.6v3h-1.3c-.6 0-.7.6-.7 1.1v3.6c0 .6.1 1.3.7 1.3h1.3v6.4c0 .3.1.6.4.8l6 4.8h-88.9l-.5.4c-.2.1-.4.2-.6.2.2 0 .4-.1.6-.2l.5-.4 6.1-4.8c.2-.2.4-.5.4-.8v-6.4h1.3c.6 0 .7-.7.7-1.3v-3.6c-.1-.5-.3-1.1-.8-1.1zm-1.3-5v-9h2v9h-2zm77.5 7h12.1c.1.*******.1s.3-.1.5-.1h.5v2h-.3c-.2-.2-.4-.3-.7-.3s-.5.1-.7.3h-11.6c-.2-.2-.4-.3-.7-.3s-.5.1-.7.3h-.3v-2h.5c.1.*******.1s.3 0 .4-.1zm-.5-7c.5 0 .9.5 1 1-.1-.4-.5-1-1-1zm13.1 0c-.5 0-.9.5-1 1v-.1c0-.5.4-.9 1-.9zm-.4-15c0-.6.4-.9 1-.9-.5-.1-.9.4-1 .9zm-.5 21.6c-.1-.1-.1-.3-.1-.5-.1.2 0 .4.1.5zm-.2-17.6h-4v-.4c0-.6-.4-1-1-1s-1 .4-1 1v.4h-5v-19h11v19zm-6 2v8.6c0 .6.4 1 1 1s1-.4 1-1v-.3c.5.7.5 1.7-.2 2.4s-1.9.7-2.6 0c-.7-.7-.7-1.9 0-2.6.2-.2.2-.5 0-.7s-.5-.2-.7 0c-1.1 1.1-1.1 2.9 0 4 .6.6 1.3.8 2 .8s1.5-.3 2-.8c1.1-1.1 1.1-2.9 0-4-.1-.1-.3-.2-.5-.1v-7.3h4v14h-11v-14h5zm6.1 18.4c0 .1-.1.2-.1.4 0-.2 0-.3.1-.4zm-.1 1.6v6l-5.5 4.4-5.5-4.4v-6h11zm-13-11h-2v-9h2v9zm-89 11h11v6l-5.5 4.4-5.5-4.4v-6zm-.2-1.7zm13 0c.1.1.2.2.2.5 0-.2-.1-.4-.2-.5zm-.1-2.5c.2-.2.3-.4.3-.6 0 .2-.1.5-.3.6zm-14.7-6.8h-2v-9h2v9zm0-13.9v2.9H390c-.6 0-1.2.1-1.2.6v11.7c0 .6.7.7 1.2.7h2.8v3h-1.5c-.6 0-.5.6-.5 1.1v3.6c0 .6 0 1.3.5 1.3h1.5v6.4c0 .3.1.6.3.8l6 4.8h-39.7c-5.3 0-9.8-3.2-11.8-7.7-.1-.2 0-.5-.1-.7-.1-.3-.2-.5-.3-.8-.3-1.2-.4-2.5-.4-3.7v-26.7c2 1.8 4.7 2.6 7.8 2.6h37.5c.2 0 .6.2.6.1s.1-.1.1-.1l-.1.1zm115 36.9h-15.4l-.5.4c-.2.1-.4.2-.6.2.2 0 .4-.1.6-.2l.5-.4 6-4.8c.2-.2.4-.5.4-.8v-6.4h1.4c.6 0 .6-.7.6-1.3v-3.6c0-.6-.1-1.1-.6-1.1h-1.4v-3h2.7c.6 0 1.3-.1 1.3-.7v-11.7c0-.6-.8-.6-1.3-.6h-2.7v-3h9v37zm-9-23v-9h2v9h-2zm9-20.5v-.3c-.1.1-.1.2-.1.3v.2c0 .1 0 .2.1.3v4h-9v-14h2.9c.6 0 1-.4 1-1s-.4-1-1-1h-20.8c-.6 0-1 .4-1 1s.4 1 1 1h2.9v14.3s-.2-.3-.3-.3h-75.1c-.3 0-.7.4-.7.6v-14.6h2.8c.6 0 1-.4 1-1s-.4-1-1-1h-20.8c-.6 0-1 .4-1 1s.4 1 1 1h3v14.4s0 .1.1.1l-.1-.1v-.1c0-.1-.4-.4-.6-.4h-37.5c-6.2 0-10.8-4.9-10.8-11.1V51.6c0 10.5-1 21.1-2 32.2v41.3c0 3.2 1 6.2 3 8.5v25.6c-.5-.2-.7-.4-.9-.7-3.6-8-5.8-12.9-5.9-25 0-15.4 1.6-30.1 3-44.3.2-1.8-.3-3.6.7-5.5 1-11.1 1.8-21.7 1.8-32.2 0-.5-.3-1-.8-1h.1c.6 0 1 .4 1 1V36.4c0-.7.1-1.2.9-1.2h51c.4 0 .7-.3.9-.6-.3-.6-.6-1.1-.7-1.4.2 0 .4.8.7 1.4 1.8 3.2 6.5 9.8 18.3 18.2l-1.4 1.8c-.2.2-.2.5-.2.7 0 .*******.7l4.1 3.1-.3.4c-.3.4-.3 1.1.2 1.4.2.1.4.2.6.2.3 0 .6-.1.8-.4l.3-.4 4.1 3.1c.2.1.4.2.6.2.3 0 .6-.1.8-.4l1.4-1.8 4.7 3.6c-.1.1-.2.1-.3.3-.9 1.1-1.2 2.5-1 3.9.2 1.4.9 2.6 2 3.5l49.1 37.7c.9.7 2 1.1 3.2 1.1h.7c1.4-.2 2.6-.9 3.5-2 .2-.3.2-.7.1-1-.1-.2-.2-.3-.3-.4.1.1.4.3.4.4 7 4.6 13.6 8.7 19.6 12.3v10.6c.1-.1.2-.2.4-.2-.3 0-.5.4-.6.5z"/><path d="M345.6 168.4s.1 0 0 0c0-.1 0 0 0 0zm100.2-89.6c.3 0 .6-.1.8-.4l.5-.7 25.6 19.6-.5.7c-.3.4-.3 1.1.2 1.4.2.1.4.2.6.2.3 0 .6-.1.8-.4l9.1-11.9c.3-.4.3-1.1-.2-1.4-.4-.3-1.1-.3-1.4.2l-.4.6-25.6-19.6.4-.6c.3-.4.3-1.1-.2-1.4-.4-.3-1.1-.3-1.4.2l-1 1.3-6.9 9-1.1 1.5c-.3.4-.3 1.1.2 1.4 0 .3.2.3.5.3zm8.2-10.1l25.6 19.6-5.7 7.5-25.6-19.6 5.7-7.5zm-104.8 39.7c.3 0 .5-.2.5-.5V90.6c0-.3-.2-.5-.5-.5s-.5.2-.5.5V108c0 .2.2.4.5.4zm34.7 22.8h-26.3c-4.4 0-7.8-3.3-7.8-7.7v-7.8c0-.3-.2-.5-.5-.5s-.5.2-.5.5v7.8c0 4.9 3.9 8.7 8.8 8.7h26.3c.3 0 .5-.2.5-.5s-.3-.5-.5-.5zm74.2 0h-43c-.3 0-.5.2-.5.5s.2.5.5.5h43c.3 0 .5-.2.5-.5s-.3-.5-.5-.5zm18.3 0h-11.9c-.3 0-.5.2-.5.5s.2.5.5.5h11.9c.3 0 .5-.2.5-.5s-.2-.5-.5-.5z"/><g><path d="M529.9 380.5L398.8 210.7c-.4-.5-1.2-.5-1.6 0L266.1 380.5c-.2.3-.3.8-.1 1.1.2.3.5.6.9.6h262.2c.4 0 .7-.3.9-.6.2-.3.2-.8-.1-1.1zm-166-84c5.9-2.7 10.1-3.7 15.6-1.7 5.9 2.2 8.2 5.8 10.6 11.9-.6 1.1-1.1 2.2-1.6 3.3l-13.1-4.3-12.6-5.4h-.1c.4-1.3.7-2.6 1-3.8h.2zm95.4 49c-.5-.2-1.1.1-1.3.6 0 .1-.1.3-.1.4 0-.1 0-.3.1-.4.2-.5.8-.8 1.3-.6l1.1.4c1-1.8 1.8-3.6 2.5-5.5s1.3-3.9 1.7-5.9l2.7 1-4.3 11.4-2.7-1-1-.4zm1.8-5.8c-3.6 9.6-10.7 17.3-20 21.5-9.3 4.2-19.6 4.5-29.1.9-19.7-7.4-29.7-29.7-22.3-49.5 3.6-9.6 10.7-17.3 20-21.5 5-2.3 10.3-3.4 15.7-3.4 4.5 0 9.1.8 13.4 2.5 19.6 7.4 29.7 29.6 22.3 49.5zm-100.9-38.2c-3.6 9.6-10.7 17.3-20 21.5-8.9 4.1-18.9 4.5-28.1 1.3l2.2-2.8c3.4 1 6.8 1.6 10.3 1.6 4.9 0 9.7-1 14.3-3.1 8.5-3.9 15-10.8 18.3-19.7 3.9-10.5 2.5-21.7-2.9-30.6l2.2-2.8c6.3 10 8.1 22.7 3.7 34.6zm-23.1-9.7l14 5.9-8.1-2.8c-2.2-.8-4.5-1.4-6.8-1.9l.9-1.2zm5.2 5l13.1 4.4h.2c-3.3 7.9-9.4 14.2-17.3 17.8-7.5 3.4-15.7 3.9-23.5 1.6l20-25.9c2.5.7 5.1 1.3 7.5 2.1zm14.5 1.3c-.1-.1-.2-.2-.4-.3l-18-7.6 15.1-19.6c4.8 8 6.2 18 3.3 27.5zm-47 27.6c.2 0 .4.1.6.2.1.1.3.1.4.1 4.5 1.6 9.1 2.5 13.7 2.5 5.6 0 11.2-1.2 16.5-3.6 9.5-4.3 16.7-11.9 20.7-21.5l13 4.3 12.7 5.4c-7 20.6 3.5 43.2 23.9 50.9 4.6 1.7 9.4 2.6 14.1 2.6 3.5 0 7.4-.5 10.4-1.4 1-.2 1.4-.4 2-.6v-.3.3c1.4-.5 2.7-1 4.1-1.6.5-.2.9-.4 1.4-.7.9 2.9 1.6 5.8 2 8.8.2 1.4-.3 2.8-1.5 3.7-1.1.9-2.6 1.1-3.9.5-1.5-.6-2.5-2.2-2.4-3.8 0-.5.1-1 .1-1.6.1-1.4.2-3.2.2-5.3-.7.2-1.3.4-2 .6 0 1.8-.1 3.3-.2 4.6 0 .6-.1 1.2-.1 1.7-.1 2.5 1.3 4.8 3.6 5.8.8.3 1.5.5 2.3.5 1.3 0 2.6-.4 3.6-1.3 1.7-1.3 2.5-3.4 2.2-5.6-.5-3.2-1.2-6.4-2.2-9.5v-.1c-.2-.5-.7-.8-1.3-.7-.4.1-.7.5-.7.9 0-.4.3-.7.7-.9.5-.2 1.1.1 1.3.7v.1c5.9-3.3 10.8-8 14.3-13.7l3.9 1.5c.1 0 .2.1.4.1.1 0 .3 0 .4-.1.2-.1.4-.3.5-.6l5-13.3c.2-.5-.1-1.1-.6-1.3l-3.9-1.5c2.9-18.6-7.4-37.3-25.6-44.2-10-3.8-20.9-3.4-30.7 1-7.3 3.3-13.3 8.6-17.5 15.3-2-4.9-4.6-9.2-11.2-11.6-6.3-2.4-11.2-.9-16 1.2 1.6-10.3-.9-20.6-6.5-28.9L398 213l129.1 167.2H269l41.8-54.1c-.1-.1-.3-.1-.4-.2-.3-.1-.5-.1-.6-.2L267 381.2l42.8-55.5z"/><path d="M437.6 293.3c-8.7-3.3-18.1-3-26.6.9s-15 10.8-18.3 19.7c-6.8 18.1 2.4 38.4 20.4 45.2 4 1.5 8.1 2.3 12.3 2.3 4.9 0 9.7-1 14.3-3.1 8.5-3.9 15-10.8 18.3-19.7 6.7-18.2-2.4-38.5-20.4-45.3zm-24.2 64.8c-17.1-6.4-25.9-25.3-20.3-42.6.1 0 .1.1.2.1l12.7 5.3c7.8 3.3 14.7 8 20.6 13.8v.1c5.2 6.1 8.2 14.1 8.9 23.5 0 .1 0 .2.1.3-7.2 2.4-15 2.2-22.2-.5zm25.8-.9c-.6.3-1.1.5-1.7.7-.4-5-1.4-9.6-3.1-13.8 2.5 3.7 4.7 7.6 6.4 11.8.1.1.1.2.2.3-.5.4-1.1.7-1.8 1zm17.8-19.1c-2.7 7.3-7.7 13.3-14.2 17.2v-.1c-6.9-16.4-19.6-29.2-35.9-36.1l-8-3.4 58.5 19.7c.1 0 .2.1.3.1h.3c-.4.9-.7 1.8-1 2.6zm1.3-4.3c-.1-.1-.2-.2-.4-.3l-63-21.3c-.2-.1-.4-.1-.5 0 3.4-7.7 9.4-13.7 17-17.2 4.4-2 9.2-3 13.9-3 4 0 8 .7 11.9 2.2 16.1 6.1 24.9 23.2 21.1 39.6z"/></g><g><path d="M299.4 186.4L161.8 11.1c-.4-.5-1.2-.5-1.6 0L22.5 186.4c-.3.4-.3.9 0 1.2l137.6 175.3c.2.2.5.4.8.4s.6-.1.8-.4l137.6-175.3c.4-.3.4-.9.1-1.2zm-71.6-52.5l-2.3-3.7c-.1-.1-.1-.2-.1-.4 0 .1.1.3.1.4l2.3 3.7 3.7 6c-.7 2.7-.5 5.3.6 7.2.9 1.6 2.4 2.8 4.4 3.3.8.2 1.7.4 2.5.6 3.4.7 6.3 1.2 7 5 .1.5.5.8 1 .8h.2c.1 0 .3-.1.4-.1.3 3.8.2 7.9-.2 12-.7 6.8-1.8 13.2-3.3 19.1l-.6 2.3-.9 3.3c-1.9 6.2-4.2 12-6.9 17.3l-7.9-76.8zm-2.6-12.4c0 .1.1.3.1.4l1.5 2.5 10.9 17.9c.3.5.1 1.2-.4 1.5-.2.1-.5.2-.8.1-.3-.1-.5-.2-.7-.5l-8.6-14.2-.5-4.9-1.5-2.5v-.3zm9.1 23c.4.7 1.1 1.2 1.9 1.4.2.1.5.1.7.1.6 0 1.1-.2 1.6-.4 1.4-.9 1.9-2.8 1-4.2l-1.9-3.1c1.2-.4 2.3-.3 3.4.4 3 1.9 5.2 7.5 6.1 14.6-1.6-3-5-3.7-7.8-4.2-.8-.2-1.6-.3-2.4-.5-1.5-.4-2.5-1.2-3.2-2.4-.6-1-.8-2.2-.7-3.6l1.3 1.9zm10 49.9l1.8-6.3v-.1c1.5-6 2.6-12.3 3.2-19.1 1.4-14.8-1.6-28.3-7.2-31.9-1.7-1.1-3.6-1.3-5.6-.5l-9.5-15.6c-.1-.2-.3-.4-.6-.4l-.7-6.4c-.2-2.3-2.1-3.8-4.4-3.8h-36.7c-2-3-4.2-4.9-6.4-6.2v-.1c-1.4-20.5-5.9-40.4-12.9-56.1-4-9.1-8.8-16.6-14.1-22.3l9.7-12.3L297.4 187l-56.9 72.5-4.2-41.8c3.6-8.6 6.1-16.7 8-23.3zM200 174.8c.1-1.1.1-2.3.2-3.4.1.2.4.3.6.4l1.1.2-1.4 7.9-5.7-1-5.7-1 1.4-7.9 1.1.2h.3c-.2 2.1-.4 3.2-.4 3.3 0 .3 0 .5.2.8s.4.4.6.4l6.5 1.1h.2c.2 0 .4-.1.6-.2.3-.3.4-.5.4-.8zm7.3 8.4l-6.1-.9 6.1.9-2.6 34.7c-.1.8-.5 1.5-1.1 2-.6.5-1.4.7-2.2.5l-26.9-4.7c-.8-.1-1.5-.6-1.9-1.3-.4-.7-.6-1.5-.4-2.3l9.5-33.5 6.2 1.1 9.2 1.6-2.6-.5 2.6.5 4.1.9h.1-.1l-4.1-.9 4.1.7-4.1-.7 4.1.7 6.1 1.2zm-9.2-9.7l-4.4-.8c.6-4 2.4-19.5-.7-38.7.6.2 1.1.4 1.7.5 0-.1-.1-.2-.1-.3-.1-.5.2-1.1.7-1.2h.4-.4c-.5.1-.8.7-.7 1.2 0 .1.1.2.1.3 2.2 8.1 4.4 21.3 3.4 39zm-29.4-58.4c-.8-2.4-1.1-4.7-.6-6.5.2 1.1.7 2.1.7 3.1 0 .1.2.2.3.3 0 .1.2.2.3.3 0 .1.1.1.1.2 4.8 8.7 20.9 12.8 30.3 15.2l1 .2v6.3c-23.1-5.9-30.3-13.6-32.1-19.1zm13 16.7l-.2-2c.1 0 .2.1.2.1v1.9zm-1.4-4.6c.5 0 .9.3 1 .8v.1c-.1-.5-.5-.9-1-.9zm3.1 37.8c.1.7-.1 1.1-.6 1.6-.4.5-1.1.6-1.7.6h-14.7c-.7 0-1.3-.1-1.7-.6-.4-.5-.6-.9-.6-1.6l4-35.8h11.2l4.1 35.8zm6.1-41.2h-.2.2zm-1.3-1.4c0 .2.1.4.1.6 0-.2-.2-.5-.3-.7-1.4-.5-2.3-1-4.3-1.6v-3.2c0-.6-.2-1.3-.7-1.3h-4.1l-.1.2v.1c0-1.5 0-3-.1-4.5.1.1.3.2.4.2h3.1c2.5 3 5.1 6.3 6 10.2zm-9.6-12.1v-.1c0-.5 0-1.1-.1-1.6.5.5 1.1.7 1.7 1.7H179c-.1-.1-.2-.1-.4 0zm.2 7.9c-.1 0-.2-.1-.3-.1 0 0 0-.1.1-.1.2.1.4.2.7.2h2.4v1.6c-1-.4-1.5-.6-2.4-1.6h-.5zm-2-1.2c-2.4-1.6-4.4-3.4-5.6-5.6 0 0 0-.1-.1-.1-.1-2.8-.3-5.6-.5-8.5v-.1c-2.2-28.5-10.4-56-24-71.3l3.3-4.2c5.9 6.4 10.4 14.2 13.6 21.5 8.4 18.7 13.2 43.6 13.3 68.3zm13.9 5.9c-2-2.7-4-6.7-6.8-10.7 2.8 4 5 8 6.5 10.7h-.1s.2.1.3.1c0 0 .1 0 .1-.1zm-.4-.1c-.6-3.9-3-7.6-5.4-10.6 2.4 3 4.7 6.6 5.4 10.6zM83.8 110l61-77.7c.2.3.5.5.7.8 4.6 5.3 8.2 11.7 10.9 17.6 6.4 14.2 10.7 32.7 12.3 51.9v.2c-.7.9-2.4 3.4-2.7 7.4 0 0 0-.1 0 0H85l60.4-77.1c-.2-.3-.5-.5-.7-.7l-61 77.8-1.6 2-1.4 1.6 1.4-1.8 1.7-2zm-5.1 8.2v13.5l-.4-3.8c-.1-.5-.5-.7-1-.7h-5.6l6.7-9h.3zM68.8 129l1.6-2 2.6-3.3-2.6 3.5-1.6 2-4.3 5.3-.2 1.9 5.7-7.3h6.3l4 35.9c.1.7-.1 1.1-.6 1.6-.4.5-1.1.6-1.7.6H63.5c-.7 0-1.3-.1-1.7-.6-.4-.5-.6-1.1-.6-1.7l3.2-28.5.2-1.9 4.2-5.5zm9.9 128.5L23.3 187l.1-.1 55.3 70.3.8 1 8.5 11.1-8.5-10.8-.8-1zm153 14.8l-.8-.4-5.7 7.4h-58.9c-2.5 0-5 1.4-7.6 2h-.2 65.2L161 361l-62.6-79.7h14.7c-.5 0-.9-.5-1-1v-.1c.1.5.5.8 1 .8h.1c1-.2 2.2-.3 3.4-.5-1.3.2-2.4.4-3.4.6-.1 0-.1.1-.1.1h45.3c-.5 0-.9-.4-1-.9v-.4.3c.1.5.5.7 1 .7.1 0 .1.1.2.1 2.6-.6 5.2-1.9 7.6-1.9 38.9-11 58-35.7 68.3-57.7l-.4-3.9c-3 6.9-6.8 14-11.7 20.9-14.7 20.5-36.3 33.7-64.2 40.7l-.1-.1c-.1 0-.2.1-.3.1h-31.4c28.6-5 81.3-17.1 107.7-61.7l-.4-3.6c-28.7 51.8-93.1 61.2-117.4 64.7-1.2.2-2.4.6-3.4.6 0 0 0-.1-.1-.1s-.3.1-.4.1H96.8l-16-21h39.7c.3 0 .5-.2.5-.5s-.2-.5-.5-.5H80l-55.4-70.4L62 139.3l.2-1.8-.2 1.9-2.8 25.3c-.1 1.2.3 2.4 1.1 3.3.8.9 2 1.3 3.2 1.3h14.7c1.2 0 2.4-.4 3.2-1.3.8-.9 1.2-2.1 1.1-3.3L79 133.4c.2.3.5.5.8.5.6 0 1-.4 1-1v-15.3c0-.6-.2-1.3-.7-1.3H80l3.6-4H166v-1.9c0 .6-.1.9 0 1.9h.1c.5 0 .9-.5 1-1v.1c0 .6-.4.9-1 .9h-.1c.1 1 .3 2.1.7 3.4.1.3.2.6.3.6h.2c.6 0 1 .6 1 1.1v-.1c0-.5-.5-1.1-1-1.1h-.2s-.1.1-.1 0h-2.3c-.6 0-.9.7-.9 1.3v15.3c0 .6.4 1 1 1s1-.4 1-1v-14.6h1.4c.2 0 .3-.1.5-.2 0 .1.1.2.1.2l.2-.2c-.1.1-.1.2-.2.2 1.8 3.2 4.8 5.9 8.9 8.9h-9.4c-.5 0-.9.3-1 .8l-4.1 36.8c-.1 1.2.3 2.3 1.1 3.2.8.9 2 1.3 3.2 1.3h14.7c1.2 0 2.4-.4 3.2-1.3.8-.9 1.2-2.1 1.1-3.3l-3.5-31.3c.2.3.5.5.8.5.6 0 1-.4 1-1v-2c2 .9 4.5 1.8 7 2.6v-.4c-.1-.5.3-1.1.8-1.2-.5.1-.9.6-.8 1.2 0 .1 0 .3.1.4 2.5 15.1 2 28 1.4 34.9 0 0-.1 0-.1-.1l-2.1-.4c-.3 0-.5 0-.7.2-.2.2-.4.4-.4.6l-1.6 8.9-6.1-1.1-3.9-.7c-.5-.1-1.1.3-1.2.8-.1.5.3 1.1.8 1.2l2.8.5-9.4 33.4c-.4 1.3-.1 2.7.6 3.9s1.9 1.9 3.3 2.2l26.9 4.7c.3 0 .6.1.8.1 1.1 0 2.1-.3 3-1 1.1-.8 1.8-2.1 1.9-3.4l2.6-34.5 2.8.5h.2c.5 0 .9-.3 1-.8.1-.5-.3-1.1-.8-1.2l-9.9-1.8 1.6-8.9c0-.3 0-.5-.2-.7-.2-.2-.4-.4-.6-.4l-2.1-.4c-.4-.1-.7.1-.9.3.5-15.5-1.4-27.2-3.3-34.9 1.3.4 2.7.7 4.1 1.1h.2c.2 0 .7-.1.8-.2.2-.2.6-.5.6-.8V127c0-.5-.5-.9-1-1l-1.6-.4c-2.1-.5-4.6-1.2-7.1-1.9.1.4-.1.9-.5 1.1-.1.1-.3.1-.5.1.1 0 .3 0 .4-.1.4-.2.6-.7.5-1.1l-.1-.1c-1.3-2.7-3.8-7.4-6.7-11.4h35.2c1.2 0 2.3.8 2.4 2l10 99.7.3 3.5v.1l.4 3.9 4.1 40.4-7.1 9.1-15.4-10.2c-.2-.2-.5-.1-.7.1-.2.2-.1.5.1.7l15.3 10.1.8.7z"/><path d="M201.4 207.1c-.3 0-.5.2-.5.5l-.5 7.1c0 .1-.1.3-.2.4-.1.1-.3.1-.4.1l-12.7-2.2-6.6-1.2-2.6-.5c-.1 0-.3-.1-.3-.2-.1-.1-.1-.3-.1-.4l5.3-18.7c.1-.3-.1-.5-.3-.6-.3-.1-.5.1-.6.3l-5.3 18.7c-.1.4 0 .8.2 1.2.2.4.6.6 1 .7l2.6.5 6.6 1.2 12.7 2.2h.3c.3 0 .7-.1.9-.3.3-.3.5-.6.6-1.1l.5-7.1c-.1-.3-.3-.5-.6-.6zm-18.2-18.9h.1c.2 0 .4-.1.5-.4l1-3.7 14.5 2.6c.3 0 .5-.1.6-.4 0-.3-.1-.5-.4-.6l-15-2.6c-.3 0-.5.1-.6.4l-1.2 4.1c.1.3.3.5.5.6zm18.6 14.5c.3 0 .5-.2.5-.5l.9-12c0-.3-.2-.5-.5-.5s-.5.2-.5.5l-.9 12c0 .3.2.5.5.5zm-24.9 54.5h-18.2c-.3 0-.5.2-.5.5s.2.5.5.5h18.2c.3 0 .5-.2.5-.5s-.2-.5-.5-.5zm-18-139.5c0-.3-.2-.5-.5-.5h-42.5c-.3 0-.5.2-.5.5s.2.5.5.5h42.5c.3 0 .5-.2.5-.5zm-93.8 45.6c.1.1.2 0 .4 0h10.4c.3 0 .5-.2.5-.5s-.2-.5-.5-.5H66l1.4-12.6c0-.3-.2-.4-.4-.4-.3 0-.5.2-.6.5L65 163.1c-.1.1 0 .1.1.2zm2.4-18.7c.1 0 .1 0 0 0 .3 0 .5-.2.6-.4l1.2-10.9c0-.3-.2-.5-.4-.6-.3 0-.5.2-.6.4L67.1 144c0 .3.2.6.4.6zm100.7 18.7c.1.1.2 0 .4 0h6.4c.3 0 .5-.2.5-.5s-.2-.5-.5-.5h-5.9l.5-3.9c0-.3-.2-.4-.4-.4-.3 0-.5.2-.6.5l-.5 4.7.1.1zm1.8-12.7s0 .1 0 0c.3 0 .5-.2.6-.4l1.9-17c0-.3-.2-.5-.4-.6-.3 0-.5.2-.6.4l-1.9 17c-.1.4.1.6.4.6zm-62.6-33.4H89.1c-.3 0-.5.2-.5.5s.2.5.5.5h18.2c.3 0 .5-.2.5-.5s-.1-.5-.4-.5z"/></g></svg>
          <div class="full-width-video__placeholder-text">
            Translation missing: en.sections.video.placeholder
          </div>
        </div>
      
    </div>
  </div>
</div>

<style>
  .full-width-video {
    margin: var(--spacing-extra-loose) 0;
  }

  .full-width-video__wrapper {
    position: relative;
  }

  .full-width-video__content-wrapper {
    margin-bottom: var(--spacing-base);
  }

  .full-width-video__content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .full-width-video__title {
    margin-bottom: var(--spacing-small);
  }

  .full-width-video__description {
    margin-bottom: var(--spacing-base);
  }

  .full-width-video__container {
    position: relative;
    width: 100%;
    background-color: #000;
  }

  .full-width-video__embed {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    max-width: 100%;
  }

  .full-width-video__player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .full-width-video__cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 1;
  }

  .full-width-video__cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .full-width-video__play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background-color: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .full-width-video__play-button:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }

  .full-width-video__play-button svg {
    width: 30px;
    height: 30px;
    fill: white;
  }

  .full-width-video__placeholder {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .full-width-video__placeholder-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.2;
  }

  .full-width-video__placeholder-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: var(--color-secondary);
  }

  @media screen and (max-width: 749px) {
    .full-width-video__play-button {
      width: 60px;
      height: 60px;
    }

    .full-width-video__play-button svg {
      width: 24px;
      height: 24px;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    initFullWidthVideo('full-width-video');
  });

  function initFullWidthVideo(sectionId) {
    const container = document.querySelector(`[data-section-id="${sectionId}"]`);
    if (!container) return;

    const youtubeVideo = container.querySelector('[data-youtube-video]');
    const vimeoVideo = container.querySelector('[data-vimeo-video]');
    const cover = container.querySelector('[data-video-cover]');
    const playButton = container.querySelector('[data-video-play-button]');

    let player;
    let isPlayerReady = false;

    // Load YouTube API if needed
    if (youtubeVideo && !window.YT) {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

      window.onYouTubeIframeAPIReady = function() {
        initYouTubePlayer();
      };
    } else if (youtubeVideo && window.YT && window.YT.Player) {
      initYouTubePlayer();
    }

    // Load Vimeo API if needed
    if (vimeoVideo && !window.Vimeo) {
      const tag = document.createElement('script');
      tag.src = 'https://player.vimeo.com/api/player.js';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

      tag.onload = function() {
        initVimeoPlayer();
      };
    } else if (vimeoVideo && window.Vimeo) {
      initVimeoPlayer();
    }

    // Initialize YouTube player
    function initYouTubePlayer() {
      const videoId = youtubeVideo.getAttribute('data-video-id');
      
      player = new YT.Player(`youtube-player-${sectionId}`, {
        videoId: videoId,
        playerVars: {
          autoplay: 0,
          controls: 1,
          rel: 0,
          showinfo: 0,
          modestbranding: 1,
          playsinline: 1
        },
        events: {
          'onReady': onPlayerReady
        }
      });
    }

    // Initialize Vimeo player
    function initVimeoPlayer() {
      const videoId = vimeoVideo.getAttribute('data-video-id');
      
      player = new Vimeo.Player(`vimeo-player-${sectionId}`, {
        id: videoId,
        autoplay: false,
        controls: true,
        title: false,
        byline: false,
        portrait: false
      });

      player.ready().then(function() {
        isPlayerReady = true;
      });
    }

    function onPlayerReady() {
      isPlayerReady = true;
    }

    // Play video when cover is clicked
    if (playButton) {
      playButton.addEventListener('click', function() {
        if (!isPlayerReady) return;
        
        if (youtubeVideo) {
          player.playVideo();
        } else if (vimeoVideo) {
          player.play();
        }
        
        if (cover) {
          cover.style.display = 'none';
        }
      });
    }

    // Autoplay video when in viewport
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && isPlayerReady) {
          if (youtubeVideo) {
            player.playVideo();
            if (cover) {
              cover.style.display = 'none';
            }
          } else if (vimeoVideo) {
            player.play();
            if (cover) {
              cover.style.display = 'none';
            }
          }
        } else if (!entry.isIntersecting && isPlayerReady) {
          if (youtubeVideo) {
            player.pauseVideo();
          } else if (vimeoVideo) {
            player.pause();
          }
        }
      });
    }, { threshold: 0.5 });

    const videoContainer = container.querySelector('[data-video-container]');
    if (videoContainer) {
      observer.observe(videoContainer);
    }
  }
</script>


</div>
<div id="shopify-section-collection-carousel" class="shopify-section"><div class="collection-carousel"
  data-section-id="collection-carousel"
  data-section-type="collection-carousel"
  data-slides-per-view="3"
  data-slides-per-view-tablet="2"
  data-slides-per-view-mobile="1"
  data-autoplay-speed="5000"
  data-autoplay="true">
  <div class="page-width">
    
      <div class="section-header text-center">
        <h2 class="section-title">Collection Carousel</h2>
        
      </div>
    

    <div class="collection-carousel__wrapper">
      <div id="collection-carousel-collection-carousel" class="keen-slider collection-carousel__slider">
        
      </div>

      

      
        <div class="collection-carousel__dots" data-carousel-dots></div>
      
    </div>
  </div>
</div>

<style>
  .collection-carousel {
    margin: var(--spacing-extra-loose) 0;
  }

  .collection-carousel__wrapper {
    position: relative;
    padding: 0 40px;
  }

  .collection-carousel__slider {
    overflow: visible;
  }

  .collection-carousel__slide {
    padding: var(--spacing-base);
  }

  .collection-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
  }

  .collection-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .collection-card__link {
    display: block;
    text-decoration: none;
    color: inherit;
  }

  .collection-card__image-wrapper {
    position: relative;
    padding-top: 100%;
    overflow: hidden;
  }

  .collection-card__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .collection-card:hover .collection-card__image {
    transform: scale(1.05);
  }

  .collection-card__info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-base);
    background-color: rgba(255, 255, 255, 0.9);
    text-align: center;
  }

  .collection-card__title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }

  .collection-card__count {
    margin-top: 5px;
    font-size: 14px;
    color: var(--color-secondary);
  }

  .collection-card--placeholder .collection-card__image-wrapper {
    background-color: #f7f7f7;
  }

  .collection-carousel__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
  }

  .collection-carousel__arrow {
    background-color: white;
    border: 1px solid var(--color-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
  }

  .collection-carousel__arrow:hover {
    background-color: var(--color-background-light);
  }

  .collection-carousel__arrow svg {
    width: 12px;
    height: 12px;
  }

  .collection-carousel__dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: var(--spacing-base);
  }

  .collection-carousel__dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--color-border);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .collection-carousel__dot--active {
    background-color: var(--color-primary);
  }

  @media screen and (max-width: 749px) {
    .collection-carousel__wrapper {
      padding: 0 30px;
    }

    .collection-carousel__arrow {
      width: 30px;
      height: 30px;
    }

    .collection-card__title {
      font-size: 16px;
    }
  }
</style>

<script>
  // Collection carousel will be initialized by theme-utilities.js
  // All configuration is passed via data attributes on the carousel element
</script>


</div>
<div id="shopify-section-testimonial-carousel" class="shopify-section"><div class="testimonial-carousel"
  data-section-id="testimonial-carousel"
  data-section-type="testimonial-carousel"
  data-slides-per-view="3"
  data-slides-per-view-tablet="2"
  data-slides-per-view-mobile="1"
  data-autoplay-speed="5000"
  data-autoplay="true">
  <div class="page-width">
    
      




<h2 class="section-heading ">
  <span class="section-heading__outline" style="--heading-outline-color: #000000;">Customer</span>
  <span class="section-heading__filled" style="--heading-filled-color: #000000;">Testimonials</span>
</h2>

    

    

    <div class="testimonial-carousel__wrapper">
      <div id="testimonial-carousel-testimonial-carousel" class="keen-slider testimonial-carousel__slider">
        
      </div>

      <div class="testimonial-carousel__controls">
        <button type="button" class="testimonial-carousel__arrow testimonial-carousel__arrow--prev" data-carousel-prev aria-label="Previous testimonials">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
            <path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/>
          </svg>
        </button>
        <button type="button" class="testimonial-carousel__arrow testimonial-carousel__arrow--next" data-carousel-next aria-label="Next testimonials">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
            <path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"/>
          </svg>
        </button>
      </div>

      
        <div class="testimonial-carousel__dots" data-carousel-dots></div>
      
    </div>
  </div>
</div>

<style>
  .testimonial-carousel {
    margin: var(--spacing-extra-loose) 0;
  }

  .testimonial-carousel__wrapper {
    position: relative;
    padding: 0 40px;
  }

  .testimonial-carousel__slider {
    overflow: visible;
  }

  .testimonial-carousel__slide {
    padding: var(--spacing-base);
    height: auto;
  }

  .testimonial-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-loose);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .testimonial-card__rating {
    display: flex;
    margin-bottom: var(--spacing-small);
  }

  .testimonial-card__star {
    color: #d4d4d4;
    margin-right: 2px;
  }

  .testimonial-card__star svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
  }

  .testimonial-card__star--filled {
    color: #FFD700; /* Yellow color for stars */
  }

  .testimonial-card__title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-small);
  }

  .testimonial-card__content {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: var(--spacing-base);
    flex-grow: 1;
  }

  .testimonial-card__author {
    display: flex;
    align-items: center;
    margin-top: auto;
  }

  .testimonial-card__author-image-wrapper {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: var(--spacing-small);
    flex-shrink: 0;
  }

  .testimonial-card__author-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .testimonial-card__author-name {
    font-weight: 500;
    font-size: 16px;
  }

  .testimonial-card__author-title {
    font-size: 14px;
    color: var(--color-secondary);
  }

  .testimonial-carousel__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
  }

  .testimonial-carousel__arrow {
    background-color: white;
    border: 1px solid var(--color-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
  }

  .testimonial-carousel__arrow:hover {
    background-color: var(--color-background-light);
  }

  .testimonial-carousel__arrow svg {
    width: 12px;
    height: 12px;
  }

  .testimonial-carousel__dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: var(--spacing-base);
  }

  .testimonial-carousel__dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--color-border);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .testimonial-carousel__dot--active {
    background-color: var(--color-primary);
  }

  @media screen and (max-width: 749px) {
    .testimonial-carousel__wrapper {
      padding: 0 30px;
    }

    .testimonial-carousel__arrow {
      width: 30px;
      height: 30px;
    }

    .testimonial-card__title {
      font-size: 16px;
    }

    .testimonial-card__content {
      font-size: 14px;
    }
  }
</style>

<script>
  // Testimonial carousel will be initialized by theme-utilities.js
  // All configuration is passed via data attributes on the carousel element
</script>


</div>
<div id="shopify-section-marketplace-logos" class="shopify-section"><div class="marketplace-logos" data-section-id="marketplace-logos" data-section-type="marketplace-logos">
  <div class="page-width">
    
      <div class="section-header text-center">
        <h2 class="section-title">Also available on</h2>
        
      </div>
    

    <div class="marketplace-logos__grid">
      
    </div>
  </div>
</div>

<style>
  .marketplace-logos {
    margin: var(--spacing-extra-loose) 0;
  }

  .marketplace-logos__grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-loose);
    margin-top: var(--spacing-base);
  }

  .marketplace-logos__item {
    flex: 0 0 auto;
    padding: var(--spacing-base);
  }

  .marketplace-logos__link {
    display: block;
    text-decoration: none;
    color: inherit;
  }

  .marketplace-logos__logo-wrapper {
    max-width: 150px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .marketplace-logos__logo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .marketplace-logos__link:hover .marketplace-logos__logo {
    opacity: 1;
  }

  .marketplace-logos__placeholder {
    width: 150px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .marketplace-logos__placeholder-svg {
    width: 100%;
    height: 100%;
    max-width: 120px;
    opacity: 0.3;
  }

  @media screen and (max-width: 749px) {
    .marketplace-logos__grid {
      gap: var(--spacing-base);
    }

    .marketplace-logos__item {
      padding: var(--spacing-small);
    }

    .marketplace-logos__logo-wrapper {
      max-width: 120px;
      height: 50px;
    }
  }
</style>


</div>
<div id="shopify-section-newsletter" class="shopify-section"><div class="newsletter" data-section-id="newsletter" data-section-type="newsletter" style="background-color: #f7f7f7;">
  <div class="page-width">
    <div class="newsletter__wrapper">
      <div class="newsletter__content">
        
          <h2 class="newsletter__heading" style="color: #333333;">
            Subscribe to our newsletter
          </h2>
        

        
          <div class="newsletter__subheading rte" style="color: #333333;">
            <p>Sign up for exclusive offers, original stories, events and more.</p>
          </div>
        

        <form method="post" action="/contact#newsletter-form-newsletter" id="newsletter-form-newsletter" accept-charset="UTF-8" class="newsletter__form-wrapper">
          <input type="hidden" name="form_type" value="customer">
          <input type="hidden" name="utf8" value="✓">
          <input type="hidden" name="contact[tags]" value="newsletter">

          <div class="newsletter__form">
            <div class="newsletter__field">
              <input
                id="NewsletterEmail-newsletter"
                type="email"
                name="contact[email]"
                class="newsletter__input"
                value=""
                placeholder="Email address"
                required
                aria-required="true"
              >
              <label class="newsletter__label" for="NewsletterEmail-newsletter">
                Email address
              </label>
            </div>

            <button type="submit" class="newsletter__button button" name="commit">
              Subscribe
            </button>
          </div>

          <div class="newsletter__response"></div>
        </form>
      </div>
    </div>
  </div>
</div>

<style>
  .newsletter {
    margin: var(--spacing-extra-loose) 0;
    padding: var(--spacing-extra-loose) 0;
  }

  .newsletter__wrapper {
    display: flex;
    justify-content: center;
  }

  .newsletter__content {
    max-width: 600px;
    text-align: center;
  }

  .newsletter__heading {
    margin-bottom: var(--spacing-base);
    font-size: 28px;
  }

  .newsletter__subheading {
    margin-bottom: var(--spacing-loose);
  }

  .newsletter__form {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-tight);
  }

  .newsletter__field {
    position: relative;
    flex: 1 1 auto;
    min-width: 200px;
  }

  .newsletter__input {
    width: 100%;
    padding: var(--spacing-tight) var(--spacing-base);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    font-size: 16px;
    line-height: 1.5;
  }

  .newsletter__label {
    position: absolute;
    top: 0;
    left: var(--spacing-base);
    transform: translateY(-50%);
    background-color: var(--color-background);
    padding: 0 var(--spacing-tight);
    font-size: 12px;
    color: var(--color-secondary);
    pointer-events: none;
  }

  .newsletter__button {
    flex: 0 0 auto;
  }

  .form-message {
    margin-top: var(--spacing-base);
    padding: var(--spacing-tight) var(--spacing-base);
    border-radius: var(--border-radius);
  }

  .form-message--error {
    background-color: rgba(227, 44, 43, 0.1);
    color: #e32c2b;
  }

  .form-message--success {
    background-color: rgba(56, 180, 74, 0.1);
    color: #38b44a;
  }

  @media screen and (max-width: 749px) {
    .newsletter {
      padding: var(--spacing-loose) 0;
    }

    .newsletter__form {
      flex-direction: column;
    }

    .newsletter__heading {
      font-size: 24px;
    }
  }
</style>


</div>

    </main>

    <div id="shopify-section-footer" class="shopify-section"><footer class="site-footer" data-section-id="footer" data-section-type="footer">
  <div class="page-width">
    <div class="footer-wrapper">
      <div class="footer-block footer-block--menu">
        <h2 class="footer-block__title">Quick links</h2>
        
          <ul class="footer-nav">
            
              <li><a href="/search">Search</a></li>
            
          </ul>
        
      </div>

      

      <div class="footer-block footer-block--newsletter">
        <h2 class="footer-block__title">Subscribe to our newsletter</h2>
        
          <div class="footer-newsletter"><form method="post" action="/contact#ContactFooter" id="ContactFooter" accept-charset="UTF-8" class="contact-form"><input type="hidden" name="form_type" value="customer" /><input type="hidden" name="utf8" value="✓" /><input type="hidden" name="contact[tags]" value="newsletter">
              <div class="newsletter-form">
                <div class="field">
                  <input
                    id="NewsletterForm--footer"
                    type="email"
                    name="contact[email]"
                    class="field__input"
                    value=""
                    aria-required="true"
                    autocorrect="off"
                    autocapitalize="off"
                    autocomplete="email"
                    placeholder="Email address"
                    required
                  >
                  <label class="field__label" for="NewsletterForm--footer">
                    Email address
                  </label>
                </div>
                <button type="submit" class="button" name="commit">
                  Subscribe
                </button>
              </div></form></div>
        

        
          <div class="footer-social-wrapper">
            <h2 class="footer-block__title">Follow us</h2>
            <ul class="footer-social">
              
              
              
              
              
              
              
              
            </ul>
          </div>
        
      </div>
    </div>

    <div class="footer-bottom">
      <div class="copyright">
        &copy; 2025 Dyrect Test. All rights reserved.
      </div>
      
        <div class="payment-icons">
          
            <svg class="icon icon--payment" viewBox="0 0 38 24" xmlns="http://www.w3.org/2000/svg" role="img" width="38" height="24" aria-labelledby="pi-visa"><title id="pi-visa">Visa</title><path opacity=".07" d="M35 0H3C1.3 0 0 1.3 0 3v18c0 1.7 1.4 3 3 3h32c1.7 0 3-1.3 3-3V3c0-1.7-1.4-3-3-3z"/><path fill="#fff" d="M35 1c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H3c-1.1 0-2-.9-2-2V3c0-1.1.9-2 2-2h32"/><path d="M28.3 10.1H28c-.4 1-.7 1.5-1 3h1.9c-.3-1.5-.3-2.2-.6-3zm2.9 5.9h-1.7c-.1 0-.1 0-.2-.1l-.2-.9-.1-.2h-2.4c-.1 0-.2 0-.2.2l-.3.9c0 .1-.1.1-.1.1h-2.1l.2-.5L27 8.7c0-.5.3-.7.8-.7h1.5c.1 0 .2 0 .2.2l1.4 6.5c.1.4.2.7.2 1.1.1.1.1.1.1.2zm-13.4-.3l.4-1.8c.1 0 .2.1.2.1.7.3 1.4.5 2.1.4.2 0 .5-.1.7-.2.5-.2.5-.7.1-1.1-.2-.2-.5-.3-.8-.5-.4-.2-.8-.4-1.1-.7-1.2-1-.8-2.4-.1-3.1.6-.4.9-.8 1.7-.8 1.2 0 2.5 0 3.1.2h.1c-.1.6-.2 1.1-.4 1.7-.5-.2-1-.4-1.5-.4-.3 0-.6 0-.9.1-.2 0-.3.1-.4.2-.2.2-.2.5 0 .7l.5.4c.4.2.8.4 1.1.6.5.3 1 .8 1.1 1.4.2.9-.1 1.7-.9 2.3-.5.4-.7.6-1.4.6-1.4 0-2.5.1-3.4-.2-.1.2-.1.2-.2.1zm-3.5.3c.1-.7.1-.7.2-1 .5-2.2 1-4.5 1.4-6.7.1-.2.1-.3.3-.3H18c-.2 1.2-.4 2.1-.7 3.2-.3 1.5-.6 3-1 4.5 0 .2-.1.2-.3.2M5 8.2c0-.1.2-.2.3-.2h3.4c.5 0 .9.3 1 .8l.9 4.4c0 .1 0 .1.1.2 0-.1.1-.1.1-.1l2.1-5.1c-.1-.1 0-.2.1-.2h2.1c0 .1 0 .1-.1.2l-3.1 7.3c-.1.2-.1.3-.2.4-.1.1-.3 0-.5 0H9.7c-.1 0-.2 0-.2-.2L7.9 9.5c-.2-.2-.5-.5-.9-.6-.6-.3-1.7-.5-1.9-.5L5 8.2z" fill="#142688"/></svg>
          
            <svg class="icon icon--payment" viewBox="0 0 38 24" xmlns="http://www.w3.org/2000/svg" role="img" width="38" height="24" aria-labelledby="pi-master"><title id="pi-master">Mastercard</title><path opacity=".07" d="M35 0H3C1.3 0 0 1.3 0 3v18c0 1.7 1.4 3 3 3h32c1.7 0 3-1.3 3-3V3c0-1.7-1.4-3-3-3z"/><path fill="#fff" d="M35 1c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H3c-1.1 0-2-.9-2-2V3c0-1.1.9-2 2-2h32"/><circle fill="#EB001B" cx="15" cy="12" r="7"/><circle fill="#F79E1B" cx="23" cy="12" r="7"/><path fill="#FF5F00" d="M22 12c0-2.4-1.2-4.5-3-5.7-1.8 1.3-3 3.4-3 5.7s1.2 4.5 3 5.7c1.8-1.2 3-3.3 3-5.7z"/></svg>
          
            <svg class="icon icon--payment" xmlns="http://www.w3.org/2000/svg" role="img" aria-labelledby="pi-american_express" viewBox="0 0 38 24" width="38" height="24"><title id="pi-american_express">American Express</title><path fill="#000" d="M35 0H3C1.3 0 0 1.3 0 3v18c0 1.7 1.4 3 3 3h32c1.7 0 3-1.3 3-3V3c0-1.7-1.4-3-3-3Z" opacity=".07"/><path fill="#006FCF" d="M35 1c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H3c-1.1 0-2-.9-2-2V3c0-1.1.9-2 2-2h32Z"/><path fill="#FFF" d="M22.012 19.936v-8.421L37 11.528v2.326l-1.732 1.852L37 17.573v2.375h-2.766l-1.47-1.622-1.46 1.628-9.292-.02Z"/><path fill="#006FCF" d="M23.013 19.012v-6.57h5.572v1.513h-3.768v1.028h3.678v1.488h-3.678v1.01h3.768v1.531h-5.572Z"/><path fill="#006FCF" d="m28.557 19.012 3.083-3.289-3.083-3.282h2.386l1.884 2.083 1.89-2.082H37v.051l-3.017 3.23L37 18.92v.093h-2.307l-1.917-2.103-1.898 2.104h-2.321Z"/><path fill="#FFF" d="M22.71 4.04h3.614l1.269 2.881V4.04h4.46l.77 2.159.771-2.159H37v8.421H19l3.71-8.421Z"/><path fill="#006FCF" d="m23.395 4.955-2.916 6.566h2l.55-1.315h2.98l.55 1.315h2.05l-2.904-6.566h-2.31Zm.25 3.777.875-2.09.873 2.09h-1.748Z"/><path fill="#006FCF" d="M28.581 11.52V4.953l2.811.01L32.84 9l1.456-4.046H37v6.565l-1.74.016v-4.51l-1.644 4.494h-1.59L30.35 7.01v4.51h-1.768Z"/></svg>

          
            <svg class="icon icon--payment" viewBox="0 0 38 24" xmlns="http://www.w3.org/2000/svg" width="38" height="24" role="img" aria-labelledby="pi-paypal"><title id="pi-paypal">PayPal</title><path opacity=".07" d="M35 0H3C1.3 0 0 1.3 0 3v18c0 1.7 1.4 3 3 3h32c1.7 0 3-1.3 3-3V3c0-1.7-1.4-3-3-3z"/><path fill="#fff" d="M35 1c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H3c-1.1 0-2-.9-2-2V3c0-1.1.9-2 2-2h32"/><path fill="#003087" d="M23.9 8.3c.2-1 0-1.7-.6-2.3-.6-.7-1.7-1-3.1-1h-4.1c-.3 0-.5.2-.6.5L14 15.6c0 .2.1.4.3.4H17l.4-3.4 1.8-2.2 4.7-2.1z"/><path fill="#3086C8" d="M23.9 8.3l-.2.2c-.5 2.8-2.2 3.8-4.6 3.8H18c-.3 0-.5.2-.6.5l-.6 3.9-.2 1c0 .2.1.4.3.4H19c.3 0 .5-.2.5-.4v-.1l.4-2.4v-.1c0-.2.3-.4.5-.4h.3c2.1 0 3.7-.8 4.1-3.2.2-1 .1-1.8-.4-2.4-.1-.5-.3-.7-.5-.8z"/><path fill="#012169" d="M23.3 8.1c-.1-.1-.2-.1-.3-.1-.1 0-.2 0-.3-.1-.3-.1-.7-.1-1.1-.1h-3c-.1 0-.2 0-.2.1-.2.1-.3.2-.3.4l-.7 4.4v.1c0-.3.3-.5.6-.5h1.3c2.5 0 4.1-1 4.6-3.8v-.2c-.1-.1-.3-.2-.5-.2h-.1z"/></svg>
          
            <svg class="icon icon--payment" viewBox="0 0 38 24" xmlns="http://www.w3.org/2000/svg" role="img" width="38" height="24" aria-labelledby="pi-diners_club"><title id="pi-diners_club">Diners Club</title><path opacity=".07" d="M35 0H3C1.3 0 0 1.3 0 3v18c0 1.7 1.4 3 3 3h32c1.7 0 3-1.3 3-3V3c0-1.7-1.4-3-3-3z"/><path fill="#fff" d="M35 1c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H3c-1.1 0-2-.9-2-2V3c0-1.1.9-2 2-2h32"/><path d="M12 12v3.7c0 .3-.2.3-.5.2-1.9-.8-3-3.3-2.3-5.4.4-1.1 1.2-2 2.3-2.4.4-.2.5-.1.5.2V12zm2 0V8.3c0-.3 0-.3.3-.2 2.1.8 3.2 3.3 2.4 5.4-.4 1.1-1.2 2-2.3 2.4-.4.2-.4.1-.4-.2V12zm7.2-7H13c3.8 0 6.8 3.1 6.8 7s-3 7-6.8 7h8.2c3.8 0 6.8-3.1 6.8-7s-3-7-6.8-7z" fill="#3086C8"/></svg>
          
            <svg class="icon icon--payment" viewBox="0 0 38 24" width="38" height="24" role="img" aria-labelledby="pi-discover" fill="none" xmlns="http://www.w3.org/2000/svg"><title id="pi-discover">Discover</title><path fill="#000" opacity=".07" d="M35 0H3C1.3 0 0 1.3 0 3v18c0 1.7 1.4 3 3 3h32c1.7 0 3-1.3 3-3V3c0-1.7-1.4-3-3-3z"/><path d="M35 1c1.1 0 2 .9 2 2v18c0 1.1-.9 2-2 2H3c-1.1 0-2-.9-2-2V3c0-1.1.9-2 2-2h32z" fill="#fff"/><path d="M3.57 7.16H2v5.5h1.57c.83 0 1.43-.2 1.96-.63.63-.52 1-1.3 1-2.11-.01-1.63-1.22-2.76-2.96-2.76zm1.26 4.14c-.34.3-.77.44-1.47.44h-.29V8.1h.29c.69 0 1.11.12 1.47.44.37.33.59.84.59 1.37 0 .53-.22 1.06-.59 1.39zm2.19-4.14h1.07v5.5H7.02v-5.5zm3.69 2.11c-.64-.24-.83-.4-.83-.69 0-.35.34-.61.8-.61.32 0 .59.13.86.45l.56-.73c-.46-.4-1.01-.61-1.62-.61-.97 0-1.72.68-1.72 1.58 0 .76.35 1.15 1.35 1.51.42.15.63.25.74.31.21.14.32.34.32.57 0 .45-.35.78-.83.78-.51 0-.92-.26-1.17-.73l-.69.67c.49.73 1.09 1.05 1.9 1.05 1.11 0 1.9-.74 1.9-1.81.02-.89-.35-1.29-1.57-1.74zm1.92.65c0 1.62 1.27 2.87 2.9 2.87.46 0 .86-.09 1.34-.32v-1.26c-.43.43-.81.6-1.29.6-1.08 0-1.85-.78-1.85-1.9 0-1.06.79-1.89 1.8-1.89.51 0 .9.18 1.34.62V7.38c-.47-.24-.86-.34-1.32-.34-1.61 0-2.92 1.28-2.92 2.88zm12.76.94l-1.47-3.7h-1.17l2.33 5.64h.58l2.37-5.64h-1.16l-1.48 3.7zm3.13 1.8h3.04v-.93h-1.97v-1.48h1.9v-.93h-1.9V8.1h1.97v-.94h-3.04v5.5zm7.29-3.87c0-1.03-.71-1.62-1.95-1.62h-1.59v5.5h1.07v-2.21h.14l1.48 2.21h1.32l-1.73-2.32c.81-.17 1.26-.72 1.26-1.56zm-2.16.91h-.31V8.03h.33c.67 0 1.03.28 1.03.82 0 .55-.36.85-1.05.85z" fill="#231F20"/><path d="M20.16 12.86a2.931 2.931 0 100-5.862 2.931 2.931 0 000 5.862z" fill="url(#pi-paint0_linear)"/><path opacity=".65" d="M20.16 12.86a2.931 2.931 0 100-5.862 2.931 2.931 0 000 5.862z" fill="url(#pi-paint1_linear)"/><path d="M36.57 7.506c0-.1-.07-.15-.18-.15h-.16v.48h.12v-.19l.14.19h.14l-.16-.2c.06-.01.1-.06.1-.13zm-.2.07h-.02v-.13h.02c.06 0 .09.02.09.06 0 .05-.03.07-.09.07z" fill="#231F20"/><path d="M36.41 7.176c-.23 0-.42.19-.42.42 0 .23.19.42.42.42.23 0 .42-.19.42-.42 0-.23-.19-.42-.42-.42zm0 .77c-.18 0-.34-.15-.34-.35 0-.19.15-.35.34-.35.18 0 .33.16.33.35 0 .19-.15.35-.33.35z" fill="#231F20"/><path d="M37 12.984S27.09 19.873 8.976 23h26.023a2 2 0 002-1.984l.024-3.02L37 12.985z" fill="#F48120"/><defs><linearGradient id="pi-paint0_linear" x1="21.657" y1="12.275" x2="19.632" y2="9.104" gradientUnits="userSpaceOnUse"><stop stop-color="#F89F20"/><stop offset=".25" stop-color="#F79A20"/><stop offset=".533" stop-color="#F68D20"/><stop offset=".62" stop-color="#F58720"/><stop offset=".723" stop-color="#F48120"/><stop offset="1" stop-color="#F37521"/></linearGradient><linearGradient id="pi-paint1_linear" x1="21.338" y1="12.232" x2="18.378" y2="6.446" gradientUnits="userSpaceOnUse"><stop stop-color="#F58720"/><stop offset=".359" stop-color="#E16F27"/><stop offset=".703" stop-color="#D4602C"/><stop offset=".982" stop-color="#D05B2E"/></linearGradient></defs></svg>
          
        </div>
      
    </div>
  </div>
</footer>


</div>

    <!-- Cart Drawer -->
<div id="cart-drawer" class="cart-drawer">
  <div class="cart-drawer__header">
    <h2 class="cart-drawer__title">Your Cart</h2>
    <button class="cart-drawer__close" aria-label="Close cart">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>
  <div class="cart-drawer__content">
    <div class="cart-drawer__empty-state">
      <p>Your cart is empty</p>
      <a href="/collections/all" class="button">Continue Shopping</a>
    </div>
    <div class="cart-drawer__items">
      <!-- Cart items will be loaded here -->
    </div>
  </div>
  <div class="cart-drawer__footer">
    <div class="cart-drawer__subtotal">
      <span>Subtotal</span>
      <span class="cart-drawer__subtotal-price">Rs. 0.00</span>
    </div>
    <div class="cart-drawer__buttons">
      <a href="/cart" class="button button--secondary">View Cart</a>
      <a href="/checkout" class="button">Checkout</a>
    </div>
  </div>
</div>
<div id="cart-drawer-overlay" class="cart-drawer-overlay"></div>

<style>
  /* Cart Drawer Styles */
  .cart-drawer {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    max-width: 100%;
    height: 100%;
    background-color: var(--color-background);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: var(--z-index-modal);
    transition: right var(--transition-duration) var(--transition-timing);
    display: flex;
    flex-direction: column;
  }

  .cart-drawer.active {
    right: 0;
  }

  /* Loading state */
  .cart-drawer.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cart-drawer.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border-radius: 50%;
    border: 2px solid var(--color-border);
    border-top-color: var(--color-primary);
    animation: cart-spinner 0.6s linear infinite;
    z-index: 11;
  }

  @keyframes cart-spinner {
    to {
      transform: rotate(360deg);
    }
  }

  /* Error message */
  .cart-drawer__error {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 12;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .cart-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-index-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-duration) var(--transition-timing),
                visibility var(--transition-duration) var(--transition-timing);
  }

  .cart-drawer-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  .cart-drawer__header {
    padding: var(--spacing-base);
    border-bottom: var(--border-width) solid var(--color-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .cart-drawer__title {
    margin: 0;
    font-size: var(--font-size-large);
    font-weight: var(--font-weight-semibold);
  }

  .cart-drawer__close {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: var(--color-text);
  }

  .cart-drawer__content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-base);
  }

  .cart-drawer__empty-state {
    text-align: center;
    padding: var(--spacing-large) 0;
  }

  .cart-drawer__empty-state p {
    margin-bottom: var(--spacing-base);
    color: var(--color-text-light);
  }

  .cart-drawer__items {
    display: none;
  }

  .cart-drawer__item {
    display: flex;
    margin-bottom: var(--spacing-base);
    padding-bottom: var(--spacing-base);
    border-bottom: var(--border-width) solid var(--color-border);
  }

  .cart-drawer__item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-right: var(--spacing-base);
  }

  .cart-drawer__item-details {
    flex: 1;
  }

  .cart-drawer__item-title {
    font-size: var(--font-size-base);
    margin: 0 0 var(--spacing-small);
    font-weight: var(--font-weight-medium);
  }

  .cart-drawer__item-variant {
    font-size: var(--font-size-small);
    color: var(--color-text-light);
    margin-bottom: var(--spacing-small);
  }

  .cart-drawer__item-price {
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-small);
  }

  .cart-drawer__item-quantity {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-small);
  }

  .cart-drawer__item-quantity-button {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-background-light);
    border: var(--border-width) solid var(--color-border);
    cursor: pointer;
  }

  .cart-drawer__item-quantity-input {
    width: 40px;
    height: 24px;
    text-align: center;
    border: var(--border-width) solid var(--color-border);
    margin: 0 var(--spacing-extra-small);
  }

  .cart-drawer__item-remove {
    background: none;
    border: none;
    color: var(--color-text-light);
    text-decoration: underline;
    cursor: pointer;
    font-size: var(--font-size-small);
    padding: 0;
    margin-top: var(--spacing-small);
  }

  .cart-drawer__footer {
    padding: var(--spacing-base);
    border-top: var(--border-width) solid var(--color-border);
  }

  .cart-drawer__subtotal {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-base);
    font-weight: var(--font-weight-semibold);
  }

  .cart-drawer__buttons {
    display: flex;
    gap: var(--spacing-small);
  }

  .cart-drawer__buttons .button {
    flex: 1;
    text-align: center;
  }

  @media screen and (max-width: var(--breakpoint-small)) {
    .cart-drawer {
      width: 100%;
    }
  }
</style>

  </div>
</body>
</html>
