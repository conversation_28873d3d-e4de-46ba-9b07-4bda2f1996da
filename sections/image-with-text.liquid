<div class="image-with-text" data-section-id="{{ section.id }}" data-section-type="image-with-text">
  <div class="page-width">
    <div class="image-with-text__wrapper" style="background-color: {{ section.settings.background_color }};">
      <div class="image-with-text__grid image-with-text__grid--{{ section.settings.layout }}">
        <div class="image-with-text__image-container">
          {% if section.settings.image != blank %}
            <img
              srcset="{%- if section.settings.image.width >= 375 -%}{{ section.settings.image | img_url: '375x' }} 375w,{%- endif -%}
                      {%- if section.settings.image.width >= 750 -%}{{ section.settings.image | img_url: '750x' }} 750w,{%- endif -%}
                      {%- if section.settings.image.width >= 1100 -%}{{ section.settings.image | img_url: '1100x' }} 1100w,{%- endif -%}
                      {%- if section.settings.image.width >= 1500 -%}{{ section.settings.image | img_url: '1500x' }} 1500w,{%- endif -%}"
              src="{{ section.settings.image | img_url: '750x' }}"
              sizes="(min-width: 750px) 50vw, 100vw"
              alt="{{ section.settings.image.alt | escape }}"
              width="{{ section.settings.image.width }}"
              height="{{ section.settings.image.height }}"
              loading="lazy"
              class="image-with-text__image"
            >
          {% else %}
            {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
          {% endif %}
        </div>
        
        <div class="image-with-text__content">
          {% if section.settings.subheading != blank %}
            <div class="image-with-text__subheading" style="color: {{ section.settings.text_color }};">
              {{ section.settings.subheading | escape }}
            </div>
          {% endif %}
          
          {% if section.settings.heading != blank %}
            <h2 class="image-with-text__heading" style="color: {{ section.settings.text_color }};">
              {{ section.settings.heading | escape }}
            </h2>
          {% endif %}
          
          {% if section.settings.text != blank %}
            <div class="image-with-text__text rte" style="color: {{ section.settings.text_color }};">
              {{ section.settings.text }}
            </div>
          {% endif %}
          
          {% if section.settings.button_label != blank and section.settings.button_link != blank %}
            <a href="{{ section.settings.button_link }}" class="button image-with-text__button">
              {{ section.settings.button_label | escape }}
            </a>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .image-with-text {
    margin: var(--spacing-extra-loose) 0;
  }
  
  .image-with-text__wrapper {
    border-radius: var(--border-radius);
    overflow: hidden;
  }
  
  .image-with-text__grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
  }
  
  .image-with-text__grid--right {
    grid-template-areas: "content image";
  }
  
  .image-with-text__grid--left {
    grid-template-areas: "image content";
  }
  
  .image-with-text__image-container {
    grid-area: image;
    overflow: hidden;
  }
  
  .image-with-text__image {
    display: block;
    width: 100%;
    height: auto;
    transition: transform 0.8s ease;
  }
  
  .image-with-text__image:hover {
    transform: scale(1.05);
  }
  
  .image-with-text__content {
    grid-area: content;
    padding: var(--spacing-extra-loose);
  }
  
  .image-with-text__subheading {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--spacing-tight);
  }
  
  .image-with-text__heading {
    font-size: 32px;
    margin-bottom: var(--spacing-base);
  }
  
  .image-with-text__text {
    margin-bottom: var(--spacing-loose);
  }
  
  .image-with-text__button {
    display: inline-block;
  }
  
  @media screen and (max-width: 749px) {
    .image-with-text__grid {
      grid-template-columns: 1fr;
    }
    
    .image-with-text__grid--right,
    .image-with-text__grid--left {
      grid-template-areas: 
        "image"
        "content";
    }
    
    .image-with-text__content {
      padding: var(--spacing-base);
    }
    
    .image-with-text__heading {
      font-size: 24px;
    }
  }
</style>

{% schema %}
{
  "name": "Image with text",
  "settings": [
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "left",
          "label": "Image on left"
        },
        {
          "value": "right",
          "label": "Image on right"
        }
      ],
      "default": "left"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Subheading"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Image with text"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text",
      "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label",
      "default": "Button label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f7f7f7"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    }
  ],
  "presets": [
    {
      "name": "Image with text",
      "category": "Image"
    }
  ]
}
{% endschema %}
