{{ 'product-card-styled.css' | asset_url | stylesheet_tag }}

<div class="styled-product-grid-section">
  <div class="page-width">
    {% if section.settings.title != blank %}
      <div class="section-header text-center">
        <h2 class="section-title">{{ section.settings.title | escape }}</h2>
        {% if section.settings.description != blank %}
          <div class="section-description">{{ section.settings.description }}</div>
        {% endif %}
      </div>
    {% endif %}

    {% if section.settings.collection != blank %}
      {% assign collection = collections[section.settings.collection] %}
      {% if collection.products.size > 0 %}
        <div class="product-grid-styled">
          {% for product in collection.products limit: section.settings.products_to_show %}
            {% render 'product-card-styled', product: product %}
          {% endfor %}
        </div>
      {% else %}
        <div class="grid">
          <div class="grid__item">
            <div class="text-center">
              <p>{{ 'collections.general.no_matches' | t }}</p>
            </div>
          </div>
        </div>
      {% endif %}
    {% endif %}

    {% if section.settings.show_view_all and section.settings.collection != blank %}
      <div class="center">
        <a href="{{ collections[section.settings.collection].url }}" class="button">
          {{ 'collections.general.view_all' | t }}
        </a>
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Styled Product Grid",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Featured Products"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 12,
      "step": 1,
      "default": 4,
      "label": "Products to show"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "Show 'View all' button"
    }
  ],
  "presets": [
    {
      "name": "Styled Product Grid",
      "category": "Collection"
    }
  ]
}
{% endschema %}
