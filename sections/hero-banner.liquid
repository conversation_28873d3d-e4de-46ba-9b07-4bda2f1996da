<div class="hero-banner" data-section-id="{{ section.id }}" data-section-type="hero-banner">
  <div class="hero-banner__wrapper hero-banner__wrapper--{{ section.settings.content_position }}" style="--desktop-height: {{ section.settings.height }}px; --mobile-height: {{ section.settings.height_mobile }}px;">
    {% if section.settings.image != blank %}
      <div class="hero-banner__image-container image-container image-container--banner" style="aspect-ratio: {{ section.settings.image.width | divided_by: section.settings.image.height | times: 100 | round | divided_by: 100.0 }};">
        <!-- Preload the LCP image for better performance -->
        <link rel="preload" as="image"
              href="{{ section.settings.image | img_url: '1500x' }}"
              imagesrcset="{%- if section.settings.image.width >= 375 -%}{{ section.settings.image | img_url: '375x' }} 375w,{%- endif -%}
                  {%- if section.settings.image.width >= 750 -%}{{ section.settings.image | img_url: '750x' }} 750w,{%- endif -%}
                  {%- if section.settings.image.width >= 1100 -%}{{ section.settings.image | img_url: '1100x' }} 1100w,{%- endif -%}
                  {%- if section.settings.image.width >= 1500 -%}{{ section.settings.image | img_url: '1500x' }} 1500w,{%- endif -%}
                  {%- if section.settings.image.width >= 2200 -%}{{ section.settings.image | img_url: '2200x' }} 2200w,{%- endif -%}
                  {%- if section.settings.image.width >= 3000 -%}{{ section.settings.image | img_url: '3000x' }} 3000w,{%- endif -%}"
              imagesizes="100vw">

        <!-- Image placeholder and loading indicator -->
        <div class="image-placeholder"></div>
        <div class="image-loading"></div>

        <img
          srcset="{%- if section.settings.image.width >= 375 -%}{{ section.settings.image | img_url: '375x' }} 375w,{%- endif -%}
                  {%- if section.settings.image.width >= 750 -%}{{ section.settings.image | img_url: '750x' }} 750w,{%- endif -%}
                  {%- if section.settings.image.width >= 1100 -%}{{ section.settings.image | img_url: '1100x' }} 1100w,{%- endif -%}
                  {%- if section.settings.image.width >= 1500 -%}{{ section.settings.image | img_url: '1500x' }} 1500w,{%- endif -%}
                  {%- if section.settings.image.width >= 2200 -%}{{ section.settings.image | img_url: '2200x' }} 2200w,{%- endif -%}
                  {%- if section.settings.image.width >= 3000 -%}{{ section.settings.image | img_url: '3000x' }} 3000w,{%- endif -%}"
          src="{{ section.settings.image | img_url: '1500x' }}"
          sizes="100vw"
          alt="{% if section.settings.image.alt != blank %}{{ section.settings.image.alt | escape }}{% else %}{{ section.settings.title | escape }}{% endif %}"
          width="{{ section.settings.image.width }}"
          height="{{ section.settings.image.height }}"
          loading="eager"
          fetchpriority="high"
          class="hero-banner__image image-fill{% if section.settings.image_overlay %} hero-banner__image--overlay{% endif %}"
          onload="this.classList.add('is-loaded')"
        >
      </div>
    {% else %}
      <div class="hero-banner__image-container" style="background-color: {{ section.settings.background_color }};">
      </div>
    {% endif %}

    <div class="hero-banner__content" role="region" aria-label="Hero banner content">
      {% if section.settings.subtitle != blank %}
        <div class="hero-banner__subtitle" style="color: {{ section.settings.text_color }};">
          {{ section.settings.subtitle | escape }}
        </div>
      {% endif %}

      {% if section.settings.title != blank %}
        <h1 class="hero-banner__heading" style="color: {{ section.settings.text_color }};">
          {{ section.settings.title | escape }}
        </h1>
      {% endif %}

      {% if section.settings.text != blank %}
        <div class="hero-banner__text" style="color: {{ section.settings.text_color }};">
          {{ section.settings.text }}
        </div>
      {% endif %}

      {% if section.settings.button_label != blank and section.settings.button_link != blank %}
        <div class="hero-banner__buttons">
          {% render 'button',
            text: section.settings.button_label,
            url: section.settings.button_link,
            with_arrow: true,
            aria_label: section.settings.button_label | append: ' - ' | append: section.settings.title
          %}

          {% if section.settings.button_label_2 != blank and section.settings.button_link_2 != blank %}
            {% render 'button',
              text: section.settings.button_label_2,
              url: section.settings.button_link_2,
              style: 'secondary',
              with_arrow: false,
              aria_label: section.settings.button_label_2 | append: ' - ' | append: section.settings.title
            %}
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .hero-banner__wrapper {
    height: var(--desktop-height);
  }

  .hero-banner__image-container {
    height: 100%;
    position: relative;
    overflow: hidden;
  }

  .hero-banner__image {
    object-position: center;
  }

  .hero-banner__content {
    max-width: 600px;
  }

  .hero-banner__wrapper--center .hero-banner__content {
    text-align: center;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .hero-banner__wrapper--left .hero-banner__content {
    text-align: left;
    left: 10%;
    transform: translateY(-50%);
  }

  .hero-banner__wrapper--right .hero-banner__content {
    text-align: right;
    right: 10%;
    left: auto;
    transform: translateY(-50%);
  }

  .hero-banner__subtitle {
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 15px;
  }

  .hero-banner__buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 30px;
  }

  .hero-banner__wrapper--center .hero-banner__buttons {
    justify-content: center;
  }

  .hero-banner__wrapper--right .hero-banner__buttons {
    justify-content: flex-end;
  }

  @media screen and (max-width: 749px) {
    .hero-banner__wrapper {
      height: var(--mobile-height);
    }

    .hero-banner__content {
      max-width: 90%;
    }

    .hero-banner__wrapper--left .hero-banner__content,
    .hero-banner__wrapper--right .hero-banner__content {
      left: 50%;
      right: auto;
      transform: translate(-50%, -50%);
      text-align: center;
    }

    .hero-banner__buttons {
      flex-direction: column;
      width: 100%;
    }
  }
</style>

{% schema %}
{
  "name": "Hero banner",
  "settings": [
    {
      "type": "header",
      "content": "Image"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "checkbox",
      "id": "image_overlay",
      "label": "Add image overlay",
      "default": true
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color (if no image)",
      "default": "#f7f7f7"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "New collection"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Welcome to our store"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text",
      "default": "<p>Add a welcome message to your customers</p>"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#FFFFFF"
    },
    {
      "type": "header",
      "content": "Buttons"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Primary button label",
      "default": "Shop now"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Primary button link"
    },
    {
      "type": "text",
      "id": "button_label_2",
      "label": "Secondary button label"
    },
    {
      "type": "url",
      "id": "button_link_2",
      "label": "Secondary button link"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "content_position",
      "label": "Content position",
      "options": [
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "height",
      "min": 400,
      "max": 1000,
      "step": 50,
      "unit": "px",
      "label": "Desktop height",
      "default": 650
    },
    {
      "type": "range",
      "id": "height_mobile",
      "min": 300,
      "max": 700,
      "step": 50,
      "unit": "px",
      "label": "Mobile height",
      "default": 500
    }
  ],
  "presets": [
    {
      "name": "Hero banner",
      "category": "Image"
    }
  ]
}
{% endschema %}
