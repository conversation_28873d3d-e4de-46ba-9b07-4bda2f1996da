{% comment %}
  Brand Story Section
  - Displays a brand logo, title, story text, and a call-to-action button
  - All elements are customizable from the theme editor
{% endcomment %}

<div class="brand-story" id="brand-story-{{ section.id }}" data-section-id="{{ section.id }}" data-section-type="brand-story">
  {% if section.settings.background_image != blank %}
    <div class="brand-story__background-image-container">
      <img
        src="{{ section.settings.background_image | img_url: '2000x' }}"
        srcset="{{ section.settings.background_image | img_url: '2000x' }} 2000w,
                {{ section.settings.background_image | img_url: '1500x' }} 1500w,
                {{ section.settings.background_image | img_url: '1000x' }} 1000w,
                {{ section.settings.background_image | img_url: '750x' }} 750w"
        sizes="100vw"
        alt=""
        class="brand-story__background-image"
        loading="lazy"
      >
      {% if section.settings.overlay_opacity > 0 %}
        <div class="brand-story__overlay" style="background-color: {{ section.settings.overlay_color }}; opacity: {{ section.settings.overlay_opacity | divided_by: 100.0 }};"></div>
      {% endif %}
    </div>
  {% endif %}

  <div class="page-width">
    <div class="brand-story__container">
      {% if section.settings.heading_first_word != blank and section.settings.heading_second_word != blank %}
        {% render 'section-heading',
          first_word: section.settings.heading_first_word,
          second_word: section.settings.heading_second_word,
          outline_color: section.settings.heading_outline_color,
          filled_color: section.settings.heading_filled_color
        %}
      {% elsif section.settings.logo != blank %}
        <div class="brand-story__logo-container">
          <img
            src="{{ section.settings.logo | img_url: 'medium' }}"
            alt="{{ section.settings.title | escape }}"
            class="brand-story__logo"
            width="{{ section.settings.logo_width }}"
            loading="lazy"
          >
        </div>
      {% endif %}

      {% if section.settings.title != blank %}
        <h2 class="brand-story__title">{{ section.settings.title }}</h2>
      {% endif %}

      {% if section.settings.subtitle != blank %}
        <div class="brand-story__subtitle">{{ section.settings.subtitle }}</div>
      {% endif %}

      {% if section.settings.text != blank %}
        <div class="brand-story__text">{{ section.settings.text }}</div>
      {% endif %}

      {% if section.settings.button_label != blank and section.settings.button_link != blank %}
        <div class="brand-story__button-container">
          <a href="{{ section.settings.button_link }}" class="brand-story__button button">
            {{ section.settings.button_label | escape }}
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .brand-story {
    padding: var(--spacing-extra-loose) 0;
    background-color: {{ section.settings.background_color }};
    color: {{ section.settings.text_color }};
    position: relative;
    overflow: hidden;
  }

  .brand-story__background-image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  .brand-story__background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .brand-story__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .brand-story__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
  }

  .brand-story__logo-container {
    margin-bottom: var(--spacing-base);
  }

  .brand-story__logo {
    max-width: 100%;
    height: auto;
  }

  .brand-story__title {
    font-family: var(--font-heading);
    font-size: var(--font-size-h1);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-tight);
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: {{ section.settings.title_color }};
  }

  .brand-story__subtitle {
    font-family: var(--font-body);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-base);
    max-width: 90%;
  }

  .brand-story__text {
    font-family: var(--font-body);
    font-size: var(--font-size-base);
    line-height: 1.6;
    margin-bottom: var(--spacing-base);
    max-width: 90%;
  }

  .brand-story__button-container {
    margin-top: var(--spacing-base);
  }

  .brand-story__button {
    background-color: {{ section.settings.button_background }};
    color: {{ section.settings.button_text_color }};
    border: 1px solid {{ section.settings.button_background }};
    min-width: 180px;
    transition: all 0.3s ease;
  }

  .brand-story__button:hover {
    background-color: {{ section.settings.button_background | color_darken: 10 }};
    color: {{ section.settings.button_text_color }};
  }

  @media screen and (max-width: 749px) {
    .brand-story__title {
      font-size: calc(var(--font-size-h1) * 0.8);
    }

    .brand-story__subtitle,
    .brand-story__text {
      max-width: 100%;
    }
  }
</style>

{% schema %}
{
  "name": "Brand Story",
  "tag": "section",
  "class": "section section-brand-story",
  "settings": [
    {
      "type": "header",
      "content": "Heading Style"
    },
    {
      "type": "text",
      "id": "heading_first_word",
      "label": "Heading First Word (Outlined)",
      "default": "Our"
    },
    {
      "type": "text",
      "id": "heading_second_word",
      "label": "Heading Second Word (Filled)",
      "default": "Story"
    },
    {
      "type": "color",
      "id": "heading_outline_color",
      "label": "Outline Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "heading_filled_color",
      "label": "Filled Text Color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Background"
    },
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "Background image"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Overlay color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Overlay opacity",
      "default": 50
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color (if no image)",
      "default": "#f8f8f8"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo"
    },
    {
      "type": "range",
      "id": "logo_width",
      "min": 50,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Logo width",
      "default": 120
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "BRAND NAME"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "BRAND TAGLINE: TRAVEL-INSPIRED SUSTAINABLE CLOTHING & MORE"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text",
      "default": "<p>Founded in 2013, our brand is a design house for travelers, adventurers and nature lovers. From versatile products to eco-friendly bags, our gear is made for those who roam.</p>"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button label",
      "default": "OUR STORY"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#000000"
    }
  ],
  "presets": [
    {
      "name": "Brand Story",
      "category": "Text",
      "settings": {
        "title": "BRAND NAME",
        "subtitle": "BRAND TAGLINE: TRAVEL-INSPIRED SUSTAINABLE CLOTHING & MORE",
        "text": "<p>Founded in 2013, our brand is a design house for travelers, adventurers and nature lovers. From versatile products to eco-friendly bags, our gear is made for those who roam.</p>",
        "button_label": "OUR STORY",
        "background_color": "#f8f8f8"
      }
    }
  ]
}
{% endschema %}
