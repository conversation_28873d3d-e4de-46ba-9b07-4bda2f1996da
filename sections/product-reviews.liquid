<style>
  .product-reviews-section {
    padding: 60px 0;
    border-top: 1px solid #e0e0e0;
    margin-top: 60px;
  }

  .product-reviews {
    max-width: 1200px;
    margin: 0 auto;
  }

  .product-reviews__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .product-reviews__title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }

  .product-reviews__write-button {
    padding: 10px 20px;
    background-color: #000;
    color: #fff;
    border: none;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .product-reviews__write-button:hover {
    background-color: #333;
  }

  .product-reviews__summary {
    display: flex;
    margin-bottom: 40px;
  }

  .product-reviews__rating {
    flex: 0 0 200px;
    text-align: center;
    padding-right: 40px;
    border-right: 1px solid #e0e0e0;
  }

  .product-reviews__average {
    font-size: 64px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 10px;
  }

  .product-reviews__stars {
    margin: 10px 0;
    font-size: 24px;
    color: #000;
  }

  .product-reviews__count {
    color: #666;
    font-size: 16px;
  }

  .product-reviews__breakdown {
    flex: 1;
    padding-left: 40px;
  }

  .review-breakdown__item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .review-breakdown__star {
    flex: 0 0 80px;
    font-size: 14px;
    font-weight: 500;
  }

  .review-breakdown__bar {
    flex: 1;
    height: 8px;
    background-color: #eee;
    border-radius: 4px;
    margin: 0 15px;
    overflow: hidden;
  }

  .review-breakdown__fill {
    height: 100%;
    background-color: #000;
  }

  .review-breakdown__count {
    flex: 0 0 30px;
    font-size: 14px;
    color: #666;
    text-align: right;
  }

  .product-reviews__list {
    margin-top: 40px;
    border-top: 1px solid #e0e0e0;
  }

  .review-item {
    padding: 30px 0;
    border-bottom: 1px solid #e0e0e0;
  }

  .review-item__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .review-item__author {
    font-weight: 600;
    font-size: 16px;
  }

  .review-item__date {
    color: #666;
    font-size: 14px;
  }

  .review-item__rating {
    margin-bottom: 10px;
    font-size: 18px;
    color: #000;
  }

  .review-item__title {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 10px;
  }

  .review-item__content {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #333;
  }

  .review-item__helpful {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
  }

  .review-item__helpful-button {
    margin-left: 10px;
    padding: 5px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    background: none;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .review-item__helpful-button:hover {
    background-color: #f5f5f5;
  }

  .review-item__helpful-button.active {
    background-color: #000;
    color: #fff;
    border-color: #000;
  }

  @media screen and (max-width: 767px) {
    .product-reviews__summary {
      flex-direction: column;
    }

    .product-reviews__rating {
      flex: none;
      padding-right: 0;
      border-right: none;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 20px;
      margin-bottom: 20px;
    }

    .product-reviews__breakdown {
      padding-left: 0;
    }
  }
</style>

<div class="product-reviews-section" data-section-id="{{ section.id }}" data-section-type="product-reviews">
  <div class="product-reviews">
    <div class="product-reviews__header">
      <h2 class="product-reviews__title">{{ section.settings.reviews_section_title }}</h2>
      <button class="product-reviews__write-button">Write a review</button>
    </div>
    
    <div class="product-reviews__summary">
      {% if product.metafields.reviews.rating %}
        <div class="product-reviews__rating">
          <div class="product-reviews__average">{{ product.metafields.reviews.rating | round: 1 }}</div>
          <div class="product-reviews__stars">★★★★★</div>
          <div class="product-reviews__count">{{ product.metafields.reviews.rating_count }} reviews</div>
        </div>
        
        <div class="product-reviews__breakdown">
          {% for i in (5..1) %}
            {% assign star_percentage = product.metafields.reviews.rating_distribution[i] | default: 0 | times: 100.0 | divided_by: product.metafields.reviews.rating_count | default: 0 %}
            <div class="review-breakdown__item">
              <div class="review-breakdown__star">{{ i }} stars</div>
              <div class="review-breakdown__bar">
                <div class="review-breakdown__fill" style="width: {{ star_percentage }}%;"></div>
              </div>
              <div class="review-breakdown__count">{{ product.metafields.reviews.rating_distribution[i] | default: 0 }}</div>
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="product-reviews__rating">
          <div class="product-reviews__average">4.8</div>
          <div class="product-reviews__stars">★★★★★</div>
          <div class="product-reviews__count">165 reviews</div>
        </div>
        
        <div class="product-reviews__breakdown">
          <div class="review-breakdown__item">
            <div class="review-breakdown__star">5 stars</div>
            <div class="review-breakdown__bar">
              <div class="review-breakdown__fill" style="width: 88%;"></div>
            </div>
            <div class="review-breakdown__count">145</div>
          </div>
          
          <div class="review-breakdown__item">
            <div class="review-breakdown__star">4 stars</div>
            <div class="review-breakdown__bar">
              <div class="review-breakdown__fill" style="width: 10%;"></div>
            </div>
            <div class="review-breakdown__count">17</div>
          </div>
          
          <div class="review-breakdown__item">
            <div class="review-breakdown__star">3 stars</div>
            <div class="review-breakdown__bar">
              <div class="review-breakdown__fill" style="width: 2%;"></div>
            </div>
            <div class="review-breakdown__count">3</div>
          </div>
          
          <div class="review-breakdown__item">
            <div class="review-breakdown__star">2 stars</div>
            <div class="review-breakdown__bar">
              <div class="review-breakdown__fill" style="width: 0%;"></div>
            </div>
            <div class="review-breakdown__count">0</div>
          </div>
          
          <div class="review-breakdown__item">
            <div class="review-breakdown__star">1 star</div>
            <div class="review-breakdown__bar">
              <div class="review-breakdown__fill" style="width: 0%;"></div>
            </div>
            <div class="review-breakdown__count">0</div>
          </div>
        </div>
      {% endif %}
    </div>
    
    <div class="product-reviews__list">
      {% if product.metafields.reviews.reviews %}
        {% for review in product.metafields.reviews.reviews %}
          <div class="review-item">
            <div class="review-item__header">
              <div class="review-item__author">{{ review.author }}</div>
              <div class="review-item__date">{{ review.created_at | date: '%b %d, %Y' }}</div>
            </div>
            <div class="review-item__rating">
              {% for i in (1..5) %}
                {% if i <= review.rating %}★{% else %}☆{% endif %}
              {% endfor %}
            </div>
            <div class="review-item__title">{{ review.title }}</div>
            <div class="review-item__content">
              {{ review.body }}
            </div>
            <div class="review-item__helpful">
              Was this review helpful?
              <button class="review-item__helpful-button">Yes</button>
              <button class="review-item__helpful-button">No</button>
            </div>
          </div>
        {% endfor %}
      {% else %}
        <div class="review-item">
          <div class="review-item__header">
            <div class="review-item__author">Sarah J.</div>
            <div class="review-item__date">2 weeks ago</div>
          </div>
          <div class="review-item__rating">★★★★★</div>
          <div class="review-item__title">Perfect for rainy days!</div>
          <div class="review-item__content">
            This jacket is amazing! It kept me completely dry during a heavy downpour and it's surprisingly breathable. The fit is perfect and I love the color. Highly recommend!
          </div>
          <div class="review-item__helpful">
            Was this review helpful?
            <button class="review-item__helpful-button">Yes</button>
            <button class="review-item__helpful-button">No</button>
          </div>
        </div>
        
        <div class="review-item">
          <div class="review-item__header">
            <div class="review-item__author">Michael T.</div>
            <div class="review-item__date">1 month ago</div>
          </div>
          <div class="review-item__rating">★★★★★</div>
          <div class="review-item__title">Great quality</div>
          <div class="review-item__content">
            The quality of this jacket is outstanding. It's lightweight but still provides good warmth. The pockets are spacious and the hood is adjustable. Very happy with my purchase.
          </div>
          <div class="review-item__helpful">
            Was this review helpful?
            <button class="review-item__helpful-button">Yes</button>
            <button class="review-item__helpful-button">No</button>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Review functionality
    const writeReviewButton = document.querySelector('.product-reviews__write-button');
    if (writeReviewButton) {
      writeReviewButton.addEventListener('click', function() {
        // This would typically open a review form
        console.log('Open review form');
      });
    }
    
    const helpfulButtons = document.querySelectorAll('.review-item__helpful-button');
    helpfulButtons.forEach(button => {
      button.addEventListener('click', function() {
        // This would typically mark a review as helpful
        this.classList.toggle('active');
      });
    });
  });
</script>

{% schema %}
{
  "name": "Product Reviews",
  "settings": [
    {
      "type": "text",
      "id": "reviews_section_title",
      "label": "Reviews section title",
      "default": "Customer Reviews"
    }
  ],
  "presets": [
    {
      "name": "Product Reviews",
      "category": "Product"
    }
  ]
}
{% endschema %}
