<div class="testimonial-carousel"
  data-section-id="{{ section.id }}"
  data-section-type="testimonial-carousel"
  data-slides-per-view="{{ section.settings.testimonials_per_row }}"
  data-slides-per-view-tablet="{{ section.settings.testimonials_per_row_tablet }}"
  data-slides-per-view-mobile="{{ section.settings.testimonials_per_row_mobile }}"
  data-autoplay-speed="{{ section.settings.autoplay_speed }}"
  data-autoplay="{{ section.settings.autoplay }}">
  <div class="page-width">
    {% if section.settings.heading_first_word != blank and section.settings.heading_second_word != blank %}
      {% render 'section-heading',
        first_word: section.settings.heading_first_word,
        second_word: section.settings.heading_second_word,
        outline_color: section.settings.heading_outline_color,
        filled_color: section.settings.heading_filled_color
      %}
    {% elsif section.settings.title != blank %}
      <div class="section-header text-center">
        <h2 class="section-title">{{ section.settings.title | escape }}</h2>
      </div>
    {% endif %}

    {% if section.settings.description != blank %}
      <div class="section-description rte text-center">
        {{ section.settings.description }}
      </div>
    {% endif %}

    <div class="testimonial-carousel__wrapper">
      <div id="testimonial-carousel-{{ section.id }}" class="keen-slider testimonial-carousel__slider">
        {% for block in section.blocks %}
          <div class="keen-slider__slide testimonial-carousel__slide" {{ block.shopify_attributes }}>
            <div class="testimonial-card">
              <div class="testimonial-card__rating">
                {% assign rating = block.settings.rating | default: 5 %}
                {% for i in (1..5) %}
                  {% if i <= rating %}
                    <span class="testimonial-card__star testimonial-card__star--filled">
                      <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                      </svg>
                    </span>
                  {% else %}
                    <span class="testimonial-card__star">
                      <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                      </svg>
                    </span>
                  {% endif %}
                {% endfor %}
              </div>

              {% if block.settings.title != blank %}
                <h3 class="testimonial-card__title">{{ block.settings.title | escape }}</h3>
              {% endif %}

              {% if block.settings.testimonial != blank %}
                <div class="testimonial-card__content">
                  {{ block.settings.testimonial }}
                </div>
              {% endif %}

              <div class="testimonial-card__author">
                {% if block.settings.author_image != blank %}
                  <div class="testimonial-card__author-image-wrapper">
                    <img
                      src="{{ block.settings.author_image | img_url: '100x100', crop: 'center' }}"
                      srcset="{{ block.settings.author_image | img_url: '100x100', crop: 'center' }} 1x, {{ block.settings.author_image | img_url: '200x200', crop: 'center' }} 2x"
                      alt="{{ block.settings.author_name | escape }}"
                      loading="lazy"
                      width="50"
                      height="50"
                      class="testimonial-card__author-image"
                    >
                  </div>
                {% endif %}
                <div class="testimonial-card__author-info">
                  {% if block.settings.author_name != blank %}
                    <div class="testimonial-card__author-name">{{ block.settings.author_name | escape }}</div>
                  {% endif %}
                  {% if block.settings.author_title != blank %}
                    <div class="testimonial-card__author-title">{{ block.settings.author_title | escape }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>

      <div class="testimonial-carousel__controls">
        <button type="button" class="testimonial-carousel__arrow testimonial-carousel__arrow--prev" data-carousel-prev aria-label="Previous testimonials">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
            <path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/>
          </svg>
        </button>
        <button type="button" class="testimonial-carousel__arrow testimonial-carousel__arrow--next" data-carousel-next aria-label="Next testimonials">
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
            <path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"/>
          </svg>
        </button>
      </div>

      {% if section.settings.show_dots %}
        <div class="testimonial-carousel__dots" data-carousel-dots></div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .testimonial-carousel {
    margin: var(--spacing-extra-loose) 0;
  }

  .testimonial-carousel__wrapper {
    position: relative;
    padding: 0 40px;
  }

  .testimonial-carousel__slider {
    overflow: visible;
  }

  .testimonial-carousel__slide {
    padding: var(--spacing-base);
    height: auto;
    box-sizing: border-box;
    /* The width will be set dynamically by JavaScript */
  }

  /* Ensure the keen-slider properly displays slides */
  .testimonial-carousel .keen-slider {
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
    overflow: hidden;
  }

  .testimonial-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-loose);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .testimonial-card__rating {
    display: flex;
    margin-bottom: var(--spacing-small);
  }

  .testimonial-card__star {
    color: #d4d4d4;
    margin-right: 2px;
  }

  .testimonial-card__star svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
  }

  .testimonial-card__star--filled {
    color: #FFD700; /* Yellow color for stars */
  }

  .testimonial-card__title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-small);
  }

  .testimonial-card__content {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: var(--spacing-base);
    flex-grow: 1;
  }

  .testimonial-card__author {
    display: flex;
    align-items: center;
    margin-top: auto;
  }

  .testimonial-card__author-image-wrapper {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: var(--spacing-small);
    flex-shrink: 0;
  }

  .testimonial-card__author-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .testimonial-card__author-name {
    font-weight: 500;
    font-size: 16px;
  }

  .testimonial-card__author-title {
    font-size: 14px;
    color: var(--color-secondary);
  }

  .testimonial-carousel__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
  }

  .testimonial-carousel__arrow {
    background-color: white;
    border: 1px solid var(--color-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
  }

  .testimonial-carousel__arrow:hover {
    background-color: var(--color-background-light);
  }

  .testimonial-carousel__arrow svg {
    width: 12px;
    height: 12px;
  }

  .testimonial-carousel__dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: var(--spacing-base);
  }

  .testimonial-carousel__dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--color-border);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .testimonial-carousel__dot--active {
    background-color: var(--color-primary);
  }

  @media screen and (min-width: 750px) and (max-width: 989px) {
    .testimonial-carousel__wrapper {
      padding: 0 30px;
    }

    .testimonial-card__title {
      font-size: 17px;
    }

    .testimonial-card__content {
      font-size: 15px;
    }

    .testimonial-card {
      padding: var(--spacing-base);
    }
  }

  @media screen and (max-width: 749px) {
    .testimonial-carousel__wrapper {
      padding: 0 25px;
    }

    .testimonial-carousel__arrow {
      width: 30px;
      height: 30px;
    }

    .testimonial-card__title {
      font-size: 16px;
    }

    .testimonial-card__content {
      font-size: 14px;
    }

    .testimonial-card__author-image-wrapper {
      width: 40px;
      height: 40px;
    }

    .testimonial-card__author-name {
      font-size: 14px;
    }

    .testimonial-card__author-title {
      font-size: 12px;
    }
  }

  @media screen and (max-width: 480px) {
    .testimonial-carousel__wrapper {
      padding: 0 20px;
    }

    .testimonial-carousel__arrow {
      width: 25px;
      height: 25px;
    }

    .testimonial-carousel__arrow svg {
      width: 10px;
      height: 10px;
    }

    .testimonial-card {
      padding: 15px;
    }

    .testimonial-card__star svg {
      width: 16px;
      height: 16px;
    }

    .testimonial-card__title {
      font-size: 15px;
      margin-bottom: 8px;
    }

    .testimonial-card__content {
      font-size: 13px;
      margin-bottom: 12px;
    }
  }
</style>

<script>
  // Testimonial carousel will be initialized by carousel-init.js
  // All configuration is passed via data attributes on the carousel element

  // Add a direct initialization script as a fallback
  document.addEventListener('DOMContentLoaded', function() {
    // Wait for keen-slider.js to load
    setTimeout(function() {
      if (typeof KeenSlider === 'function') {
        const container = document.querySelector('[data-section-id="{{ section.id }}"]');
        if (container && !container.keenSlider) {
          // Get settings from data attributes
          const slidesPerView = parseInt(container.getAttribute('data-slides-per-view') || 3, 10);
          const slidesPerViewTablet = parseInt(container.getAttribute('data-slides-per-view-tablet') || 2, 10);
          const slidesPerViewMobile = parseInt(container.getAttribute('data-slides-per-view-mobile') || 1, 10);

          // Set slide widths directly
          const slides = container.querySelectorAll('.keen-slider__slide');
          const windowWidth = window.innerWidth;
          let currentSlidesPerView = slidesPerView;

          if (windowWidth < 750) {
            currentSlidesPerView = slidesPerViewMobile;
          } else if (windowWidth < 990) {
            currentSlidesPerView = slidesPerViewTablet;
          }

          const slideWidth = 100 / currentSlidesPerView;
          slides.forEach(slide => {
            slide.style.cssText = `min-width: ${slideWidth}% !important; max-width: ${slideWidth}% !important; width: ${slideWidth}% !important;`;
          });

          // Initialize carousel if not already initialized
          if (typeof window.initCarousels === 'function') {
            window.initCarousels('{{ section.id }}');
          }
        }
      }
    }, 500); // Wait for other scripts to load
  });
</script>

{% schema %}
{
  "name": "Testimonial Carousel",
  "settings": [
    {
      "type": "header",
      "content": "Heading Style"
    },
    {
      "type": "text",
      "id": "heading_first_word",
      "label": "Heading First Word (Outlined)",
      "default": "Customer"
    },
    {
      "type": "text",
      "id": "heading_second_word",
      "label": "Heading Second Word (Filled)",
      "default": "Testimonials"
    },
    {
      "type": "color",
      "id": "heading_outline_color",
      "label": "Outline Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "heading_filled_color",
      "label": "Filled Text Color",
      "default": "#000000"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Alternative Heading (Legacy)",
      "default": "Customer Testimonials",
      "info": "Used if the outlined/filled heading words are not provided"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "range",
      "id": "testimonials_per_row",
      "min": 1,
      "max": 4,
      "step": 1,
      "label": "Testimonials per row (desktop)",
      "default": 3
    },
    {
      "type": "range",
      "id": "testimonials_per_row_tablet",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Testimonials per row (tablet)",
      "default": 2
    },
    {
      "type": "range",
      "id": "testimonials_per_row_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Testimonials per row (mobile)",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "label": "Show dots",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 3000,
      "max": 8000,
      "step": 500,
      "unit": "ms",
      "label": "Autoplay speed",
      "default": 5000
    }
  ],
  "blocks": [
    {
      "type": "testimonial",
      "name": "Testimonial",
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "min": 1,
          "max": 5,
          "step": 1,
          "label": "Rating",
          "default": 5
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Great Product"
        },
        {
          "type": "richtext",
          "id": "testimonial",
          "label": "Testimonial",
          "default": "<p>Share what your customers are saying about your products, your company...</p>"
        },
        {
          "type": "text",
          "id": "author_name",
          "label": "Author name",
          "default": "John Doe"
        },
        {
          "type": "text",
          "id": "author_title",
          "label": "Author title",
          "default": "Verified Customer"
        },
        {
          "type": "image_picker",
          "id": "author_image",
          "label": "Author image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Testimonial Carousel",
      "category": "Testimonial",
      "settings": {
        "heading_first_word": "Customer",
        "heading_second_word": "Testimonials",
        "heading_outline_color": "#000000",
        "heading_filled_color": "#000000"
      },
      "blocks": [
        {
          "type": "testimonial"
        },
        {
          "type": "testimonial"
        },
        {
          "type": "testimonial"
        }
      ]
    }
  ]
}
{% endschema %}
