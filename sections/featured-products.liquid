<div class="featured-products" data-section-id="{{ section.id }}" data-section-type="featured-products">
  <div class="page-width">
    {% render 'section-heading',
      first_word: section.settings.heading_first_word,
      second_word: section.settings.heading_second_word,
      outline_color: section.settings.heading_outline_color,
      filled_color: section.settings.heading_filled_color
    %}

    {% if section.settings.description != blank %}
      <div class="featured-products__description rte text-center">
        {{ section.settings.description }}
      </div>
    {% endif %}

    <div class="featured-products__slider" id="featured-products-slider-{{ section.id }}">
      <div class="keen-slider">
        {% for product in section.settings.product_list %}
          <div class="keen-slider__slide">
            <div class="product-card">
              <a href="{{ product.url }}" class="product-card__link">
                <div class="product-card__image-wrapper">
                  {% if product.featured_media %}
                    {% assign img_url = product.featured_media | img_url: '400x400', crop: 'center' %}
                    {% assign img_url_2x = product.featured_media | img_url: '800x800', crop: 'center' %}
                    <img
                      src="{{ img_url }}"
                      srcset="{{ img_url }} 1x, {{ img_url_2x }} 2x"
                      alt="{{ product.featured_media.alt | escape }}"
                      loading="lazy"
                      width="400"
                      height="400"
                      class="product-card__image"
                    >
                  {% else %}
                    {{ 'product-1' | placeholder_svg_tag: 'product-card__image placeholder-svg' }}
                  {% endif %}

                  {% if product.compare_at_price > product.price %}
                    <span class="product-card__badge">{{ 'products.product.on_sale' | t }}</span>
                  {% endif %}
                </div>

                <div class="product-card__info">
                  <h3 class="product-card__title">{{ product.title }}</h3>

                  <div class="product-card__price">
                    {% if product.compare_at_price > product.price %}
                      <span class="product-card__price--sale">{{ product.price | money }}</span>
                      <span class="product-card__price--compare">{{ product.compare_at_price | money }}</span>
                    {% else %}
                      <span class="product-card__price--regular">{{ product.price | money }}</span>
                    {% endif %}
                  </div>
                </div>
              </a>
            </div>
          </div>
        {% endfor %}
      </div>

      <div class="featured-products__controls">
        <button type="button" class="featured-products__arrow featured-products__arrow--prev" aria-label="{{ 'general.slider.previous' | t }}">
          {% render 'icon-chevron-left' %}
        </button>
        <button type="button" class="featured-products__arrow featured-products__arrow--next" aria-label="{{ 'general.slider.next' | t }}">
          {% render 'icon-chevron-right' %}
        </button>
      </div>

      {% if section.settings.show_dots %}
        <div class="featured-products__dots" data-carousel-dots></div>
      {% endif %}
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const sliderContainer = document.getElementById('featured-products-slider-{{ section.id }}');
    if (!sliderContainer) return;

    const sliderElement = sliderContainer.querySelector('.keen-slider');
    const prevButton = sliderContainer.querySelector('.featured-products__arrow--prev');
    const nextButton = sliderContainer.querySelector('.featured-products__arrow--next');
    const dotsElement = sliderContainer.querySelector('[data-carousel-dots]');
    const slides = sliderElement.querySelectorAll('.keen-slider__slide');

    // Get settings from section settings
    const desktopSlidesPerView = {{ section.settings.products_per_view }};
    const tabletSlidesPerView = {{ section.settings.products_per_view_tablet | default: 2 }};
    const mobileSlidesPerView = {{ section.settings.products_per_view_mobile | default: 1 }};
    const autoplay = {{ section.settings.autoplay | default: false }};
    const autoplaySpeed = {{ section.settings.autoplay_speed | default: 5000 }};

    // Helper function for autoplay
    let autoplayTimer = null;
    function setupAutoplay(instance, speed) {
      if (!autoplay) return;

      const startAutoplay = () => {
        stopAutoplay();
        autoplayTimer = setInterval(() => {
          instance.next();
        }, speed);
      };

      const stopAutoplay = () => {
        if (autoplayTimer) {
          clearInterval(autoplayTimer);
          autoplayTimer = null;
        }
      };

      // Start autoplay
      startAutoplay();

      // Pause on hover
      sliderElement.addEventListener('mouseenter', stopAutoplay);
      sliderElement.addEventListener('mouseleave', startAutoplay);

      // Cleanup on destroy
      instance.on('destroyed', stopAutoplay);
    }

    // Helper function to setup dots
    function setupDots(instance, dotsElement) {
      if (!dotsElement) return;

      // Clear existing dots
      dotsElement.innerHTML = '';

      // Create dots
      const slides = instance.track.details.slides;
      const slidesPerView = getSlidesPerView();
      const totalDots = Math.ceil(slides.length / slidesPerView);

      for (let i = 0; i < totalDots; i++) {
        const dot = document.createElement('button');
        dot.classList.add('dot');
        dot.setAttribute('aria-label', `Go to slide group ${i + 1}`);
        dot.addEventListener('click', () => instance.moveToIdx(i * slidesPerView));
        dotsElement.appendChild(dot);
      }

      // Update active dot
      const updateActiveDot = () => {
        const { slides, rel } = instance.track.details;
        const dots = dotsElement.querySelectorAll('.dot');
        const slidesPerView = getSlidesPerView();
        const activeDotIndex = Math.floor(rel / slidesPerView);

        dots.forEach((dot, idx) => {
          dot.classList.toggle('dot--active', idx === activeDotIndex);
        });
      };

      instance.on('slideChanged', updateActiveDot);
      updateActiveDot();
    }

    // Get current slides per view based on viewport width
    function getSlidesPerView() {
      const viewportWidth = window.innerWidth;
      if (viewportWidth <= 749) {
        return mobileSlidesPerView;
      } else if (viewportWidth <= 989) {
        return tabletSlidesPerView;
      } else {
        return desktopSlidesPerView;
      }
    }

    // Initialize the slider with a simpler configuration
    const keenSlider = new KeenSlider(sliderElement, {
      loop: true,
      mode: 'snap',
      rtl: false,
      slides: {
        origin: 'center',
        perView: getSlidesPerView(),
        spacing: window.innerWidth <= 749 ? 10 : 20
      },
      created: function(instance) {
        // Setup dots if enabled
        if (dotsElement) {
          setupDots(instance, dotsElement);
        }

        // Setup autoplay if enabled
        if (autoplay) {
          setupAutoplay(instance, autoplaySpeed);
        }

        // Force refresh after a short delay to handle any layout issues
        setTimeout(() => {
          instance.refresh();
        }, 50);
      }
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      keenSlider.destroy();

      // Reinitialize with new settings
      const newSlider = new KeenSlider(sliderElement, {
        loop: true,
        mode: 'snap',
        rtl: false,
        slides: {
          origin: 'center',
          perView: getSlidesPerView(),
          spacing: window.innerWidth <= 749 ? 10 : 20
        },
        created: function(instance) {
          // Setup dots if enabled
          if (dotsElement) {
            setupDots(instance, dotsElement);
          }

          // Setup autoplay if enabled
          if (autoplay) {
            setupAutoplay(instance, autoplaySpeed);
          }

          // Force refresh after a short delay to handle any layout issues
          setTimeout(() => {
            instance.refresh();
          }, 50);
        }
      });

      // Update event listeners
      prevButton.addEventListener('click', () => newSlider.prev());
      nextButton.addEventListener('click', () => newSlider.next());
    });

    // Add click event listeners
    prevButton.addEventListener('click', () => keenSlider.prev());
    nextButton.addEventListener('click', () => keenSlider.next());

    // Force a refresh on page load
    window.addEventListener('load', () => {
      // Destroy and recreate slider to ensure proper initialization
      keenSlider.destroy();

      const loadedSlider = new KeenSlider(sliderElement, {
        loop: true,
        mode: 'snap',
        rtl: false,
        slides: {
          origin: 'center',
          perView: getSlidesPerView(),
          spacing: window.innerWidth <= 749 ? 10 : 20
        },
        created: function(instance) {
          // Setup dots if enabled
          if (dotsElement) {
            setupDots(instance, dotsElement);
          }

          // Setup autoplay if enabled
          if (autoplay) {
            setupAutoplay(instance, autoplaySpeed);
          }
        }
      });

      // Update event listeners
      prevButton.addEventListener('click', () => loadedSlider.prev());
      nextButton.addEventListener('click', () => loadedSlider.next());
    });
  });
</script>

<style>
  .featured-products {
    margin: 60px 0;
  }

  .featured-products__description {
    max-width: 800px;
    margin: 20px auto 30px;
  }

  .featured-products__slider {
    position: relative;
    margin-top: 30px;
  }

  .product-card {
    height: 100%;
    width: 100%;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
    overflow: hidden;
  }

  .product-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .product-card__image-wrapper {
    position: relative;
    overflow: hidden;
    padding-top: 100%;
    background-color: #f8f8f8;
    width: 100%;
  }

  .product-card__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
  }

  .product-card:hover .product-card__image {
    transform: scale(1.05);
  }

  .product-card__badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--color-primary);
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: var(--border-radius);
  }

  .product-card__info {
    padding: 15px;
  }

  .product-card__title {
    font-size: 16px;
    margin-bottom: 5px;
    font-weight: 500;
  }

  .product-card__price {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
  }

  .product-card__price--sale {
    color: var(--color-sale);
    font-weight: 700;
  }

  .product-card__price--compare {
    text-decoration: line-through;
    opacity: 0.6;
    font-size: 14px;
  }

  .product-card__price--regular {
    font-weight: 700;
  }

  .featured-products__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 10;
  }

  .featured-products__arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    border: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    pointer-events: auto;
    transition: background-color 0.3s ease, transform 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .featured-products__arrow:hover {
    background-color: var(--color-primary);
    transform: scale(1.1);
  }

  .featured-products__arrow:hover svg {
    fill: white;
  }

  .featured-products__arrow--prev {
    margin-left: -20px;
  }

  .featured-products__arrow--next {
    margin-right: -20px;
  }

  .featured-products__dots {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 8px;
  }

  .featured-products__dots .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: none;
    background-color: #ddd;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
  }

  .featured-products__dots .dot--active {
    background-color: var(--color-primary, #000);
    transform: scale(1.2);
  }

  .featured-products__dots .dot:hover {
    background-color: #aaa;
  }

  @media screen and (max-width: 749px) {
    .featured-products {
      margin: 40px 0;
    }

    .featured-products__arrow {
      width: 36px;
      height: 36px;
    }

    .featured-products__arrow--prev {
      margin-left: -10px;
    }

    .featured-products__arrow--next {
      margin-right: -10px;
    }
  }

  /* Fix for mobile carousel */
  .keen-slider {
    display: flex;
    overflow: hidden;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -khtml-user-select: none;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
    width: 100%;
  }

  .keen-slider__slide {
    position: relative;
    overflow: hidden;
    min-height: 100%;
    width: 100%;
  }

  @media screen and (max-width: 749px) {
    .keen-slider__slide {
      width: 100% !important;
    }

    .product-card {
      width: 100% !important;
      margin: 0 auto;
    }

    .product-card__image-wrapper {
      width: 100% !important;
    }
  }
</style>

{% schema %}
{
  "name": "Featured Products",
  "settings": [
    {
      "type": "text",
      "id": "heading_first_word",
      "label": "Heading First Word (Outlined)",
      "default": "Featured"
    },
    {
      "type": "text",
      "id": "heading_second_word",
      "label": "Heading Second Word (Filled)",
      "default": "Products"
    },
    {
      "type": "color",
      "id": "heading_outline_color",
      "label": "Outline Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "heading_filled_color",
      "label": "Filled Text Color",
      "default": "#000000"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "product_list",
      "id": "product_list",
      "label": "Products",
      "limit": 12
    },
    {
      "type": "header",
      "content": "Carousel Settings"
    },
    {
      "type": "range",
      "id": "products_per_view",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "Products per row (desktop)"
    },
    {
      "type": "range",
      "id": "products_per_view_tablet",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 2,
      "label": "Products per row (tablet)"
    },
    {
      "type": "range",
      "id": "products_per_view_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 1,
      "label": "Products per row (mobile)"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable autoplay",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 2000,
      "max": 8000,
      "step": 500,
      "unit": "ms",
      "label": "Autoplay speed",
      "default": 5000
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "label": "Show dots",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "Featured Products",
      "category": "Collection",
      "settings": {
        "heading_first_word": "Featured",
        "heading_second_word": "Products",
        "heading_outline_color": "#000000",
        "heading_filled_color": "#000000",
        "products_per_view": 4,
        "products_per_view_tablet": 2,
        "products_per_view_mobile": 1,
        "autoplay": true,
        "show_dots": false
      }
    }
  ]
}
{% endschema %}
