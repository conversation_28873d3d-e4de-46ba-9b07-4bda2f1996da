{{ 'category-grid.css' | asset_url | stylesheet_tag }}

<div class="category-grid-section full-width"
     style="background-color: {{ section.settings.background_color }};
            padding-top: {{ section.settings.padding_top }}px;
            padding-bottom: {{ section.settings.padding_bottom }}px;">
  <div class="page-width">
    {% if section.settings.title != blank %}
      <div class="section-header text-center">
        <h2 class="section-title">{{ section.settings.title }}</h2>
        {% if section.settings.description != blank %}
          <div class="section-description">{{ section.settings.description }}</div>
        {% endif %}
      </div>
    {% endif %}

    {% comment %}Debug info - only visible in theme editor{% endcomment %}
    {% if section.blocks.size == 0 and request.design_mode %}
      <div class="category-grid-debug">
        <p>⚠️ No category blocks have been added. Add collection blocks in the theme editor.</p>
      </div>
    {% endif %}

    <div class="category-grid category-grid--{{ section.settings.columns_desktop }}-col-desktop category-grid--{{ section.settings.columns_mobile }}-col-mobile{% if section.settings.show_title_overlay %} category-grid--title-overlay{% endif %}" style="gap: {{ section.settings.gap }}px;">
      {% if section.blocks.size > 0 %}
        {% for block in section.blocks %}
          {% if block.settings.collection != blank %}
            {% assign collection = collections[block.settings.collection] %}
            <div class="category-item" {{ block.shopify_attributes }}>
              <a href="{{ collection.url }}" class="category-item__link">
                <div class="category-item__image-container">
                  {% if block.settings.use_collection_image and collection.image != blank %}
                    <img
                      srcset="{{ collection.image | img_url: '400x400', crop: 'center' }} 1x, {{ collection.image | img_url: '800x800', crop: 'center' }} 2x"
                      src="{{ collection.image | img_url: '400x400', crop: 'center' }}"
                      alt="{{ collection.title | escape }}"
                      width="400"
                      height="400"
                      class="category-item__image"
                      loading="lazy"
                      decoding="async"
                    >
                  {% elsif block.settings.custom_image != blank %}
                    <img
                      srcset="{{ block.settings.custom_image | img_url: '400x400', crop: 'center' }} 1x, {{ block.settings.custom_image | img_url: '800x800', crop: 'center' }} 2x"
                      src="{{ block.settings.custom_image | img_url: '400x400', crop: 'center' }}"
                      alt="{{ collection.title | escape }}"
                      width="400"
                      height="400"
                      class="category-item__image"
                      loading="lazy"
                      decoding="async"
                    >
                  {% elsif collection.products.first.featured_image != blank %}
                    <img
                      srcset="{{ collection.products.first.featured_image | img_url: '400x400', crop: 'center' }} 1x, {{ collection.products.first.featured_image | img_url: '800x800', crop: 'center' }} 2x"
                      src="{{ collection.products.first.featured_image | img_url: '400x400', crop: 'center' }}"
                      alt="{{ collection.title | escape }}"
                      width="400"
                      height="400"
                      class="category-item__image"
                      loading="lazy"
                      decoding="async"
                    >
                  {% else %}
                    {{ 'collection-1' | placeholder_svg_tag: 'category-item__image placeholder-svg' }}
                    {% if request.design_mode %}
                      <div class="category-item__image-debug">
                        <p>No image available</p>
                      </div>
                    {% endif %}
                  {% endif %}
                </div>
                <div class="category-item__title">
                  {% if block.settings.custom_title != blank %}
                    {{ block.settings.custom_title }}
                  {% else %}
                    {{ collection.title }}
                  {% endif %}
                </div>
              </a>
            </div>
          {% else %}
            <div class="category-item placeholder" {{ block.shopify_attributes }}>
              <div class="category-item__image-container">
                {{ 'collection-1' | placeholder_svg_tag: 'category-item__image placeholder-svg' }}
              </div>
              <div class="category-item__title">
                Select a collection
              </div>
            </div>
          {% endif %}
        {% endfor %}
      {% else %}
        {% comment %} Display placeholder items if no blocks are added {% endcomment %}
        {% for i in (1..4) %}
          <div class="category-item placeholder">
            <div class="category-item__image-container">
              {{ 'collection-' | append: forloop.index | placeholder_svg_tag: 'category-item__image placeholder-svg' }}
            </div>
            <div class="category-item__title">
              Example Category {{ forloop.index }}
            </div>
          </div>
        {% endfor %}
      {% endif %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Category Grid",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "CATEGORIES"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "columns_desktop",
      "label": "Number of columns on desktop",
      "options": [
        {
          "value": "2",
          "label": "2 columns"
        },
        {
          "value": "3",
          "label": "3 columns"
        },
        {
          "value": "4",
          "label": "4 columns"
        }
      ],
      "default": "4"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "label": "Number of columns on mobile",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ],
      "default": "2"
    },
    {
      "type": "range",
      "id": "gap",
      "min": 0,
      "max": 40,
      "step": 4,
      "unit": "px",
      "label": "Gap between items",
      "default": 16
    },
    {
      "type": "header",
      "content": "Style"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f9f9f9"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 40
    },
    {
      "type": "checkbox",
      "id": "show_title_overlay",
      "label": "Show title overlay on image",
      "default": false,
      "info": "If checked, titles will appear over the images instead of below them"
    }
  ],
  "max_blocks": 8,
  "blocks": [
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection",
          "info": "Select a collection to display in the grid"
        },
        {
          "type": "checkbox",
          "id": "use_collection_image",
          "label": "Use collection image",
          "default": true,
          "info": "If checked, will use the collection image. If unchecked or if collection has no image, will use custom image or first product image."
        },
        {
          "type": "image_picker",
          "id": "custom_image",
          "label": "Custom image",
          "info": "Optional. Used if collection image is not available or not selected."
        },
        {
          "type": "text",
          "id": "custom_title",
          "label": "Custom title",
          "info": "Optional. If left blank, will use collection title."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Category Grid",
      "category": "Collection",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
