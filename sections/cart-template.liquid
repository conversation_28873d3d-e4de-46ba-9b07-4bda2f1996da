<div class="cart-template" data-section-id="{{ section.id }}" data-section-type="cart">
  <div class="page-width">
    <div class="cart-template__header">
      <h1 class="cart-template__title">Your Cart</h1>
    </div>

    {% if cart.item_count > 0 %}
      <form action="{{ routes.cart_url }}" method="post" id="cart-form" novalidate>
        <div class="cart-template__items">
          <table class="cart-template__table">
            <thead class="cart-template__table-head">
              <tr>
                <th class="cart-template__table-heading cart-template__table-heading--product">Product</th>
                <th class="cart-template__table-heading cart-template__table-heading--price">Price</th>
                <th class="cart-template__table-heading cart-template__table-heading--quantity">Quantity</th>
                <th class="cart-template__table-heading cart-template__table-heading--total">Total</th>
              </tr>
            </thead>
            <tbody>
              {% for item in cart.items %}
                <tr class="cart-template__table-row" data-cart-item data-cart-item-key="{{ item.key }}">
                  <td class="cart-template__table-cell cart-template__table-cell--product">
                    <div class="cart-template__product">
                      <div class="cart-template__product-image">
                        {% if item.image %}
                          <a href="{{ item.url }}">
                            <img src="{{ item.image | img_url: '120x120', crop: 'center' }}"
                                 alt="{{ item.title | escape }}"
                                 loading="lazy"
                                 width="120"
                                 height="120">
                          </a>
                        {% else %}
                          {{ 'product-1' | placeholder_svg_tag: 'cart-template__product-image-placeholder' }}
                        {% endif %}
                      </div>
                      <div class="cart-template__product-details">
                        <a href="{{ item.url }}" class="cart-template__product-title">
                          {{ item.product.title }}
                        </a>

                        {% unless item.product.has_only_default_variant %}
                          <p class="cart-template__product-variant">
                            {{ item.variant.title }}
                          </p>
                        {% endunless %}

                        {% if item.properties.size > 0 %}
                          <div class="cart-template__product-properties">
                            {% for property in item.properties %}
                              {% unless property.last == blank %}
                                <div class="cart-template__product-property">
                                  <span class="cart-template__product-property-name">{{ property.first }}:</span>
                                  <span class="cart-template__product-property-value">
                                    {% if property.last contains '/uploads/' %}
                                      <a href="{{ property.last }}">{{ property.last | split: '/' | last }}</a>
                                    {% else %}
                                      {{ property.last }}
                                    {% endif %}
                                  </span>
                                </div>
                              {% endunless %}
                            {% endfor %}
                          </div>
                        {% endif %}

                        <button type="button"
                                class="cart-template__remove-button"
                                aria-label="{{ 'cart.remove_title' | t: title: item.title }}"
                                data-cart-remove="{{ item.key }}">
                          Remove
                        </button>
                      </div>
                    </div>
                  </td>
                  <td class="cart-template__table-cell cart-template__table-cell--price" data-label="Price">
                    <div class="cart-template__price">
                      {% if item.original_price != item.final_price %}
                        <span class="cart-template__price--regular">{{ item.original_price | money }}</span>
                        <span class="cart-template__price--sale">{{ item.final_price | money }}</span>
                      {% else %}
                        <span>{{ item.original_price | money }}</span>
                      {% endif %}

                      {% if item.line_level_discount_allocations.size > 0 %}
                        <ul class="cart-template__discounts">
                          {% for discount_allocation in item.line_level_discount_allocations %}
                            <li class="cart-template__discount">
                              {{ discount_allocation.discount_application.title }}: -{{ discount_allocation.amount | money }}
                            </li>
                          {% endfor %}
                        </ul>
                      {% endif %}
                    </div>
                  </td>
                  <td class="cart-template__table-cell cart-template__table-cell--quantity" data-label="Quantity">
                    <div class="cart-template__quantity">
                      <label for="Quantity-{{ item.key }}" class="visually-hidden">
                        Quantity
                      </label>
                      <div class="cart-template__quantity-input">
                        <button type="button"
                                class="cart-template__quantity-button cart-template__quantity-button--decrease"
                                data-cart-quantity-decrease
                                aria-label="{{ 'products.product.quantity.decrease' | t: product: item.product.title }}">
                          -
                        </button>
                        <input type="number"
                               id="Quantity-{{ item.key }}"
                               name="updates[{{ item.key }}]"
                               value="{{ item.quantity }}"
                               min="0"
                               aria-label="{{ 'products.product.quantity.input_label' | t: product: item.product.title }}"
                               class="cart-template__quantity-input-field"
                               data-cart-quantity-input>
                        <button type="button"
                                class="cart-template__quantity-button cart-template__quantity-button--increase"
                                data-cart-quantity-increase
                                aria-label="{{ 'products.product.quantity.increase' | t: product: item.product.title }}">
                          +
                        </button>
                      </div>
                    </div>
                  </td>
                  <td class="cart-template__table-cell cart-template__table-cell--total" data-label="Total">
                    <div class="cart-template__line-price">
                      {{ item.final_line_price | money }}
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <div class="cart-template__footer">
          <div class="cart-template__footer-left">
            {% if section.settings.show_note %}
              <div class="cart-template__note">
                <label for="CartNote" class="cart-template__note-label">Order special instructions</label>
                <textarea id="CartNote" name="note" class="cart-template__note-input">{{ cart.note }}</textarea>
              </div>
            {% endif %}
          </div>

          <div class="cart-template__footer-right">
            <div class="cart-template__subtotal">
              <span class="cart-template__subtotal-label">Subtotal</span>
              <span class="cart-template__subtotal-price">{{ cart.total_price | money }}</span>
            </div>

            {% if cart.cart_level_discount_applications.size > 0 %}
              <ul class="cart-template__discounts">
                {% for discount_application in cart.cart_level_discount_applications %}
                  <li class="cart-template__discount">
                    {{ discount_application.title }}: -{{ discount_application.total_allocated_amount | money }}
                  </li>
                {% endfor %}
              </ul>
            {% endif %}

            <div class="cart-template__taxes">
              {% if shop.taxes_included and shop.shipping_policy.body != blank %}
                {{ 'cart.taxes_included_and_shipping_policy_html' | t: link: shop.shipping_policy.url }}
              {% elsif shop.taxes_included %}
                {{ 'cart.taxes_included_but_shipping_at_checkout' | t }}
              {% elsif shop.shipping_policy.body != blank %}
                {{ 'cart.taxes_and_shipping_policy_at_checkout_html' | t: link: shop.shipping_policy.url }}
              {% else %}
                Taxes and shipping calculated at checkout
              {% endif %}
            </div>

            <div class="cart-template__buttons">
              <button type="submit" name="update" class="cart-template__update-button">
                Update cart
              </button>

              <button type="submit" name="checkout" class="cart-template__checkout-button">
                Check out
              </button>
            </div>
          </div>
        </div>
      </form>
    {% else %}
      <div class="cart-template__empty">
        <p class="cart-template__empty-text">Your cart is empty</p>
        <div class="cart-template__empty-button-wrapper">
          <a href="{{ routes.all_products_collection_url }}" class="cart-template__continue-button">
            Continue shopping
          </a>
        </div>
      </div>
    {% endif %}
  </div>
</div>

<style>
  .cart-template {
    padding: var(--spacing-large) 0;
  }

  .cart-template__header {
    margin-bottom: var(--spacing-large);
  }

  .cart-template__title {
    margin: 0;
    font-size: var(--font-size-heading-large);
  }

  .cart-template__table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-large);
  }

  .cart-template__table-head {
    border-bottom: 1px solid var(--color-border);
  }

  .cart-template__table-heading {
    padding: var(--spacing-small);
    text-align: left;
    font-weight: var(--font-weight-medium);
  }

  .cart-template__table-row {
    border-bottom: 1px solid var(--color-border-light);
  }

  .cart-template__table-cell {
    padding: var(--spacing-base);
    vertical-align: top;
  }

  .cart-template__product {
    display: flex;
    align-items: flex-start;
  }

  .cart-template__product-image {
    flex-shrink: 0;
    margin-right: var(--spacing-base);
    width: 120px;
    height: 120px;
  }

  .cart-template__product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .cart-template__product-title {
    display: block;
    margin-bottom: var(--spacing-extra-small);
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
    text-decoration: none;
  }

  .cart-template__product-variant {
    margin-bottom: var(--spacing-extra-small);
    font-size: var(--font-size-small);
    color: var(--color-text-light);
  }

  .cart-template__product-properties {
    margin-bottom: var(--spacing-small);
    font-size: var(--font-size-small);
  }

  .cart-template__product-property {
    margin-bottom: var(--spacing-extra-small);
  }

  .cart-template__remove-button {
    background: none;
    border: none;
    padding: 0;
    font-size: var(--font-size-small);
    color: var(--color-text-light);
    text-decoration: underline;
    cursor: pointer;
  }

  .cart-template__price--regular {
    text-decoration: line-through;
    color: var(--color-text-light);
    margin-right: var(--spacing-extra-small);
  }

  .cart-template__price--sale {
    color: var(--color-error);
  }

  .cart-template__discounts {
    list-style: none;
    padding: 0;
    margin: var(--spacing-extra-small) 0 0;
    font-size: var(--font-size-small);
    color: var(--color-error);
  }

  .cart-template__quantity {
    max-width: 120px;
  }

  .cart-template__quantity-input {
    display: flex;
    align-items: center;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    overflow: hidden;
  }

  .cart-template__quantity-button {
    background: var(--color-background-light);
    border: none;
    padding: var(--spacing-small);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .cart-template__quantity-input-field {
    width: 40px;
    border: none;
    text-align: center;
    padding: var(--spacing-small) 0;
    -moz-appearance: textfield;
  }

  .cart-template__quantity-input-field::-webkit-outer-spin-button,
  .cart-template__quantity-input-field::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .cart-template__footer {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: var(--spacing-large);
  }

  .cart-template__footer-left {
    width: 100%;
    margin-bottom: var(--spacing-large);
  }

  .cart-template__footer-right {
    width: 100%;
  }

  .cart-template__note-label {
    display: block;
    margin-bottom: var(--spacing-small);
    font-weight: var(--font-weight-medium);
  }

  .cart-template__note-input {
    width: 100%;
    min-height: 100px;
    padding: var(--spacing-small);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
  }

  .cart-template__subtotal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-small);
    font-weight: var(--font-weight-bold);
  }

  .cart-template__taxes {
    margin-bottom: var(--spacing-base);
    font-size: var(--font-size-small);
    color: var(--color-text-light);
  }

  .cart-template__buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-small);
  }

  .cart-template__update-button {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-background-light);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .cart-template__update-button:hover {
    background-color: var(--color-border-light);
  }

  .cart-template__checkout-button {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .cart-template__checkout-button:hover {
    background-color: var(--color-secondary);
  }

  .cart-template__empty {
    text-align: center;
    padding: var(--spacing-extra-large) 0;
  }

  .cart-template__empty-text {
    font-size: var(--font-size-large);
    margin-bottom: var(--spacing-large);
  }

  .cart-template__continue-button {
    display: inline-block;
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .cart-template__continue-button:hover {
    background-color: var(--color-secondary);
  }

  @media screen and (min-width: 750px) {
    .cart-template__footer-left {
      width: 40%;
      margin-bottom: 0;
    }

    .cart-template__footer-right {
      width: 50%;
    }

    .cart-template__buttons {
      flex-direction: row;
      justify-content: flex-end;
    }
  }

  @media screen and (max-width: 749px) {
    .cart-template__table {
      display: block;
    }

    .cart-template__table-head {
      display: none;
    }

    .cart-template__table-row {
      display: block;
      padding: var(--spacing-base) 0;
    }

    .cart-template__table-cell {
      display: block;
      padding: var(--spacing-small) 0;
      width: 100%;
    }

    .cart-template__table-cell--price,
    .cart-template__table-cell--quantity,
    .cart-template__table-cell--total {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .cart-template__table-cell--price::before,
    .cart-template__table-cell--quantity::before,
    .cart-template__table-cell--total::before {
      content: attr(data-label);
      font-weight: var(--font-weight-medium);
    }

    .cart-template__table-cell--price::before {
      content: attr(data-label);
    }

    .cart-template__table-cell--quantity::before {
      content: attr(data-label);
    }

    .cart-template__table-cell--total::before {
      content: attr(data-label);
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const cartForm = document.getElementById('cart-form');
    if (!cartForm) return;

    // Quantity buttons
    const decreaseButtons = document.querySelectorAll('[data-cart-quantity-decrease]');
    const increaseButtons = document.querySelectorAll('[data-cart-quantity-increase]');
    const quantityInputs = document.querySelectorAll('[data-cart-quantity-input]');
    const removeButtons = document.querySelectorAll('[data-cart-remove]');

    // Handle quantity decrease
    decreaseButtons.forEach(button => {
      button.addEventListener('click', function() {
        const input = this.closest('.cart-template__quantity-input').querySelector('[data-cart-quantity-input]');
        let value = parseInt(input.value);
        if (value > 0) {
          input.value = value - 1;
          input.dispatchEvent(new Event('change'));
        }
      });
    });

    // Handle quantity increase
    increaseButtons.forEach(button => {
      button.addEventListener('click', function() {
        const input = this.closest('.cart-template__quantity-input').querySelector('[data-cart-quantity-input]');
        let value = parseInt(input.value);
        input.value = value + 1;
        input.dispatchEvent(new Event('change'));
      });
    });

    // Handle quantity change
    quantityInputs.forEach(input => {
      input.addEventListener('change', function() {
        // If we're in the Shopify Theme Editor, don't submit
        if (window.Shopify && window.Shopify.designMode) {
          console.log('Cart update prevented in Theme Editor');
          return;
        }

        // Auto-submit the form when quantity changes
        if (this.value === '0') {
          // If quantity is 0, find the remove button and trigger it
          const itemKey = this.closest('[data-cart-item]').dataset.cartItemKey;
          const removeButton = document.querySelector(`[data-cart-remove="${itemKey}"]`);
          if (removeButton) {
            removeButton.click();
          } else {
            cartForm.submit();
          }
        } else {
          // Debounce the form submission
          clearTimeout(this.timeout);
          this.timeout = setTimeout(() => {
            cartForm.submit();
          }, 500);
        }
      });
    });

    // Handle item removal
    removeButtons.forEach(button => {
      button.addEventListener('click', function() {
        const itemKey = this.dataset.cartRemove;
        const input = document.querySelector(`[data-cart-item-key="${itemKey}"] [data-cart-quantity-input]`);

        if (input) {
          input.value = 0;

          // If we're in the Shopify Theme Editor, don't submit
          if (window.Shopify && window.Shopify.designMode) {
            console.log('Cart removal prevented in Theme Editor');
            return;
          }

          cartForm.submit();
        }
      });
    });
  });
</script>

{% schema %}
{
  "name": "Cart page",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_note",
      "label": "Enable order notes",
      "default": true
    }
  ]
}
{% endschema %}
