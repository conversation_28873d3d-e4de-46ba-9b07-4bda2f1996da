<div class="newsletter" data-section-id="{{ section.id }}" data-section-type="newsletter" style="background-color: {{ section.settings.background_color }};">
  <div class="page-width">
    <div class="newsletter__wrapper">
      <div class="newsletter__content">
        {% if section.settings.heading != blank %}
          <h2 class="newsletter__heading" style="color: {{ section.settings.text_color }};">
            {{ section.settings.heading | escape }}
          </h2>
        {% endif %}

        {% if section.settings.subheading != blank %}
          <div class="newsletter__subheading rte" style="color: {{ section.settings.text_color }};">
            {{ section.settings.subheading }}
          </div>
        {% endif %}

        <form method="post" action="/contact#newsletter-form-{{ section.id }}" id="newsletter-form-{{ section.id }}" accept-charset="UTF-8" class="newsletter__form-wrapper">
          <input type="hidden" name="form_type" value="customer">
          <input type="hidden" name="utf8" value="✓">
          <input type="hidden" name="contact[tags]" value="newsletter">

          <div class="newsletter__form">
            <div class="newsletter__field">
              <input
                id="NewsletterEmail-{{ section.id }}"
                type="email"
                name="contact[email]"
                class="newsletter__input"
                value="{% if customer %}{{ customer.email }}{% endif %}"
                placeholder="{{ 'general.newsletter_form.email_placeholder' | t }}"
                required
                aria-required="true"
              >
              <label class="newsletter__label" for="NewsletterEmail-{{ section.id }}">
                {{ 'general.newsletter_form.email_placeholder' | t }}
              </label>
            </div>

            <button type="submit" class="newsletter__button button" name="commit">
              {{ 'general.newsletter_form.submit' | t }}
            </button>
          </div>

          <div class="newsletter__response">
            {%- if form.posted_successfully? -%}
              <p class="newsletter__success form-message form-message--success">
                {{ 'general.newsletter_form.confirmation' | t }}
              </p>
            {%- endif -%}
            {%- if form.errors -%}
              <div class="newsletter__error form-message form-message--error">
                {{ form.errors | default_errors }}
              </div>
            {%- endif -%}
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<style>
  .newsletter {
    margin: var(--spacing-extra-loose) 0;
    padding: var(--spacing-extra-loose) 0;
  }

  .newsletter__wrapper {
    display: flex;
    justify-content: center;
  }

  .newsletter__content {
    max-width: 600px;
    text-align: center;
  }

  .newsletter__heading {
    margin-bottom: var(--spacing-base);
    font-size: 28px;
  }

  .newsletter__subheading {
    margin-bottom: var(--spacing-loose);
  }

  .newsletter__form {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-tight);
  }

  .newsletter__field {
    position: relative;
    flex: 1 1 auto;
    min-width: 200px;
  }

  .newsletter__input {
    width: 100%;
    padding: var(--spacing-tight) var(--spacing-base);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    font-size: 16px;
    line-height: 1.5;
  }

  .newsletter__label {
    position: absolute;
    top: 0;
    left: var(--spacing-base);
    transform: translateY(-50%);
    background-color: var(--color-background);
    padding: 0 var(--spacing-tight);
    font-size: 12px;
    color: var(--color-secondary);
    pointer-events: none;
  }

  .newsletter__button {
    flex: 0 0 auto;
  }

  .form-message {
    margin-top: var(--spacing-base);
    padding: var(--spacing-tight) var(--spacing-base);
    border-radius: var(--border-radius);
  }

  .form-message--error {
    background-color: rgba(227, 44, 43, 0.1);
    color: #e32c2b;
  }

  .form-message--success {
    background-color: rgba(56, 180, 74, 0.1);
    color: #38b44a;
  }

  @media screen and (max-width: 749px) {
    .newsletter {
      padding: var(--spacing-loose) 0;
    }

    .newsletter__form {
      flex-direction: column;
    }

    .newsletter__heading {
      font-size: 24px;
    }
  }
</style>

{% schema %}
{
  "name": "Newsletter",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Subscribe to our newsletter"
    },
    {
      "type": "richtext",
      "id": "subheading",
      "label": "Subheading",
      "default": "<p>Sign up for exclusive offers, original stories, events and more.</p>"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f7f7f7"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#333333"
    }
  ],
  "presets": [
    {
      "name": "Newsletter",
      "category": "Promotional"
    }
  ]
}
{% endschema %}
