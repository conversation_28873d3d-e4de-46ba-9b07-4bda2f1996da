<div class="full-width-video" data-section-id="{{ section.id }}" data-section-type="full-width-video">
  <div class="full-width-video__wrapper">
    {% if section.settings.title != blank or section.settings.description != blank %}
      <div class="full-width-video__content-wrapper page-width">
        <div class="full-width-video__content">
          {% if section.settings.title != blank %}
            <h2 class="full-width-video__title">{{ section.settings.title | escape }}</h2>
          {% endif %}

          {% if section.settings.description != blank %}
            <div class="full-width-video__description rte">{{ section.settings.description }}</div>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <div class="full-width-video__container" data-video-container>
      {% if section.settings.video_source == 'url' and section.settings.video_url != blank %}
        {% if section.settings.video_url.type == 'youtube' %}
          <div class="full-width-video__embed" data-youtube-video data-video-id="{{ section.settings.video_url.id }}">
            <div id="youtube-player-{{ section.id }}" class="full-width-video__player"></div>
            {% if section.settings.cover_image != blank %}
              <div class="full-width-video__cover" data-video-cover>
                <img
                  src="{{ section.settings.cover_image | img_url: '1500x' }}"
                  srcset="{{ section.settings.cover_image | img_url: '1500x' }} 1x, {{ section.settings.cover_image | img_url: '3000x' }} 2x"
                  alt="{{ section.settings.cover_image.alt | escape }}"
                  loading="lazy"
                  width="{{ section.settings.cover_image.width }}"
                  height="{{ section.settings.cover_image.height }}"
                  class="full-width-video__cover-image"
                >
                <button type="button" class="full-width-video__play-button" data-video-play-button aria-label="Play video">
                  <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
              </div>
            {% endif %}
          </div>
        {% elsif section.settings.video_url.type == 'vimeo' %}
          <div class="full-width-video__embed" data-vimeo-video data-video-id="{{ section.settings.video_url.id }}">
            <div id="vimeo-player-{{ section.id }}" class="full-width-video__player"></div>
            {% if section.settings.cover_image != blank %}
              <div class="full-width-video__cover" data-video-cover>
                <img
                  src="{{ section.settings.cover_image | img_url: '1500x' }}"
                  srcset="{{ section.settings.cover_image | img_url: '1500x' }} 1x, {{ section.settings.cover_image | img_url: '3000x' }} 2x"
                  alt="{{ section.settings.cover_image.alt | escape }}"
                  loading="lazy"
                  width="{{ section.settings.cover_image.width }}"
                  height="{{ section.settings.cover_image.height }}"
                  class="full-width-video__cover-image"
                >
                <button type="button" class="full-width-video__play-button" data-video-play-button aria-label="Play video">
                  <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </button>
              </div>
            {% endif %}
          </div>
        {% endif %}
      {% elsif section.settings.video_source == 'upload' and section.settings.video_file != blank %}
        <div class="full-width-video__embed" data-uploaded-video>
          <video
            id="uploaded-video-{{ section.id }}"
            class="full-width-video__uploaded-video"
            controls
            preload="auto"
            playsinline
            {% if section.settings.cover_image != blank %}poster="{{ section.settings.cover_image | img_url: '1500x' }}"{% endif %}
          >
            {% for source in section.settings.video_file.sources %}
              <source src="{{ source.url }}" type="{{ source.mime_type }}">
            {% endfor %}
            Your browser does not support the video tag.
          </video>

          {% if section.settings.cover_image != blank %}
            <div class="full-width-video__cover" data-video-cover>
              <img
                src="{{ section.settings.cover_image | img_url: '1500x' }}"
                srcset="{{ section.settings.cover_image | img_url: '1500x' }} 1x, {{ section.settings.cover_image | img_url: '3000x' }} 2x"
                alt="{{ section.settings.cover_image.alt | escape }}"
                loading="lazy"
                width="{{ section.settings.cover_image.width }}"
                height="{{ section.settings.cover_image.height }}"
                class="full-width-video__cover-image"
              >
              <button type="button" class="full-width-video__play-button" data-video-play-button aria-label="Play video">
                <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </button>
            </div>
          {% endif %}
        </div>
      {% else %}
        <div class="full-width-video__placeholder">
          {{ 'lifestyle-1' | placeholder_svg_tag: 'full-width-video__placeholder-svg' }}
          <div class="full-width-video__placeholder-text">
            {% if section.settings.video_source == 'url' %}
              {{ 'sections.video.placeholder' | t | default: 'Add a video URL' }}
            {% else %}
              {{ 'sections.video.upload_placeholder' | t | default: 'Upload a video file' }}
            {% endif %}
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .full-width-video {
    margin: var(--spacing-extra-loose) 0;
  }

  .full-width-video__wrapper {
    position: relative;
  }

  .full-width-video__content-wrapper {
    margin-bottom: var(--spacing-base);
  }

  .full-width-video__content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .full-width-video__title {
    margin-bottom: var(--spacing-small);
  }

  .full-width-video__description {
    margin-bottom: var(--spacing-base);
  }

  .full-width-video__container {
    position: relative;
    width: 100%;
    background-color: #000;
  }

  .full-width-video__embed {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    max-width: 100%;
  }

  .full-width-video__player,
  .full-width-video__uploaded-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .full-width-video__cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 1;
  }

  .full-width-video__cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .full-width-video__play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background-color: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .full-width-video__play-button:hover {
    background-color: rgba(0, 0, 0, 0.9);
  }

  .full-width-video__play-button svg {
    width: 30px;
    height: 30px;
    fill: white;
  }

  .full-width-video__placeholder {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .full-width-video__placeholder-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.2;
  }

  .full-width-video__placeholder-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    color: var(--color-secondary);
  }

  @media screen and (max-width: 749px) {
    .full-width-video__play-button {
      width: 60px;
      height: 60px;
    }

    .full-width-video__play-button svg {
      width: 24px;
      height: 24px;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    initFullWidthVideo('{{ section.id }}');
  });

  function initFullWidthVideo(sectionId) {
    const container = document.querySelector(`[data-section-id="${sectionId}"]`);
    if (!container) return;

    const youtubeVideo = container.querySelector('[data-youtube-video]');
    const vimeoVideo = container.querySelector('[data-vimeo-video]');
    const uploadedVideo = container.querySelector('[data-uploaded-video]');
    const cover = container.querySelector('[data-video-cover]');
    const playButton = container.querySelector('[data-video-play-button]');

    let player;
    let isPlayerReady = false;
    let uploadedVideoElement;

    // Handle uploaded video
    if (uploadedVideo) {
      uploadedVideoElement = uploadedVideo.querySelector('video');
      if (uploadedVideoElement) {
        isPlayerReady = true;

        // Add debugging for video element
        console.log('Video element found:', uploadedVideoElement);
        console.log('Video source:', uploadedVideoElement.querySelector('source')?.src);
        console.log('Video type:', uploadedVideoElement.querySelector('source')?.type);

        // Add loadedmetadata event to check if video is loading properly
        uploadedVideoElement.addEventListener('loadedmetadata', function() {
          console.log('Video metadata loaded. Duration:', uploadedVideoElement.duration);
        });

        // Add error event to catch loading errors
        uploadedVideoElement.addEventListener('error', function(e) {
          console.error('Video error:', e);
        });

        uploadedVideoElement.querySelector('source')?.addEventListener('error', function(e) {
          console.error('Source error:', e);
        });
      } else {
        console.error('Video element not found in uploaded video container');
      }
    }

    // Load YouTube API if needed
    if (youtubeVideo && !window.YT) {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

      window.onYouTubeIframeAPIReady = function() {
        initYouTubePlayer();
      };
    } else if (youtubeVideo && window.YT && window.YT.Player) {
      initYouTubePlayer();
    }

    // Load Vimeo API if needed
    if (vimeoVideo && !window.Vimeo) {
      const tag = document.createElement('script');
      tag.src = 'https://player.vimeo.com/api/player.js';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

      tag.onload = function() {
        initVimeoPlayer();
      };
    } else if (vimeoVideo && window.Vimeo) {
      initVimeoPlayer();
    }

    // Initialize YouTube player
    function initYouTubePlayer() {
      const videoId = youtubeVideo.getAttribute('data-video-id');

      player = new YT.Player(`youtube-player-${sectionId}`, {
        videoId: videoId,
        playerVars: {
          autoplay: 0,
          controls: 1,
          rel: 0,
          showinfo: 0,
          modestbranding: 1,
          playsinline: 1
        },
        events: {
          'onReady': onPlayerReady
        }
      });
    }

    // Initialize Vimeo player
    function initVimeoPlayer() {
      const videoId = vimeoVideo.getAttribute('data-video-id');

      player = new Vimeo.Player(`vimeo-player-${sectionId}`, {
        id: videoId,
        autoplay: false,
        controls: true,
        title: false,
        byline: false,
        portrait: false
      });

      player.ready().then(function() {
        isPlayerReady = true;
      });
    }

    function onPlayerReady() {
      isPlayerReady = true;
    }

    // Play video when cover is clicked
    if (playButton) {
      playButton.addEventListener('click', function() {
        console.log('Play button clicked');

        if (youtubeVideo && isPlayerReady) {
          console.log('Playing YouTube video');
          player.playVideo();
          if (cover) {
            cover.style.display = 'none';
          }
        } else if (vimeoVideo && isPlayerReady) {
          console.log('Playing Vimeo video');
          player.play();
          if (cover) {
            cover.style.display = 'none';
          }
        } else if (uploadedVideoElement) {
          console.log('Attempting to play uploaded video');

          // Make sure video is loaded
          if (uploadedVideoElement.readyState === 0) {
            console.log('Video not loaded yet, loading now');
            uploadedVideoElement.load();
          }

          // For manual click, we can unmute the video
          uploadedVideoElement.muted = false;
          uploadedVideoElement.controls = true;

          // Hide cover immediately to show loading state
          if (cover) {
            cover.style.display = 'none';
          }

          // Try to play the video
          console.log('Calling play() on video element');
          const playPromise = uploadedVideoElement.play();

          // Handle the play promise
          if (playPromise !== undefined) {
            playPromise.then(() => {
              // Play started successfully
              console.log('Video playback started successfully');
            }).catch(error => {
              // Play was prevented
              console.error('Play prevented:', error);

              // Try again with muted video as a fallback
              console.log('Trying with muted video as fallback');
              uploadedVideoElement.muted = true;
              uploadedVideoElement.play().then(() => {
                console.log('Muted video playback started');
              }).catch(e => {
                console.error('Muted play also prevented:', e);

                // Show the cover again if all play attempts fail
                if (cover) {
                  console.log('Showing cover again due to playback failure');
                  cover.style.display = 'block';
                }
              });
            });
          } else {
            // For older browsers that don't return a promise
            console.log('Browser did not return play promise, assuming playback started');
          }
        } else {
          console.error('No video element found to play');
        }
      });
    }

    // Handle uploaded video cover click
    if (uploadedVideoElement && cover) {
      // Add direct click handler to the cover itself (in addition to the play button)
      cover.addEventListener('click', function() {
        console.log('Cover clicked directly');

        // Make sure video is loaded
        if (uploadedVideoElement.readyState === 0) {
          console.log('Video not loaded yet, loading now');
          uploadedVideoElement.load();
        }

        // For manual click, we can unmute the video
        uploadedVideoElement.muted = false;
        uploadedVideoElement.controls = true;

        // Hide cover immediately to show loading state
        cover.style.display = 'none';

        // Try to play the video
        console.log('Calling play() on video element from cover click');
        uploadedVideoElement.play().catch(error => {
          console.error('Play from cover click prevented:', error);

          // Try again with muted video as a fallback
          uploadedVideoElement.muted = true;
          uploadedVideoElement.play().catch(e => {
            console.error('Muted play from cover click also prevented:', e);
            cover.style.display = 'block';
          });
        });
      });

      // Add ended event to show cover again
      uploadedVideoElement.addEventListener('ended', function() {
        console.log('Video ended, showing cover again');
        cover.style.display = 'block';
      });

      // Add error event handler to show cover if video fails to load
      uploadedVideoElement.addEventListener('error', function() {
        console.error('Video error occurred, showing cover');
        cover.style.display = 'block';
      });
    }

    // Autoplay video when in viewport
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          console.log('Video container entered viewport');

          if (youtubeVideo && isPlayerReady) {
            console.log('Autoplaying YouTube video');
            player.playVideo();
            if (cover) {
              cover.style.display = 'none';
            }
          } else if (vimeoVideo && isPlayerReady) {
            console.log('Autoplaying Vimeo video');
            player.play();
            if (cover) {
              cover.style.display = 'none';
            }
          } else if (uploadedVideoElement) {
            console.log('Attempting to autoplay uploaded video');

            // Make sure video is loaded
            if (uploadedVideoElement.readyState === 0) {
              console.log('Video not loaded yet, loading now');
              uploadedVideoElement.load();
            }

            // Check if we're in the theme editor
            const isThemeEditor = typeof Shopify !== 'undefined' && Shopify.designMode === true;

            // Only autoplay if we're not in the theme editor
            if (!isThemeEditor) {
              console.log('Not in theme editor, attempting autoplay');

              // Ensure video is muted for autoplay to work in most browsers
              uploadedVideoElement.muted = true;

              // Try to play the video
              console.log('Calling play() on video element for autoplay');
              const playPromise = uploadedVideoElement.play();

              // Handle the play promise
              if (playPromise !== undefined) {
                playPromise.then(() => {
                  // Autoplay started successfully
                  console.log('Autoplay started successfully');
                  if (cover) {
                    cover.style.display = 'none';
                  }
                }).catch(error => {
                  // Autoplay was prevented
                  console.error('Autoplay prevented:', error);

                  // Keep the cover visible since autoplay failed
                  if (cover) {
                    console.log('Keeping cover visible due to autoplay failure');
                    cover.style.display = 'block';
                  }
                });
              } else {
                console.log('Browser did not return play promise for autoplay');
              }
            } else {
              console.log('In theme editor, skipping autoplay');
            }
          }
        } else if (!entry.isIntersecting) {
          console.log('Video container left viewport');

          if (youtubeVideo && isPlayerReady) {
            console.log('Pausing YouTube video');
            player.pauseVideo();
          } else if (vimeoVideo && isPlayerReady) {
            console.log('Pausing Vimeo video');
            player.pause();
          } else if (uploadedVideoElement) {
            // Pause the video when it's no longer in view
            if (!uploadedVideoElement.paused) {
              console.log('Pausing uploaded video');
              uploadedVideoElement.pause();
            } else {
              console.log('Uploaded video already paused');
            }
          }
        }
      });
    }, { threshold: 0.3 });

    const videoContainer = container.querySelector('[data-video-container]');
    if (videoContainer) {
      observer.observe(videoContainer);
    }
  }
</script>

{% schema %}
{
  "name": "Full Width Video",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Video"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Video Source"
    },
    {
      "type": "select",
      "id": "video_source",
      "label": "Video source",
      "options": [
        {
          "value": "url",
          "label": "External URL (YouTube/Vimeo)"
        },
        {
          "value": "upload",
          "label": "Uploaded video file"
        }
      ],
      "default": "url"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "label": "Video URL",
      "accept": ["youtube", "vimeo"],
      "info": "Supports YouTube and Vimeo",
      "placeholder": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
    },
    {
      "type": "video",
      "id": "video_file",
      "label": "Video file",
      "info": "Upload MP4 or WebM video file"
    },
    {
      "type": "image_picker",
      "id": "cover_image",
      "label": "Cover image"
    }
  ],
  "presets": [
    {
      "name": "Full Width Video",
      "category": "Video"
    }
  ]
}
{% endschema %}
