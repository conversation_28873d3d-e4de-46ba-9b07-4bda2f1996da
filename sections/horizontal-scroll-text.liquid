<div class="horizontal-scroll-section" id="horizontalScrollSection"
  data-section-id="{{ section.id }}"
  data-section-type="horizontal-scroll-text"
  style="background-color: {{ section.settings.background_color }}; height: {{ section.settings.section_height }}vh;">

  <div class="horizontal-scroll-container">
    <div class="horizontal-scroll-content" id="horizontalScrollContent">
      <div class="horizontal-scroll-text">
        {% assign words = section.settings.heading_text | split: ' ' %}
        <h2 class="horizontal-scroll-heading" style="color: {{ section.settings.text_color }};">
          {% for word in words %}
            <span class="scroll-word">{{ word }}</span>
          {% endfor %}
        </h2>
      </div>
    </div>
  </div>
</div>

<style>
  .horizontal-scroll-section {
    position: relative;
    width: 100%;
    min-height: 300px;
    overflow: hidden;
    display: flex;
    align-items: center;
  }

  .horizontal-scroll-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .horizontal-scroll-content {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    will-change: transform;
  }

  .horizontal-scroll-text {
    white-space: nowrap;
    padding: 0 250px;
    min-width: 100%;
  }

  .horizontal-scroll-heading {
    font-family: var(--font-heading);
    font-size: clamp(4rem, 10vw, 12rem);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: -0.02em;
    line-height: 1;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    will-change: transform;
  }

  .scroll-word {
    display: inline-block;
    margin-right: 20px;
    position: relative;
    transition: transform 0.3s ease;
  }

  .scroll-word:last-child {
    margin-right: 0;
  }

  /* Optional: Add a stylized effect to the first word */
  .scroll-word:first-child {
    position: relative;
    color: transparent;
    -webkit-text-stroke: 2px {{ section.settings.text_color }};
    background: linear-gradient({{ section.settings.text_color }} 0 100%) left / 0 no-repeat;
    -webkit-background-clip: text;
    background-clip: text;
  }

  /* Optional: Add a different effect to the last word */
  .scroll-word:last-child {
    color: {{ section.settings.accent_color }};
  }

  @media screen and (max-width: 989px) {
    .horizontal-scroll-section {
      min-height: 200px;
    }

    .horizontal-scroll-heading {
      font-size: clamp(3rem, 8vw, 8rem);
    }

    .horizontal-scroll-text {
      padding: 0 150px;
    }

    .scroll-word {
      margin-right: 15px;
    }

    .scroll-word:first-child {
      -webkit-text-stroke: 1.5px {{ section.settings.text_color }};
    }
  }

  @media screen and (max-width: 749px) {
    .horizontal-scroll-section {
      min-height: 150px;
    }

    .horizontal-scroll-heading {
      font-size: clamp(2.5rem, 7vw, 6rem);
    }

    .horizontal-scroll-text {
      padding: 0 80px;
    }

    .scroll-word {
      margin-right: 10px;
    }

    .scroll-word:first-child {
      -webkit-text-stroke: 1px {{ section.settings.text_color }};
    }
  }

  @media screen and (max-width: 480px) {
    .horizontal-scroll-section {
      min-height: 120px;
    }

    .horizontal-scroll-heading {
      font-size: clamp(1.8rem, 6vw, 4rem);
    }

    .horizontal-scroll-text {
      padding: 0 40px;
    }

    .scroll-word {
      margin-right: 8px;
    }
  }
</style>

<script>
  // Use Intersection Observer for better performance
  document.addEventListener('DOMContentLoaded', function() {
    const section = document.getElementById('horizontalScrollSection');
    const content = document.getElementById('horizontalScrollContent');
    const words = document.querySelectorAll('.scroll-word');

    if (!section || !content) return;

    // Set initial position
    content.style.transform = 'translateX(0)';

    // Variables for scroll calculations
    let textWidth, windowWidth, scrollDistance;

    // Calculate dimensions
    function calculateDimensions() {
      textWidth = content.scrollWidth;
      windowWidth = window.innerWidth;
      scrollDistance = textWidth - windowWidth + 500;
      return { textWidth, windowWidth, scrollDistance };
    }

    // Initial calculation
    calculateDimensions();

    // Function to update horizontal scroll position based on vertical scroll
    function updateHorizontalScroll(entries) {
      // Use the first entry (our section)
      const entry = entries[0];

      if (!entry.isIntersecting && entry.intersectionRatio === 0) {
        // Section is not visible at all
        return;
      }

      // Get the section's position relative to the viewport
      const rect = section.getBoundingClientRect();
      const sectionTop = rect.top;
      const sectionHeight = rect.height;
      const windowHeight = window.innerHeight;

      // Calculate how far through the section we've scrolled (0 to 1)
      let scrollProgress = 1 - (sectionTop + sectionHeight) / (windowHeight + sectionHeight);
      scrollProgress = Math.max(0, Math.min(1, scrollProgress)); // Clamp between 0 and 1

      // Apply the horizontal scroll with easing
      const translateX = -scrollProgress * scrollDistance;

      // Use transform with will-change already set in CSS
      content.style.transform = `translateX(${translateX}px)`;

      // Optimize word animations by batching DOM operations
      // Only animate words every other frame for performance
      if (window.scrollAnimationFrame % 2 === 0) {
        words.forEach((word, index) => {
          // Calculate a slight offset for each word to create a staggered effect
          const wordProgress = Math.max(0, Math.min(1, scrollProgress * 1.5 - index * 0.1));

          // Apply subtle scaling and rotation based on scroll position
          const scale = 1 + (wordProgress * 0.1);
          const yOffset = Math.sin(wordProgress * Math.PI) * 10;

          word.style.transform = `translateY(${yOffset}px) scale(${scale})`;
        });
      }
      window.scrollAnimationFrame++;
    }

    // Initialize animation frame counter
    window.scrollAnimationFrame = 0;

    // Create the Intersection Observer
    const observer = new IntersectionObserver(
      (entries) => {
        // Use requestAnimationFrame for smoother scrolling
        window.requestAnimationFrame(() => {
          updateHorizontalScroll(entries);
        });
      },
      {
        // Watch the section with these options
        root: null, // viewport
        rootMargin: "100px 0px", // start observing slightly before the section comes into view
        threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1] // multiple thresholds for smoother transitions
      }
    );

    // Start observing the section
    observer.observe(section);

    // Also listen for scroll events when the section is in view for smoother animation
    let isInView = false;
    let ticking = false;

    // Check if section is in view
    function checkIfInView() {
      const rect = section.getBoundingClientRect();
      isInView = rect.top < window.innerHeight && rect.bottom > 0;
    }

    // Scroll handler with throttling
    window.addEventListener('scroll', function() {
      checkIfInView();

      if (isInView && !ticking) {
        window.requestAnimationFrame(function() {
          updateHorizontalScroll([{isIntersecting: true, intersectionRatio: 0.5}]);
          ticking = false;
        });
        ticking = true;
      }
    });

    // Efficient resize handler with debouncing
    let resizeTimer;
    window.addEventListener('resize', function() {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(function() {
        // Recalculate dimensions
        calculateDimensions();

        // Update position if in view
        if (isInView) {
          updateHorizontalScroll([{isIntersecting: true, intersectionRatio: 0.5}]);
        }
      }, 250);
    });

    // Initial check
    checkIfInView();
  });
</script>

{% schema %}
{
  "name": "Horizontal Scroll Text",
  "settings": [
    {
      "type": "header",
      "content": "Text Settings"
    },
    {
      "type": "textarea",
      "id": "heading_text",
      "label": "Heading Text",
      "default": "LIFE IS AN ADVENTURE",
      "info": "Text will scroll horizontally as the user scrolls down the page"
    },
    {
      "type": "header",
      "content": "Color Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#f8f8f8"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent Color",
      "default": "#ff4500",
      "info": "Used for the last word in the text"
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "range",
      "id": "section_height",
      "min": 30,
      "max": 100,
      "step": 5,
      "unit": "vh",
      "label": "Section Height",
      "default": 50,
      "info": "Height of the section as a percentage of the viewport height"
    }
  ],
  "presets": [
    {
      "name": "Horizontal Scroll Text",
      "category": "Text"
    }
  ]
}
{% endschema %}
