<div class="hero-carousel"
     data-section-id="{{ section.id }}"
     data-section-type="hero-carousel"
     data-slides-per-view="{{ section.settings.slides_per_view }}"
     data-slides-spacing="{{ section.settings.slides_spacing }}"
     data-transition-type="{{ section.settings.transition_type }}"
     data-transition-speed="{{ section.settings.transition_speed }}">
  <div class="hero-carousel__wrapper">
    <div id="hero-carousel-{{ section.id }}" class="hero-carousel__slider">
      {% if section.blocks.size == 0 and template.name == 'index' %}
        <div class="hero-carousel__slide active">
          <div class="hero-carousel__slide-wrapper hero-carousel__slide-wrapper--center">
            <div class="hero-carousel__image-container" style="background-color: #f7f7f7;">
              <div class="hero-carousel__placeholder">
                {{ 'lifestyle-1' | placeholder_svg_tag: 'hero-carousel__placeholder-svg' }}
              </div>
            </div>
            <div class="hero-carousel__content">
              <h2 class="hero-carousel__heading">Add slide content</h2>
              <div class="hero-carousel__text">
                <p>Use the theme editor to add slides to your hero carousel.</p>
              </div>
            </div>
          </div>
        </div>
      {% else %}
        {% for block in section.blocks %}
          <div class="hero-carousel__slide{% if forloop.first %} active{% endif %}" {{ block.shopify_attributes }} data-block-id="{{ block.id }}">
            <div class="hero-carousel__slide-wrapper hero-carousel__slide-wrapper--{{ block.settings.content_position }}">
              {% if block.settings.image != blank %}
                <div class="hero-carousel__image-container">
                  {% comment %}
                    Preload links are now handled by the hero-carousel-preload snippet in the <head>
                    This improves performance by loading images earlier in the page lifecycle
                  {% endcomment %}

                  {% comment %} Desktop Image - Optimized loading {% endcomment %}
                  {% if forloop.first %}
                    <img
                        srcset="{%- if block.settings.image.width >= 1100 -%}{{ block.settings.image | img_url: '1100x', crop: 'center' }} 1100w,{%- endif -%}
                                {%- if block.settings.image.width >= 1500 -%}{{ block.settings.image | img_url: '1500x', crop: 'center' }} 1500w,{%- endif -%}
                                {%- if block.settings.image.width >= 2000 -%}{{ block.settings.image | img_url: '2000x', crop: 'center' }} 2000w{%- endif -%}"
                        src="{{ block.settings.image | img_url: '1100x', crop: 'center' }}"
                        sizes="100vw"
                        alt="{{ block.settings.image.alt | escape }}"
                        width="{{ block.settings.image.width }}"
                        height="{{ block.settings.image.height }}"
                        loading="eager"
                        fetchpriority="high"
                        decoding="sync"
                        importance="high"
                        class="hero-carousel__image {% if block.settings.image_mobile == blank %}hero-carousel__image--all-devices{% else %}hero-carousel__image--desktop{% endif %}{% if block.settings.image_overlay %} hero-carousel__image--overlay{% endif %}">
                  {% else %}
                    <img
                        srcset="{%- if block.settings.image.width >= 1100 -%}{{ block.settings.image | img_url: '1100x', crop: 'center' }} 1100w,{%- endif -%}
                                {%- if block.settings.image.width >= 1500 -%}{{ block.settings.image | img_url: '1500x', crop: 'center' }} 1500w,{%- endif -%}
                                {%- if block.settings.image.width >= 2000 -%}{{ block.settings.image | img_url: '2000x', crop: 'center' }} 2000w{%- endif -%}"
                        src="{{ block.settings.image | img_url: '1500x', crop: 'center' }}"
                        sizes="100vw"
                        alt="{{ block.settings.image.alt | escape }}"
                        width="{{ block.settings.image.width }}"
                        height="{{ block.settings.image.height }}"
                        loading="lazy"
                        fetchpriority="low"
                        decoding="async"
                        class="hero-carousel__image {% if block.settings.image_mobile == blank %}hero-carousel__image--all-devices{% else %}hero-carousel__image--desktop{% endif %}{% if block.settings.image_overlay %} hero-carousel__image--overlay{% endif %}">
                  {% endif %}

                  {% comment %} Mobile Image (only if provided) - Optimized for faster loading {% endcomment %}
                  {% if block.settings.image_mobile != blank %}
                    {% if forloop.first %}
                      {% comment %} For the first slide on mobile, use a more aggressive optimization approach {% endcomment %}
                      <img
                          srcset="{%- if block.settings.image_mobile.width >= 375 -%}{{ block.settings.image_mobile | img_url: '375x', crop: 'center' }} 375w,{%- endif -%}
                                  {%- if block.settings.image_mobile.width >= 550 -%}{{ block.settings.image_mobile | img_url: '550x', crop: 'center' }} 550w,{%- endif -%}
                                  {%- if block.settings.image_mobile.width >= 750 -%}{{ block.settings.image_mobile | img_url: '750x', crop: 'center' }} 750w{%- endif -%}"
                          src="{{ block.settings.image_mobile | img_url: '375x', crop: 'center' }}"
                          sizes="100vw"
                          alt="{{ block.settings.image_mobile.alt | default: block.settings.image.alt | escape }}"
                          width="{{ block.settings.image_mobile.width }}"
                          height="{{ block.settings.image_mobile.height }}"
                          loading="eager"
                          fetchpriority="high"
                          decoding="sync"
                          importance="high"
                          class="hero-carousel__image hero-carousel__image--mobile{% if block.settings.image_overlay %} hero-carousel__image--overlay{% endif %}">
                    {% else %}
                      {% comment %} For subsequent slides, use standard lazy loading {% endcomment %}
                      <img
                          srcset="{%- if block.settings.image_mobile.width >= 375 -%}{{ block.settings.image_mobile | img_url: '375x', crop: 'center' }} 375w,{%- endif -%}
                                  {%- if block.settings.image_mobile.width >= 550 -%}{{ block.settings.image_mobile | img_url: '550x', crop: 'center' }} 550w,{%- endif -%}
                                  {%- if block.settings.image_mobile.width >= 750 -%}{{ block.settings.image_mobile | img_url: '750x', crop: 'center' }} 750w{%- endif -%}"
                          src="{{ block.settings.image_mobile | img_url: '750x', crop: 'center' }}"
                          sizes="100vw"
                          alt="{{ block.settings.image_mobile.alt | default: block.settings.image.alt | escape }}"
                          width="{{ block.settings.image_mobile.width }}"
                          height="{{ block.settings.image_mobile.height }}"
                          loading="lazy"
                          fetchpriority="low"
                          decoding="async"
                          class="hero-carousel__image hero-carousel__image--mobile{% if block.settings.image_overlay %} hero-carousel__image--overlay{% endif %}">
                    {% endif %}
                  {% endif %}
                </div>
              {% else %}
                <div class="hero-carousel__image-container" style="background-color: {{ block.settings.background_color }};">
                  {% if template.name == 'index' %}
                    <div class="hero-carousel__placeholder">
                      {{ 'lifestyle-1' | placeholder_svg_tag: 'hero-carousel__placeholder-svg' }}
                    </div>
                  {% endif %}
                </div>
              {% endif %}

              <div class="hero-carousel__content">
                {% if block.settings.subtitle != blank %}
                  <div class="hero-carousel__subtitle" style="color: {{ block.settings.text_color }};">
                    {{ block.settings.subtitle | escape }}
                  </div>
                {% endif %}

                {% if block.settings.title != blank %}
                  <h2 class="hero-carousel__heading" style="color: {{ block.settings.text_color }};">
                    {{ block.settings.title | escape }}
                  </h2>
                {% endif %}

                {% if block.settings.text != blank %}
                  <div class="hero-carousel__text" style="color: {{ block.settings.text_color }};">
                    {{ block.settings.text }}
                  </div>
                {% endif %}

                {% if block.settings.button_label != blank and block.settings.button_link != blank %}
                  <div class="hero-carousel__buttons">
                    <a href="{{ block.settings.button_link }}" class="button hero-carousel__button">
                      {{ block.settings.button_label | escape }}
                    </a>

                    {% if block.settings.button_label_2 != blank and block.settings.button_link_2 != blank %}
                      <a href="{{ block.settings.button_link_2 }}" class="button button--secondary hero-carousel__button">
                        {{ block.settings.button_label_2 | escape }}
                      </a>
                    {% endif %}
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        {% endfor %}
      {% endif %}
    </div>

    {% if section.blocks.size > 1 %}
      <div class="hero-carousel__controls">
        <div class="hero-carousel__arrows">
          <button type="button" class="hero-carousel__arrow hero-carousel__arrow--prev" data-carousel-prev aria-label="Previous slide">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/>
            </svg>
          </button>
          <button type="button" class="hero-carousel__arrow hero-carousel__arrow--next" data-carousel-next aria-label="Next slide">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"/>
            </svg>
          </button>
        </div>
        <div class="hero-carousel__dots" data-carousel-dots></div>
      </div>
    {% endif %}
  </div>
</div>

{% render 'hero-carousel-init-mobile' %}

<script>
  // Optimize hero carousel image loading - Enhanced version for all slides
  (function() {
    // Check if we're on mobile - if so, we already initialized with the lightweight version
    const isMobile = window.matchMedia('(max-width: 749px)').matches;
    if (isMobile) return; // Skip desktop initialization on mobile

    // Execute immediately without waiting for DOMContentLoaded
    // to improve performance for all slides
    function initHeroCarousel() {
      const carousel = document.querySelector('.hero-carousel');
      if (!carousel) return;

      const slides = carousel.querySelectorAll('.hero-carousel__slide');
      if (slides.length <= 1) return;

      // Mark the first slide as active if not already
      if (!slides[0].classList.contains('active')) {
        slides[0].classList.add('active');
      }

      // Create a priority queue for loading images
      const imageQueue = [];

      // Function to load all slide images in priority order
      function queueAllSlideImages() {
        // First, add the active slide and next slide to the queue with high priority
        let activeIndex = 0;
        slides.forEach((slide, index) => {
          if (slide.classList.contains('active')) {
            activeIndex = index;
          }
        });

        // Calculate next and previous slide indices
        const nextIndex = (activeIndex + 1) % slides.length;
        const prevIndex = (activeIndex - 1 + slides.length) % slides.length;

        // Add slides to queue in priority order: active, next, previous, then others
        const priorityOrder = [
          { index: activeIndex, priority: 1 },
          { index: nextIndex, priority: 2 },
          { index: prevIndex, priority: 3 }
        ];

        // Add remaining slides with lower priority
        for (let i = 0; i < slides.length; i++) {
          if (i !== activeIndex && i !== nextIndex && i !== prevIndex) {
            priorityOrder.push({ index: i, priority: 4 + Math.abs(i - activeIndex) });
          }
        }

        // Process each slide in priority order
        priorityOrder.forEach(item => {
          const slide = slides[item.index];
          const images = slide.querySelectorAll('img');

          images.forEach(img => {
            imageQueue.push({
              img: img,
              priority: item.priority,
              loaded: img.complete
            });
          });
        });

        // Start loading images
        processImageQueue();
      }

      // Process the image queue with a small delay between each image
      function processImageQueue() {
        // Sort queue by priority
        imageQueue.sort((a, b) => a.priority - b.priority);

        // Process queue with delays to avoid network congestion
        imageQueue.forEach((item, index) => {
          if (!item.loaded) {
            setTimeout(() => {
              // Set loading to eager for high priority images
              if (item.priority <= 3) {
                item.img.setAttribute('loading', 'eager');
                item.img.setAttribute('fetchpriority', 'high');
              }

              // Force browser to load the image if it hasn't already
              if (!item.img.complete) {
                const currentSrc = item.img.currentSrc || item.img.src;
                if (currentSrc) {
                  const preloadLink = document.createElement('link');
                  preloadLink.rel = 'preload';
                  preloadLink.as = 'image';
                  preloadLink.href = currentSrc;
                  preloadLink.onload = function() {
                    item.loaded = true;
                    document.head.removeChild(preloadLink);
                  };
                  document.head.appendChild(preloadLink);
                }
              }
            }, index * 100); // Stagger loading by 100ms per image
          }
        });
      }

      // Queue all slide images immediately
      queueAllSlideImages();

      // Use Intersection Observer to detect when carousel is visible
      if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
          if (entries[0].isIntersecting) {
            // Refresh the queue when carousel becomes visible
            queueAllSlideImages();
            observer.disconnect();
          }
        }, {
          rootMargin: '200px' // Load when within 200px of viewport
        });

        observer.observe(carousel);
      }

      // Add event listeners for slide changes to update image loading priorities
      carousel.addEventListener('slideChanged', function(e) {
        if (e.detail && typeof e.detail.currentSlide === 'number') {
          // Reset and reprocess the queue with new priorities
          queueAllSlideImages();
        }
      });

      // Enhanced navigation controls with preloading
      const prevButton = carousel.querySelector('[data-carousel-prev]');
      const nextButton = carousel.querySelector('[data-carousel-next]');

      if (prevButton && nextButton) {
        // Function to ensure image is loaded before showing slide
        function ensureImageLoaded(slideIndex, callback) {
          const slide = slides[slideIndex];
          if (!slide) return callback(false);

          const images = slide.querySelectorAll('img');
          if (images.length === 0) return callback(true);

          let loadedCount = 0;
          let allLoaded = true;

          images.forEach(img => {
            if (img.complete) {
              loadedCount++;
            } else {
              allLoaded = false;
              // Set high priority loading
              img.setAttribute('loading', 'eager');
              img.setAttribute('fetchpriority', 'high');

              // Force load with preload link
              const currentSrc = img.currentSrc || img.src;
              if (currentSrc) {
                const preloadLink = document.createElement('link');
                preloadLink.rel = 'preload';
                preloadLink.as = 'image';
                preloadLink.href = currentSrc;
                document.head.appendChild(preloadLink);

                // Remove after a short time
                setTimeout(() => {
                  if (document.head.contains(preloadLink)) {
                    document.head.removeChild(preloadLink);
                  }
                }, 2000);
              }
            }
          });

          if (allLoaded) {
            return callback(true);
          }

          // If not all images are loaded, wait a bit and check again
          const maxAttempts = 10;
          let attempts = 0;

          function checkLoaded() {
            attempts++;
            let nowLoaded = true;

            images.forEach(img => {
              if (!img.complete) {
                nowLoaded = false;
              }
            });

            if (nowLoaded || attempts >= maxAttempts) {
              callback(true);
            } else {
              setTimeout(checkLoaded, 100);
            }
          }

          setTimeout(checkLoaded, 100);
        }

        // Enhanced navigation with preloading
        prevButton.addEventListener('click', function() {
          let activeIndex = 0;
          slides.forEach((slide, index) => {
            if (slide.classList.contains('active')) {
              activeIndex = index;
            }
          });

          const prevIndex = (activeIndex - 1 + slides.length) % slides.length;

          // Ensure image is loaded before showing slide
          ensureImageLoaded(prevIndex, function() {
            // Trigger custom event for slide change
            carousel.dispatchEvent(new CustomEvent('slideChanged', {
              detail: { currentSlide: prevIndex }
            }));
          });
        });

        nextButton.addEventListener('click', function() {
          let activeIndex = 0;
          slides.forEach((slide, index) => {
            if (slide.classList.contains('active')) {
              activeIndex = index;
            }
          });

          const nextIndex = (activeIndex + 1) % slides.length;

          // Ensure image is loaded before showing slide
          ensureImageLoaded(nextIndex, function() {
            // Trigger custom event for slide change
            carousel.dispatchEvent(new CustomEvent('slideChanged', {
              detail: { currentSlide: nextIndex }
            }));
          });
        });
      }
    }

    // Try to initialize immediately
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initHeroCarousel);
    } else {
      initHeroCarousel();
    }
  })();
</script>

<style>
  .hero-carousel {
    margin-bottom: 0;
    overflow: hidden;
  }

  .hero-carousel__wrapper {
    position: relative;
    height: 40vh; /* Fixed height of 40% of viewport height for desktop and tablet */
    overflow: hidden;
  }

  @media screen and (max-width: 749px) {
    .hero-carousel__wrapper {
      height: 50vh; /* Fixed height of 50% of viewport height for mobile */
      min-height: 300px; /* Minimum height to ensure visibility */
      max-height: 500px; /* Maximum height to prevent excessive space */
    }
  }

  .hero-carousel__slider {
    display: flex;
    transition: transform 0.5s ease;
    width: 100%;
    height: 100%; /* Take full height of parent */
    position: relative; /* Create positioning context for slides */
  }

  .hero-carousel__slide {
    flex: 0 0 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 0;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    z-index: 1;
    pointer-events: none;
    height: 100%; /* Ensure slide takes full height */
    display: none; /* Hide all slides by default */
  }

  .hero-carousel__slide.active {
    opacity: 1;
    z-index: 2;
    position: relative;
    pointer-events: auto;
    display: block !important; /* Show active slide */
  }

  /* Transition types */
  .hero-carousel[data-transition-type="fade"] .hero-carousel__slide {
    transition-property: opacity;
    transition-timing-function: ease;
  }

  .hero-carousel[data-transition-type="slide"] .hero-carousel__slide {
    transition-property: transform, opacity;
    transition-timing-function: ease;
    transform: translateX(100%);
  }

  .hero-carousel[data-transition-type="slide"] .hero-carousel__slide.active {
    transform: translateX(0);
  }

  .hero-carousel[data-transition-type="slide"] .hero-carousel__slide.prev {
    transform: translateX(-100%);
  }

  .hero-carousel[data-transition-type="none"] .hero-carousel__slide {
    transition: none;
  }

  /* Slides per view settings */
  .hero-carousel[data-slides-per-view="1"] .hero-carousel__slide.active {
    flex: 0 0 100%;
    width: 100%;
  }

  .hero-carousel[data-slides-per-view="2"] .hero-carousel__slide.active {
    flex: 0 0 50%;
    width: 50%;
    position: relative;
    display: inline-block;
    vertical-align: top;
  }

  .hero-carousel[data-slides-per-view="3"] .hero-carousel__slide.active {
    flex: 0 0 33.333%;
    width: 33.333%;
    position: relative;
    display: inline-block;
    vertical-align: top;
  }

  /* For multiple slides per view, we need to adjust the container */
  .hero-carousel[data-slides-per-view="2"] .hero-carousel__slider,
  .hero-carousel[data-slides-per-view="3"] .hero-carousel__slider {
    display: block;
    white-space: nowrap;
    font-size: 0; /* Remove space between inline-block elements */
  }

  /* Spacing between slides */
  .hero-carousel[data-slides-spacing="0"] .hero-carousel__slide {
    padding: 0;
  }

  .hero-carousel[data-slides-spacing="5"] .hero-carousel__slide {
    padding: 0 2.5px;
  }

  .hero-carousel[data-slides-spacing="10"] .hero-carousel__slide {
    padding: 0 5px;
  }

  .hero-carousel[data-slides-spacing="15"] .hero-carousel__slide {
    padding: 0 7.5px;
  }

  .hero-carousel[data-slides-spacing="20"] .hero-carousel__slide {
    padding: 0 10px;
  }

  .hero-carousel[data-slides-spacing="25"] .hero-carousel__slide {
    padding: 0 12.5px;
  }

  .hero-carousel[data-slides-spacing="30"] .hero-carousel__slide {
    padding: 0 15px;
  }

  /* Mobile always shows 1 slide */
  @media screen and (max-width: 749px) {
    .hero-carousel .hero-carousel__slide {
      flex: 0 0 100%;
      width: 100%;
    }
  }

  .hero-carousel__slide-wrapper {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%; /* Take full height of parent */
    border-radius: var(--border-radius, 0);
    display: flex; /* Use flexbox to ensure content fills the space */
    flex-direction: column; /* Stack children vertically */
  }

  /* Fallback for browsers that don't support vh units */
  @supports not (height: 40vh) {
    .hero-carousel__wrapper {
      height: 400px; /* Fixed fallback height for desktop */
    }

    /* Create a placeholder space that maintains height */
    .hero-carousel__wrapper::after {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(240, 240, 240, 0.1); /* Very light gray, almost invisible */
    }
  }

  .hero-carousel__image-container {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden; /* Prevent image overflow */
    flex: 1; /* Take up all available space */
    min-height: 0; /* Allow container to shrink if needed */
    background-color: #f7f7f7; /* Light background while images load */
    display: block; /* Ensure container is displayed */
  }

  .hero-carousel__placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .hero-carousel__placeholder-svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    max-height: 500px;
    opacity: 0.2;
  }

  .hero-carousel__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block; /* Prevent inline spacing issues */
    will-change: transform;
    transition: transform 0.8s ease;
    backface-visibility: hidden; /* Improves performance */
    transform: translateZ(0); /* Hardware acceleration */
    position: absolute; /* Ensure image doesn't affect layout flow */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    max-height: none; /* Override any max-height restrictions */
    min-height: 100%; /* Ensure image covers the full height */
  }

  /* Desktop/Mobile image display */
  .hero-carousel__image--all-devices {
    display: block; /* Always visible on all devices */
  }

  .hero-carousel__image--desktop {
    display: block;
  }

  .hero-carousel__image--mobile {
    display: none;
  }

  @media screen and (max-width: 749px) {
    .hero-carousel__image--desktop:not(.hero-carousel__image--all-devices) {
      display: none;
    }

    .hero-carousel__image--mobile {
      display: block;
    }
  }

  /* Only apply hover effect on non-touch devices */
  @media (hover: hover) {
    .hero-carousel__slide:hover .hero-carousel__image {
      transform: scale(1.05);
    }
  }

  .hero-carousel__image--overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .hero-carousel__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 90%;
    max-width: 800px;
    z-index: 1;
    color: white;
  }

  .hero-carousel__slide-wrapper--center .hero-carousel__content {
    text-align: center;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .hero-carousel__slide-wrapper--left .hero-carousel__content {
    text-align: left;
    left: 10%;
    transform: translateY(-50%);
  }

  .hero-carousel__slide-wrapper--right .hero-carousel__content {
    text-align: right;
    right: 10%;
    left: auto;
    transform: translateY(-50%);
  }

  .hero-carousel__subtitle {
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 15px;
  }

  .hero-carousel__heading {
    font-size: 3.5rem;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.1;
  }

  .hero-carousel__text {
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.1rem;
  }

  .hero-carousel__buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 30px;
  }

  .hero-carousel__slide-wrapper--center .hero-carousel__buttons {
    justify-content: center;
  }

  .hero-carousel__slide-wrapper--right .hero-carousel__buttons {
    justify-content: flex-end;
  }

  .hero-carousel__controls {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 2;
  }

  .hero-carousel__arrows {
    display: flex;
    gap: 10px;
  }

  .hero-carousel__arrow {
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .hero-carousel__arrow:hover {
    background-color: white;
  }

  .hero-carousel__arrow svg {
    width: 12px;
    height: 12px;
  }

  .hero-carousel__dots {
    display: flex;
    gap: 8px;
  }

  .hero-carousel__dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .hero-carousel__dot--active {
    background-color: white;
  }

  @media screen and (max-width: 989px) {
    .hero-carousel__heading {
      font-size: 2.5rem;
    }
  }

  @media screen and (max-width: 989px) {
    .hero-carousel__heading {
      font-size: 2.8rem;
    }

    .hero-carousel__text {
      font-size: 1.05rem;
    }

    .hero-carousel__subtitle {
      font-size: 0.9rem;
    }

    .hero-carousel__content {
      width: 80%;
    }

    /* Maintain 40% height on tablets */
    .hero-carousel__wrapper {
      height: 40vh;
    }

    /* Fallback for browsers that don't support vh units on tablet */
    @supports not (height: 40vh) {
      .hero-carousel__wrapper {
        height: 350px;
      }
    }
  }

  @media screen and (max-width: 749px) {
    .hero-carousel__wrapper {
      height: 50vh; /* Fixed height of 50% of viewport height for mobile */
    }

    /* Fallback for browsers that don't support vh units on mobile */
    @supports not (height: 50vh) {
      .hero-carousel__wrapper {
        height: 300px; /* Fixed fallback height for mobile */
      }
    }

    /* Ensure active slide is properly positioned on mobile */
    .hero-carousel__slide.active {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .hero-carousel__slide-wrapper--left .hero-carousel__content,
    .hero-carousel__slide-wrapper--right .hero-carousel__content {
      left: 50%;
      right: auto;
      transform: translate(-50%, -50%);
      text-align: center;
      width: 90%;
    }

    .hero-carousel__heading {
      font-size: 2rem;
      margin-bottom: 15px;
    }

    .hero-carousel__text {
      font-size: 1rem;
      margin-bottom: 20px;
    }

    .hero-carousel__subtitle {
      font-size: 0.85rem;
      margin-bottom: 10px;
    }

    .hero-carousel__buttons {
      flex-direction: column;
      width: 100%;
      gap: 10px;
      margin-top: 20px;
    }

    .hero-carousel__controls {
      bottom: 15px;
    }

    .hero-carousel__arrow {
      width: 35px;
      height: 35px;
    }
  }

  @media screen and (max-width: 480px) {
    .hero-carousel__heading {
      font-size: 1.7rem;
      margin-bottom: 12px;
    }

    .hero-carousel__text {
      font-size: 0.9rem;
      margin-bottom: 15px;
    }

    .hero-carousel__subtitle {
      font-size: 0.8rem;
      margin-bottom: 8px;
    }

    .hero-carousel__arrow {
      width: 30px;
      height: 30px;
    }

    .hero-carousel__arrow svg {
      width: 10px;
      height: 10px;
    }

    .hero-carousel__dot {
      width: 8px;
      height: 8px;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    initHeroCarousel('{{ section.id }}');
  });

  // Global variables
  let autoplayTimer = null;
  let currentSlideIndex = 0;

  function initHeroCarousel(sectionId) {
    const carousel = document.querySelector(`.hero-carousel[data-section-id="${sectionId}"]`);
    if (!carousel) return;

    // Clear any existing autoplay
    if (autoplayTimer) {
      clearInterval(autoplayTimer);
      autoplayTimer = null;
    }

    // Get carousel elements
    const slider = carousel.querySelector('.hero-carousel__slider');
    const slides = Array.from(carousel.querySelectorAll('.hero-carousel__slide'));
    const prevButton = carousel.querySelector('[data-carousel-prev]');
    const nextButton = carousel.querySelector('[data-carousel-next]');
    const dotsContainer = carousel.querySelector('[data-carousel-dots]');

    // If no slides, exit
    if (slides.length === 0) return;

    // Get settings from data attributes
    const slidesPerView = parseInt(carousel.getAttribute('data-slides-per-view')) || 1;
    const slidesSpacing = parseInt(carousel.getAttribute('data-slides-spacing')) || 0;
    const transitionType = carousel.getAttribute('data-transition-type') || 'fade';
    const transitionSpeed = parseInt(carousel.getAttribute('data-transition-speed')) || 500;

    // Apply transition speed to slides
    slides.forEach(slide => {
      slide.style.transitionDuration = `${transitionSpeed}ms`;
    });

    // Check if we're in the theme editor
    const isInThemeEditor = window.Shopify && window.Shopify.designMode;

    // Only enable autoplay if it's enabled in settings and not in the theme editor
    const shouldAutoplay = {{ section.settings.autoplay }} && !isInThemeEditor;

    // Set up the carousel
    setupCarousel();
    setupDots();
    setupNavigation();

    // Start autoplay if enabled
    if (shouldAutoplay) {
      startAutoplay();
    }

    // Function to set up the carousel
    function setupCarousel() {
      // Reset current slide index
      currentSlideIndex = 0;

      // Ensure the first slide has the active class
      if (slides.length > 0 && !slides[0].classList.contains('active')) {
        slides[0].classList.add('active');
      }

      // Set initial slide positions
      updateSlides();
    }

    // Function to update slides based on current index
    function updateSlides() {
      let prevIndex = currentSlideIndex - 1;
      if (prevIndex < 0) prevIndex = slides.length - 1;

      slides.forEach((slide, index) => {
        // Remove all classes first
        slide.classList.remove('active', 'prev');

        // For visible slides
        if (index >= currentSlideIndex && index < currentSlideIndex + slidesPerView) {
          // Add active class for current slides
          slide.classList.add('active');
        } else if (transitionType === 'slide' && index === prevIndex) {
          // Add prev class for slide transition
          slide.classList.add('prev');
        }
      });

      // Update dots
      updateDots();
    }

    // Function to go to a specific slide
    function goToSlide(index) {
      // Handle loop
      if (index < 0) {
        index = slides.length - slidesPerView;
      } else if (index > slides.length - slidesPerView) {
        index = 0;
      }

      currentSlideIndex = index;
      updateSlides();
    }

    // Function to go to the next slide
    function nextSlide() {
      goToSlide(currentSlideIndex + 1);
    }

    // Function to go to the previous slide
    function prevSlide() {
      goToSlide(currentSlideIndex - 1);
    }

    // Function to set up navigation
    function setupNavigation() {
      if (prevButton) {
        prevButton.addEventListener('click', function() {
          prevSlide();
          if (shouldAutoplay) {
            stopAutoplay();
            startAutoplay();
          }
        });
      }

      if (nextButton) {
        nextButton.addEventListener('click', function() {
          nextSlide();
          if (shouldAutoplay) {
            stopAutoplay();
            startAutoplay();
          }
        });
      }
    }

    // Function to set up dots
    function setupDots() {
      if (!dotsContainer) return;

      // Clear existing dots
      dotsContainer.innerHTML = '';

      // Create dots
      slides.forEach((_, index) => {
        if (index <= slides.length - slidesPerView) {
          const dot = document.createElement('button');
          dot.classList.add('hero-carousel__dot');
          dot.setAttribute('aria-label', `Go to slide ${index + 1}`);
          dot.addEventListener('click', function() {
            goToSlide(index);
            if (shouldAutoplay) {
              stopAutoplay();
              startAutoplay();
            }
          });
          dotsContainer.appendChild(dot);
        }
      });

      updateDots();
    }

    // Function to update dots
    function updateDots() {
      if (!dotsContainer) return;

      const dots = Array.from(dotsContainer.querySelectorAll('.hero-carousel__dot'));
      dots.forEach((dot, index) => {
        if (index === currentSlideIndex) {
          dot.classList.add('hero-carousel__dot--active');
        } else {
          dot.classList.remove('hero-carousel__dot--active');
        }
      });
    }

    // Function to start autoplay
    function startAutoplay() {
      autoplayTimer = setInterval(() => {
        nextSlide();
      }, {{ section.settings.autoplay_speed }});
    }

    // Function to stop autoplay
    function stopAutoplay() {
      if (autoplayTimer) {
        clearInterval(autoplayTimer);
        autoplayTimer = null;
      }
    }

    // Public API
    return {
      goToSlide,
      nextSlide,
      prevSlide,
      startAutoplay,
      stopAutoplay
    };
  }

  // Handle Shopify theme editor events
  document.addEventListener('shopify:section:load', function(event) {
    if (event.detail.sectionId === '{{ section.id }}') {
      setTimeout(() => {
        initHeroCarousel('{{ section.id }}');
      }, 50);
    }
  });

  document.addEventListener('shopify:section:unload', function(event) {
    if (event.detail.sectionId === '{{ section.id }}' && autoplayTimer) {
      clearInterval(autoplayTimer);
      autoplayTimer = null;
    }
  });

  document.addEventListener('shopify:section:select', function(event) {
    if (event.detail.sectionId === '{{ section.id }}') {
      // Stop autoplay when section is selected in the editor
      if (autoplayTimer) {
        clearInterval(autoplayTimer);
        autoplayTimer = null;
      }

      // Reinitialize to apply any setting changes
      initHeroCarousel('{{ section.id }}');
    }
  });

  document.addEventListener('shopify:block:select', function(event) {
    if (event.detail.sectionId === '{{ section.id }}') {
      // Find the slide index
      const carousel = document.querySelector(`.hero-carousel[data-section-id="${event.detail.sectionId}"]`);
      if (!carousel) return;

      const slides = Array.from(carousel.querySelectorAll('.hero-carousel__slide'));
      const selectedSlide = slides.find(slide => slide.getAttribute('data-block-id') === event.detail.blockId);
      if (!selectedSlide) return;

      const slideIndex = slides.indexOf(selectedSlide);

      // Go to the selected slide
      const carouselInstance = initHeroCarousel('{{ section.id }}');
      if (carouselInstance) {
        carouselInstance.goToSlide(slideIndex);
      }

      // Stop autoplay
      if (autoplayTimer) {
        clearInterval(autoplayTimer);
        autoplayTimer = null;
      }
    }
  });

  // Special handler for settings changes in the theme editor
  if (window.Shopify && window.Shopify.designMode) {
    document.addEventListener('shopify:section:render', function(event) {
      if (event.detail.sectionId === '{{ section.id }}') {
        setTimeout(() => {
          initHeroCarousel('{{ section.id }}');
        }, 100);
      }
    });
  }
</script>

{% schema %}
{
  "name": "Hero Carousel",
  "settings": [
    {
      "type": "range",
      "id": "height",
      "min": 240,
      "max": 600,
      "step": 30,
      "unit": "px",
      "label": "Desktop height",
      "default": 390
    },
    {
      "type": "range",
      "id": "height_mobile",
      "min": 180,
      "max": 500,
      "step": 20,
      "unit": "px",
      "label": "Mobile height",
      "default": 300
    },
    {
      "type": "range",
      "id": "slides_per_view",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Slides per view",
      "default": 1,
      "info": "Number of slides visible at once"
    },
    {
      "type": "range",
      "id": "slides_spacing",
      "min": 0,
      "max": 30,
      "step": 5,
      "unit": "px",
      "label": "Space between slides",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable autoplay",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 3000,
      "max": 8000,
      "step": 500,
      "unit": "ms",
      "label": "Autoplay speed",
      "default": 5000
    },
    {
      "type": "select",
      "id": "transition_type",
      "label": "Transition effect",
      "options": [
        {
          "value": "fade",
          "label": "Fade"
        },
        {
          "value": "slide",
          "label": "Slide"
        },
        {
          "value": "none",
          "label": "None"
        }
      ],
      "default": "fade"
    },
    {
      "type": "range",
      "id": "transition_speed",
      "min": 200,
      "max": 1000,
      "step": 100,
      "unit": "ms",
      "label": "Transition speed",
      "default": 500
    },
    {
      "type": "header",
      "content": "Performance"
    },
    {
      "type": "paragraph",
      "content": "This section supports separate desktop and mobile images for optimal performance. For best results, use JPG images that are properly sized for each device: desktop (1500 x 800px) and mobile (750 x 1000px)."
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "Slide",
      "settings": [
        {
          "type": "header",
          "content": "Images"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Desktop image",
          "info": "Recommended size: 1500 x 800 pixels"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "Mobile image (optional)",
          "info": "If not provided, desktop image will be used. Recommended size: 750 x 1000 pixels"
        },
        {
          "type": "checkbox",
          "id": "image_overlay",
          "label": "Add image overlay",
          "default": true
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background color (if no image)",
          "default": "#f7f7f7"
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "New collection"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Welcome to our store"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Add a welcome message to your customers</p>"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "#FFFFFF"
        },
        {
          "type": "header",
          "content": "Buttons"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "Primary button label",
          "default": "Shop now"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Primary button link"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "label": "Secondary button label"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "Secondary button link"
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Content position",
          "options": [
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Hero Carousel",
      "category": "Image",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}
