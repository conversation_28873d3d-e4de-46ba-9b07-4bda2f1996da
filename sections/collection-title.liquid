{% comment %}
  Collection Title and Description
  
  Displays the collection title and description below the banner, left-aligned.
{% endcomment %}

<div class="collection-title" data-section-id="{{ section.id }}" data-section-type="collection-title">
  <div class="page-width">
    <div class="collection-title__wrapper">
      {% if section.settings.show_title %}
        <h1 class="collection-title__heading">
          {% if collection.title %}
            {{ collection.title }}
          {% else %}
            {{ section.settings.custom_title | default: 'Collection' }}
          {% endif %}
        </h1>
      {% endif %}

      {% if section.settings.show_description and collection.description != blank %}
        <div class="collection-title__description rte">
          {{ collection.description }}
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .collection-title {
    margin-bottom: 30px;
    margin-top: 30px;
  }

  .collection-title__wrapper {
    text-align: left;
  }

  .collection-title__heading {
    font-size: 28px;
    margin: 0 0 15px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .collection-title__description {
    margin-top: 10px;
    font-size: 16px;
    line-height: 1.6;
  }

  @media screen and (max-width: 749px) {
    .collection-title__heading {
      font-size: 24px;
    }
  }
</style>

{% schema %}
{
  "name": "Collection Title",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_title",
      "label": "Show collection title",
      "default": true
    },
    {
      "type": "text",
      "id": "custom_title",
      "label": "Custom title",
      "info": "Used if collection title is not available"
    },
    {
      "type": "checkbox",
      "id": "show_description",
      "label": "Show collection description",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Collection Title",
      "category": "Collection"
    }
  ]
}
{% endschema %}
