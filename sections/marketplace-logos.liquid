<div class="marketplace-logos"
  data-section-id="{{ section.id }}"
  data-section-type="marketplace-logos"
  data-slides-per-view="{{ section.settings.logos_per_row }}"
  data-slides-per-view-tablet="{{ section.settings.logos_per_row_tablet }}"
  data-slides-per-view-mobile="{{ section.settings.logos_per_row_mobile }}"
  data-autoplay-speed="{{ section.settings.autoplay_speed }}"
  data-autoplay="{{ section.settings.autoplay }}">
  <div class="page-width">
    {% if section.settings.heading_first_word != blank and section.settings.heading_second_word != blank %}
      {% render 'section-heading',
        first_word: section.settings.heading_first_word,
        second_word: section.settings.heading_second_word,
        outline_color: section.settings.heading_outline_color,
        filled_color: section.settings.heading_filled_color
      %}
    {% elsif section.settings.title != blank %}
      <div class="section-header text-center">
        <h2 class="section-title">{{ section.settings.title | escape }}</h2>
        {% if section.settings.description != blank %}
          <div class="section-description rte">{{ section.settings.description }}</div>
        {% endif %}
      </div>
    {% endif %}

    <div class="marketplace-logos__wrapper">
      <div id="marketplace-logos-{{ section.id }}" class="keen-slider marketplace-logos__slider">
        {% for block in section.blocks %}
          <div class="keen-slider__slide marketplace-logos__slide" {{ block.shopify_attributes }}>
            {% if block.settings.link != blank %}
              <a href="{{ block.settings.link }}" class="marketplace-logos__link" target="_blank" rel="noopener">
            {% endif %}

            {% if block.settings.logo != blank %}
              <div class="marketplace-logos__logo-wrapper">
                <img
                  src="{{ block.settings.logo | img_url: '200x' }}"
                  alt="{{ block.settings.logo.alt | escape }}"
                  loading="lazy"
                  width="{{ block.settings.logo.width }}"
                  height="{{ block.settings.logo.height }}"
                  class="marketplace-logos__logo"
                >
              </div>
            {% else %}
              <div class="marketplace-logos__placeholder">
                {{ 'image' | placeholder_svg_tag: 'marketplace-logos__placeholder-svg' }}
              </div>
            {% endif %}

            {% if block.settings.link != blank %}
              </a>
            {% endif %}
          </div>
        {% endfor %}
      </div>

      {% if section.blocks.size > section.settings.logos_per_row %}
        <div class="marketplace-logos__controls">
          <button type="button" class="marketplace-logos__arrow marketplace-logos__arrow--prev" data-carousel-prev aria-label="Previous logos">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/>
            </svg>
          </button>
          <button type="button" class="marketplace-logos__arrow marketplace-logos__arrow--next" data-carousel-next aria-label="Next logos">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"/>
            </svg>
          </button>
        </div>
      {% endif %}

      {% if section.settings.show_dots %}
        <div class="marketplace-logos__dots" data-carousel-dots></div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .marketplace-logos {
    margin: var(--spacing-extra-loose) 0;
  }

  .marketplace-logos__wrapper {
    position: relative;
    padding: 0 40px;
    margin-top: var(--spacing-base);
  }

  .marketplace-logos__slider {
    position: relative;
    overflow: hidden;
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
  }

  .marketplace-logos__slide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-base);
    height: 100px;
    box-sizing: border-box;
  }

  .marketplace-logos__link {
    display: block;
    text-decoration: none;
    color: inherit;
  }

  .marketplace-logos__logo-wrapper {
    max-width: 150px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .marketplace-logos__logo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .marketplace-logos__link:hover .marketplace-logos__logo {
    opacity: 1;
  }

  .marketplace-logos__placeholder {
    width: 150px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .marketplace-logos__placeholder-svg {
    width: 100%;
    height: 100%;
    max-width: 120px;
    opacity: 0.3;
  }

  .marketplace-logos__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
  }

  .marketplace-logos__arrow {
    background-color: white;
    border: 1px solid var(--color-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
  }

  .marketplace-logos__arrow:hover {
    background-color: var(--color-background-light);
  }

  .marketplace-logos__arrow svg {
    width: 12px;
    height: 12px;
  }

  .marketplace-logos__dots {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-base);
  }

  @media screen and (max-width: 989px) {
    .marketplace-logos__wrapper {
      padding: 0 30px;
    }

    .marketplace-logos__slide {
      height: 90px;
    }

    .marketplace-logos__logo-wrapper {
      max-width: 130px;
      height: 55px;
    }
  }

  @media screen and (max-width: 749px) {
    .marketplace-logos__wrapper {
      padding: 0 25px;
    }

    .marketplace-logos__slide {
      height: 80px;
      padding: var(--spacing-small);
    }

    .marketplace-logos__logo-wrapper {
      max-width: 120px;
      height: 50px;
    }

    .marketplace-logos__arrow {
      width: 30px;
      height: 30px;
    }

    .marketplace-logos__arrow svg {
      width: 10px;
      height: 10px;
    }
  }

  @media screen and (max-width: 480px) {
    .marketplace-logos__wrapper {
      padding: 0 20px;
    }

    .marketplace-logos__slide {
      height: 70px;
      padding: 8px;
    }

    .marketplace-logos__logo-wrapper {
      max-width: 100px;
      height: 45px;
    }

    .marketplace-logos__arrow {
      width: 25px;
      height: 25px;
    }

    .marketplace-logos__arrow svg {
      width: 8px;
      height: 8px;
    }

    .marketplace-logos {
      margin: var(--spacing-loose) 0;
    }
  }
</style>

<script>
  // Marketplace logos carousel will be initialized by carousel-init.js
  // All configuration is passed via data attributes on the carousel element

  // Add a direct initialization script as a fallback
  document.addEventListener('DOMContentLoaded', function() {
    // Wait for keen-slider.js to load
    setTimeout(function() {
      if (typeof KeenSlider === 'function') {
        const container = document.querySelector('[data-section-id="{{ section.id }}"]');
        if (container && !container.keenSlider) {
          // Get settings from data attributes
          const slidesPerView = parseInt(container.getAttribute('data-slides-per-view') || 5, 10);
          const slidesPerViewTablet = parseInt(container.getAttribute('data-slides-per-view-tablet') || 3, 10);
          const slidesPerViewMobile = parseInt(container.getAttribute('data-slides-per-view-mobile') || 2, 10);

          // Set slide widths directly
          const slides = container.querySelectorAll('.keen-slider__slide');
          const windowWidth = window.innerWidth;
          let currentSlidesPerView = slidesPerView;

          if (windowWidth < 750) {
            currentSlidesPerView = slidesPerViewMobile;
          } else if (windowWidth < 990) {
            currentSlidesPerView = slidesPerViewTablet;
          }

          const slideWidth = 100 / currentSlidesPerView;
          slides.forEach(slide => {
            slide.style.cssText = `min-width: ${slideWidth}% !important; max-width: ${slideWidth}% !important; width: ${slideWidth}% !important;`;
          });

          // Initialize carousel if not already initialized
          if (typeof window.initCarousels === 'function') {
            window.initCarousels('{{ section.id }}');
          }
        }
      }
    }, 500); // Wait for other scripts to load
  });
</script>

{% schema %}
{
  "name": "Marketplace Logos",
  "settings": [
    {
      "type": "header",
      "content": "Heading Style"
    },
    {
      "type": "text",
      "id": "heading_first_word",
      "label": "Heading First Word (Outlined)",
      "default": "Also"
    },
    {
      "type": "text",
      "id": "heading_second_word",
      "label": "Heading Second Word (Filled)",
      "default": "Available"
    },
    {
      "type": "color",
      "id": "heading_outline_color",
      "label": "Outline Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "heading_filled_color",
      "label": "Filled Text Color",
      "default": "#000000"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Alternative Heading (Legacy)",
      "default": "Also available on",
      "info": "Used if the outlined/filled heading words are not provided"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Carousel Settings"
    },
    {
      "type": "range",
      "id": "logos_per_row",
      "min": 3,
      "max": 8,
      "step": 1,
      "label": "Logos per row (desktop)",
      "default": 5
    },
    {
      "type": "range",
      "id": "logos_per_row_tablet",
      "min": 2,
      "max": 4,
      "step": 1,
      "label": "Logos per row (tablet)",
      "default": 3
    },
    {
      "type": "range",
      "id": "logos_per_row_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Logos per row (mobile)",
      "default": 2
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Enable autoplay",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 2000,
      "max": 8000,
      "step": 500,
      "unit": "ms",
      "label": "Autoplay speed",
      "default": 3000
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "label": "Show dots",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "logo",
      "name": "Marketplace Logo",
      "settings": [
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Logo image"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Marketplace Logos",
      "category": "Image",
      "blocks": [
        {
          "type": "logo"
        },
        {
          "type": "logo"
        },
        {
          "type": "logo"
        },
        {
          "type": "logo"
        }
      ]
    }
  ]
}
{% endschema %}
