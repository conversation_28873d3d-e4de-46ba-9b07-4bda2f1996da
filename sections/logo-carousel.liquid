<div class="logo-carousel"
  data-section-id="{{ section.id }}"
  data-section-type="logo-carousel"
  data-slides-per-view="{{ section.settings.logos_per_row }}"
  data-slides-per-view-tablet="{{ section.settings.logos_per_row_tablet }}"
  data-slides-per-view-mobile="{{ section.settings.logos_per_row_mobile }}"
  data-autoplay-speed="{{ section.settings.autoplay_speed }}">
  <div class="page-width">
    {% if section.settings.heading_first_word != blank and section.settings.heading_second_word != blank %}
      {% render 'section-heading',
        first_word: section.settings.heading_first_word,
        second_word: section.settings.heading_second_word,
        outline_color: section.settings.heading_outline_color,
        filled_color: section.settings.heading_filled_color
      %}
    {% elsif section.settings.title != blank %}
      <div class="section-header text-center">
        <h2 class="section-title">{{ section.settings.title | escape }}</h2>
      </div>
    {% endif %}

    {% if section.settings.description != blank %}
      <div class="section-description rte text-center">
        {{ section.settings.description }}
      </div>
    {% endif %}

    <div class="logo-carousel__wrapper">
      <div id="logo-carousel-{{ section.id }}" class="keen-slider logo-carousel__slider">
        {% for block in section.blocks %}
          <div class="keen-slider__slide logo-carousel__slide" {{ block.shopify_attributes }}>
            {% if block.settings.logo != blank %}
              <div class="logo-carousel__logo-wrapper">
                <img
                  src="{{ block.settings.logo | img_url: '200x' }}"
                  alt="{{ block.settings.logo.alt | escape }}"
                  loading="lazy"
                  width="{{ block.settings.logo.width }}"
                  height="{{ block.settings.logo.height }}"
                  class="logo-carousel__logo"
                >
              </div>
            {% else %}
              <div class="logo-carousel__placeholder">
                {{ 'image' | placeholder_svg_tag: 'logo-carousel__placeholder-svg' }}
              </div>
            {% endif %}
          </div>
        {% endfor %}
      </div>

      {% if section.blocks.size > section.settings.logos_per_row %}
        <div class="logo-carousel__controls">
          <button type="button" class="logo-carousel__arrow logo-carousel__arrow--prev" data-carousel-prev aria-label="Previous logos">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/>
            </svg>
          </button>
          <button type="button" class="logo-carousel__arrow logo-carousel__arrow--next" data-carousel-next aria-label="Next logos">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"/>
            </svg>
          </button>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .logo-carousel {
    margin: var(--spacing-extra-loose) 0;
  }

  .logo-carousel__wrapper {
    position: relative;
    padding: 0 40px;
  }

  .logo-carousel__slider {
    overflow: visible;
  }

  .logo-carousel__slide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-base);
    height: 100px;
  }

  .logo-carousel__logo-wrapper {
    max-width: 100%;
    max-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    min-height: 80px;
    min-width: 80px;
  }

  .logo-carousel__logo {
    opacity: 0.8;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  .logo-carousel__slide:hover .logo-carousel__logo {
    opacity: 1;
  }

  .logo-carousel__placeholder {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo-carousel__placeholder-svg {
    width: 100%;
    height: 100%;
    max-width: 120px;
    opacity: 0.3;
  }

  .logo-carousel__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
  }

  .logo-carousel__arrow {
    background-color: white;
    border: 1px solid var(--color-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
  }

  .logo-carousel__arrow:hover {
    background-color: var(--color-background-light);
  }

  .logo-carousel__arrow svg {
    width: 12px;
    height: 12px;
  }

  @media screen and (max-width: 989px) {
    .logo-carousel__wrapper {
      padding: 0 30px;
    }

    .logo-carousel__slide {
      height: 90px;
    }

    .logo-carousel__logo {
      max-height: 70px;
    }

    .section-title {
      font-size: 22px;
    }
  }

  @media screen and (max-width: 749px) {
    .logo-carousel__wrapper {
      padding: 0 25px;
    }

    .logo-carousel__slide {
      height: 80px;
      padding: var(--spacing-small);
    }

    .logo-carousel__logo {
      max-height: 60px;
    }

    .logo-carousel__arrow {
      width: 30px;
      height: 30px;
    }

    .logo-carousel__arrow svg {
      width: 10px;
      height: 10px;
    }

    .section-title {
      font-size: 20px;
    }
  }

  @media screen and (max-width: 480px) {
    .logo-carousel__wrapper {
      padding: 0 20px;
    }

    .logo-carousel__slide {
      height: 70px;
      padding: 8px;
    }

    .logo-carousel__logo {
      max-height: 50px;
    }

    .logo-carousel__arrow {
      width: 25px;
      height: 25px;
    }

    .logo-carousel__arrow svg {
      width: 8px;
      height: 8px;
    }

    .logo-carousel {
      margin: var(--spacing-loose) 0;
    }
  }
</style>

<script>
  // Logo carousel will be initialized by theme-utilities.js
  // All configuration is passed via data attributes on the carousel element
</script>

{% schema %}
{
  "name": "Logo Carousel",
  "settings": [
    {
      "type": "header",
      "content": "Heading Style"
    },
    {
      "type": "text",
      "id": "heading_first_word",
      "label": "Heading First Word (Outlined)",
      "default": "Featured"
    },
    {
      "type": "text",
      "id": "heading_second_word",
      "label": "Heading Second Word (Filled)",
      "default": "In"
    },
    {
      "type": "color",
      "id": "heading_outline_color",
      "label": "Outline Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "heading_filled_color",
      "label": "Filled Text Color",
      "default": "#000000"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Alternative Heading (Legacy)",
      "default": "Featured in",
      "info": "Used if the outlined/filled heading words are not provided"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "range",
      "id": "logos_per_row",
      "min": 3,
      "max": 8,
      "step": 1,
      "label": "Logos per row (desktop)",
      "default": 5
    },
    {
      "type": "range",
      "id": "logos_per_row_tablet",
      "min": 2,
      "max": 4,
      "step": 1,
      "label": "Logos per row (tablet)",
      "default": 3
    },
    {
      "type": "range",
      "id": "logos_per_row_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Logos per row (mobile)",
      "default": 2
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 2000,
      "max": 8000,
      "step": 500,
      "unit": "ms",
      "label": "Autoplay speed",
      "default": 3000
    }
  ],
  "blocks": [
    {
      "type": "logo",
      "name": "Logo",
      "settings": [
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Logo image"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link (optional)"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Logo Carousel",
      "category": "Image",
      "settings": {
        "heading_first_word": "Featured",
        "heading_second_word": "In",
        "heading_outline_color": "#000000",
        "heading_filled_color": "#000000"
      },
      "blocks": [
        {
          "type": "logo"
        },
        {
          "type": "logo"
        },
        {
          "type": "logo"
        },
        {
          "type": "logo"
        },
        {
          "type": "logo"
        }
      ]
    }
  ]
}
{% endschema %}
