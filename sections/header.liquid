{% if section.settings.announcement_enable %}
  <div class="announcement-bar">
    <div class="page-width">
      <p class="announcement-bar__message">{{ section.settings.announcement_text }}</p>
    </div>
  </div>
{% endif %}

<header class="site-header" data-section-id="{{ section.id }}" data-section-type="header">
  <div class="header-top-container" style="background-color: {{ section.settings.logo_bar_bg_color }}; color: {{ section.settings.logo_bar_text_color }};">
    <div class="header-wrapper">
      <div class="header-left">
        <button type="button" class="mobile-nav-toggle medium-down--show" aria-controls="MobileNav" aria-expanded="false" style="color: {{ section.settings.logo_bar_text_color }};">
          <span class="mobile-nav-toggle-bar" style="background-color: {{ section.settings.logo_bar_text_color }};"></span>
          <span class="mobile-nav-toggle-bar" style="background-color: {{ section.settings.logo_bar_text_color }};"></span>
          <span class="mobile-nav-toggle-bar" style="background-color: {{ section.settings.logo_bar_text_color }};"></span>
          <span class="visually-hidden">{{ 'general.navigation.menu' | t }}</span>
        </button>

        {% if section.settings.sale_enable %}
          <div class="header-sale-announcement" data-rotation-speed="{{ section.settings.sale_rotation_speed | times: 1000 }}">
            {% assign has_announcements = false %}

            {% for i in (1..3) %}
              {% assign text_key = 'sale_text_' | append: i %}
              {% assign link_key = 'sale_link_' | append: i %}

              {% if section.settings[text_key] != blank %}
                {% assign has_announcements = true %}
                <div class="sale-announcement__item{% if forloop.first %} active{% endif %}" data-announcement-index="{{ forloop.index0 }}">
                  {% if section.settings[link_key] != blank %}
                    <a href="{{ section.settings[link_key] }}" class="sale-announcement__link" style="color: {{ section.settings.logo_bar_text_color }};">
                      <span class="sale-announcement__text">{{ section.settings[text_key] }}</span>
                    </a>
                  {% else %}
                    <span class="sale-announcement__text" style="color: {{ section.settings.logo_bar_text_color }};">{{ section.settings[text_key] }}</span>
                  {% endif %}
                </div>
              {% endif %}
            {% endfor %}

            {% unless has_announcements %}
              <div class="sale-announcement__item active">
                <span class="sale-announcement__text" style="color: {{ section.settings.logo_bar_text_color }};">SALE: Up to 50% off</span>
              </div>
            {% endunless %}
          </div>
        {% endif %}
      </div>

      <div class="header-logo">
        {% if section.settings.logo != blank %}
          <a href="/" class="logo-image">
            {% assign logo_alt = section.settings.logo.alt | default: shop.name %}
            {% assign logo_size = section.settings.logo_max_width | append: 'x' %}
            <img src="{{ section.settings.logo | img_url: logo_size }}"
                 srcset="{{ section.settings.logo | img_url: logo_size }} 1x, {{ section.settings.logo | img_url: logo_size, scale: 2 }} 2x"
                 alt="{{ logo_alt }}"
                 width="{{ section.settings.logo_max_width }}"
                 height="{{ section.settings.logo.height | divided_by: section.settings.logo.width | times: section.settings.logo_max_width | round }}"
                 loading="eager">
          </a>
        {% else %}
          <a href="/" class="site-header__logo-link">{{ shop.name }}</a>
        {% endif %}
      </div>

      <div class="header-icons">
        <button type="button" class="header-icon" data-search-toggle aria-controls="SearchOverlay" aria-expanded="false" style="color: {{ section.settings.logo_bar_text_color }};">
          {% render 'icon-search' %}
          <span class="visually-hidden">{{ 'general.search.search' | t }}</span>
        </button>
        <a href="/account" class="header-icon" style="color: {{ section.settings.logo_bar_text_color }};">
          {% render 'icon-account' %}
          <span class="visually-hidden">{{ 'customer.account.title' | t }}</span>
        </a>
        <button type="button" class="header-icon cart-icon" style="color: {{ section.settings.logo_bar_text_color }};">
          {% render 'icon-cart' %}
          <span class="visually-hidden">{{ 'cart.general.title' | t }}</span>
          <span class="cart-count" data-cart-count style="background-color: {{ section.settings.cart_notification_color }}; color: {{ section.settings.cart_notification_text_color }};">
            0
          </span>
        </button>
      </div>

      {% comment %}
        Include optimized header functionality
      {% endcomment %}
      {% render 'optimized-header' %}
    </div>
  </div>

  <div class="header-nav-container" id="headerNavContainer">
    <div class="nav-wrapper">
      <div class="nav-logo-container">
        {% if section.settings.menu_logo_different and section.settings.menu_logo != blank %}
          <a href="/" class="nav-logo-image">
            {% assign menu_logo_alt = section.settings.menu_logo.alt | default: shop.name %}
            {% assign menu_logo_size = section.settings.menu_logo_max_width | append: 'x' %}
            <img src="{{ section.settings.menu_logo | img_url: menu_logo_size }}"
                 srcset="{{ section.settings.menu_logo | img_url: menu_logo_size }} 1x, {{ section.settings.menu_logo | img_url: menu_logo_size, scale: 2 }} 2x"
                 alt="{{ menu_logo_alt }}"
                 width="{{ section.settings.menu_logo_max_width }}"
                 height="{{ section.settings.menu_logo.height | divided_by: section.settings.menu_logo.width | times: section.settings.menu_logo_max_width | round }}"
                 loading="eager">
          </a>
        {% elsif section.settings.logo != blank %}
          <a href="/" class="nav-logo-image">
            {% assign logo_alt = section.settings.logo.alt | default: shop.name %}
            {% assign logo_size = section.settings.logo_max_width | append: 'x' %}
            <img src="{{ section.settings.logo | img_url: logo_size }}"
                 srcset="{{ section.settings.logo | img_url: logo_size }} 1x, {{ section.settings.logo | img_url: logo_size, scale: 2 }} 2x"
                 alt="{{ logo_alt }}"
                 width="{{ section.settings.logo_max_width | divided_by: 2 }}"
                 height="{{ section.settings.logo.height | divided_by: section.settings.logo.width | times: section.settings.logo_max_width | divided_by: 2 | round }}"
                 loading="eager">
          </a>
        {% else %}
          <a href="/" class="nav-logo-text">{{ shop.name }}</a>
        {% endif %}
      </div>

      <nav class="header-nav" role="navigation">
        {% if section.settings.menu != blank %}
          <ul class="site-nav medium-down--hide">
            {% for link in linklists[section.settings.menu].links %}
              <li class="site-nav__item{% if link.active %} site-nav__item--active{% endif %}{% if link.links.size > 0 %} site-nav__item--has-dropdown{% endif %}">
                <a href="{{ link.url }}" class="site-nav__link{% if link.links.size > 0 %} site-nav__link--has-dropdown{% endif %}">
                  {{ link.title }}
                  {% if link.links.size > 0 %}
                    <span class="site-nav__dropdown-toggle">
                      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-chevron-down" viewBox="0 0 24 24">
                        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                      </svg>
                    </span>
                  {% endif %}
                </a>
                {% if link.links.size > 0 %}
                  {% render 'mega-menu-dropdown', link: link %}
                {% endif %}
              </li>
            {% endfor %}
          </ul>
        {% endif %}
      </nav>

      <div class="nav-icons">
        <button type="button" class="header-icon" data-search-toggle aria-controls="SearchOverlay" aria-expanded="false">
          {% render 'icon-search' %}
          <span class="visually-hidden">{{ 'general.search.search' | t }}</span>
        </button>
        <a href="/account" class="header-icon">
          {% render 'icon-account' %}
          <span class="visually-hidden">{{ 'customer.account.title' | t }}</span>
        </a>
        <button type="button" class="header-icon cart-icon">
          {% render 'icon-cart' %}
          <span class="visually-hidden">{{ 'cart.general.title' | t }}</span>
          <span class="cart-count" data-cart-count style="background-color: {{ section.settings.cart_notification_color }}; color: {{ section.settings.cart_notification_text_color }};">
            {{ cart.item_count }}
          </span>
        </button>
      </div>
    </div>
  </div>

  <div class="mobile-nav medium-down--show" id="MobileNav" aria-hidden="true">
    <div class="mobile-nav__inner">
      <button type="button" class="mobile-nav__close" aria-controls="MobileNav" aria-expanded="true" tabindex="-1">
        <span class="mobile-nav__close-icon"></span>
        <span class="visually-hidden">{{ 'general.navigation.close_menu' | t }}</span>
      </button>

      {% if section.settings.menu != blank %}
        <ul class="mobile-nav__list">
          {% for link in linklists[section.settings.menu].links %}
            <li class="mobile-nav__item{% if link.active %} mobile-nav__item--active{% endif %}">
              <a href="{{ link.url }}" class="mobile-nav__link" tabindex="-1">
                {{ link.title }}
              </a>
              {% if link.links.size > 0 %}
                <ul class="mobile-nav__dropdown">
                  {% for child_link in link.links %}
                    <li class="mobile-nav__dropdown-item">
                      <a href="{{ child_link.url }}" class="mobile-nav__dropdown-link" tabindex="-1">
                        {{ child_link.title }}
                      </a>
                    </li>
                  {% endfor %}
                </ul>
              {% endif %}
            </li>
          {% endfor %}
        </ul>
      {% endif %}
    </div>
  </div>

  {% render 'search-overlay' %}
</header>

<style>
  /* Header styles */
  .site-header {
    padding: 0;
    border-bottom: none;
    position: relative;
    z-index: 100;
    width: 100%;
  }

  .header-top-container {
    padding: 15px 0;
    width: 100%;
  }

  .header-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--gutter);
  }

  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .header-sale-announcement {
    margin-left: 20px;
    position: relative;
    min-height: 20px;
    overflow: hidden;
    min-width: 150px;
    max-width: 250px;
  }

  .sale-announcement__item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease, visibility 0.5s ease;
  }

  .sale-announcement__item.active {
    opacity: 1;
    visibility: visible;
    position: relative;
  }

  .sale-announcement__link {
    display: inline-block;
    font-weight: var(--font-weight-medium);
    font-size: 14px;
    transition: opacity 0.2s ease;
    white-space: normal;
    line-height: 1.2;
  }

  .sale-announcement__link:hover {
    opacity: 0.8;
  }

  .sale-announcement__text {
    display: inline-block;
    font-weight: var(--font-weight-medium);
    font-size: 14px;
    white-space: normal;
    line-height: 1.2;
  }

  .header-logo {
    flex: 0 0 auto;
    text-align: center;
    margin: 0 auto;
  }

  .header-logo img {
    max-height: 50px;
    width: auto;
  }

  .header-icons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
  }

  .header-icon {
    background: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
    margin-left: 20px;
    color: var(--color-text-primary);
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .icon {
    width: 20px;
    height: 20px;
  }

  .cart-icon {
    position: relative;
  }

  .cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 10px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
  }

  /* Navigation styles */
  .header-nav-container {
    background-color: var(--color-background);
    border-top: 1px solid var(--color-border);
    border-bottom: 1px solid var(--color-border);
    width: 100%;
    position: relative;
    z-index: 100;
    transition: position 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
    padding: 5px 0;
  }

  .header-nav-container.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease;
  }

  @keyframes slideDown {
    from {
      transform: translateY(-100%);
    }
    to {
      transform: translateY(0);
    }
  }

  .nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 var(--gutter);
  }

  .nav-logo-container {
    opacity: 0;
    visibility: hidden;
    width: 0;
    overflow: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease, width 0.3s ease;
    flex: 0 0 auto;
  }

  .fixed .nav-logo-container {
    opacity: 1;
    visibility: visible;
    width: auto;
    margin-right: 30px;
    display: flex;
    align-items: center;
  }

  .nav-logo-image img {
    max-height: 40px;
    width: auto;
    object-fit: contain;
  }

  .nav-logo-text {
    font-weight: var(--font-weight-bold);
    font-size: 16px;
  }

  .nav-icons {
    display: none;
  }

  .fixed .nav-icons {
    display: flex;
    align-items: center;
  }

  .header-nav {
    display: flex;
    justify-content: center;
    flex: 1;
  }

  .site-nav {
    display: flex;
    justify-content: center;
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
  }

  .site-nav__item {
    position: relative;
    margin: 0 10px;
    height: 46px; /* Fixed height to prevent layout shifts */
  }

  .site-nav__link {
    display: block;
    padding: 15px 0;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: var(--font-weight-bold);
    position: relative;
    color: var(--color-text-primary);
    height: 100%;
    box-sizing: border-box;
  }

  .site-nav__link--has-dropdown {
    display: flex;
    align-items: center;
  }

  .site-nav__dropdown-toggle {
    margin-left: 5px;
    display: flex;
    align-items: center;
    width: 12px; /* Fixed width to prevent layout shifts */
    height: 12px; /* Fixed height to prevent layout shifts */
  }

  .icon-chevron-down {
    width: 12px;
    height: 12px;
    transition: transform 0.3s ease;
    display: block; /* Ensure consistent display */
    flex: 0 0 auto; /* Prevent flex resizing */
  }

  /* Mega menu dropdown */
  .site-nav__item--has-dropdown {
    position: relative;
  }

  .mega-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    width: 220px;
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
    z-index: 100;
    pointer-events: none; /* Prevent interaction when hidden */
    will-change: transform, opacity; /* Optimize for animations */
  }

  .site-nav__item--has-dropdown:hover .mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
    pointer-events: auto; /* Re-enable interaction when visible */
  }

  .site-nav__item--has-dropdown:hover .icon-chevron-down {
    transform: rotate(180deg);
  }

  .mega-menu__inner {
    padding: 20px;
  }

  .mega-menu__list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .mega-menu__item {
    margin-bottom: 10px;
  }

  .mega-menu__link {
    font-size: 12px;
    color: var(--color-text-primary);
    transition: color 0.2s ease;
    display: block;
    padding: 5px 0;
  }

  .mega-menu__link:hover {
    color: var(--color-primary);
  }

  /* Mobile navigation */
  .mobile-nav-toggle {
    display: none;
    background: transparent;
    border: none;
    padding: 0;
    cursor: pointer;
  }

  .mobile-nav-toggle-bar {
    display: block;
    width: 20px;
    height: 2px;
    background-color: var(--color-text-primary);
    margin: 4px 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
  }

  .mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-background);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    display: none;
  }

  .mobile-nav[aria-hidden="false"] {
    transform: translateX(0);
  }

  .mobile-nav__inner {
    padding: 30px 20px;
    height: 100%;
    overflow-y: auto;
  }

  .mobile-nav__close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: transparent;
    border: none;
    padding: 10px;
    cursor: pointer;
  }

  .mobile-nav__close-icon {
    position: relative;
    display: block;
    width: 20px;
    height: 20px;
  }

  .mobile-nav__close-icon::before,
  .mobile-nav__close-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--color-text-primary);
  }

  .mobile-nav__close-icon::before {
    transform: rotate(45deg);
  }

  .mobile-nav__close-icon::after {
    transform: rotate(-45deg);
  }

  .mobile-nav__list {
    list-style: none;
    padding: 0;
    margin: 40px 0 0;
  }

  .mobile-nav__item {
    margin-bottom: 20px;
  }

  .mobile-nav__link {
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-text-primary);
  }

  .mobile-nav__dropdown {
    list-style: none;
    padding: 0 0 0 20px;
    margin: 10px 0 0;
  }

  .mobile-nav__dropdown-item {
    margin-bottom: 10px;
  }

  .mobile-nav__dropdown-link {
    font-size: 14px;
    color: var(--color-text-primary);
  }

  @media screen and (max-width: 989px) {
    .medium-down--show {
      display: block;
    }

    .medium-down--hide {
      display: none;
    }

    .mobile-nav-toggle {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
    }

    .mobile-nav {
      display: block;
    }

    .header-nav-container {
      display: none;
    }

    .header-top-container {
      padding: 15px 0;
    }

    .header-wrapper,
    .nav-wrapper {
      padding: 0 var(--gutter);
    }

    .header-sale-announcement {
      margin-left: 15px;
      min-width: 120px;
      min-height: 18px;
      max-width: 200px;
    }

    .sale-announcement__text {
      font-size: 13px;
    }
  }

  @media screen and (max-width: 749px) {
    .header-wrapper,
    .nav-wrapper {
      padding: 0 20px;
    }

    /* Mobile header layout */
    .header-wrapper {
      display: grid;
      grid-template-columns: auto 1fr auto;
      grid-template-areas: "toggle logo icons";
      align-items: center;
    }

    .header-left {
      grid-area: toggle;
      flex: 0 0 auto;
      margin-right: 15px;
    }

    .header-logo {
      grid-area: logo;
      margin: 0;
      text-align: left;
      justify-self: start;
    }

    .header-logo img {
      max-height: 40px;
    }

    .header-icons {
      grid-area: icons;
      flex: 0 0 auto;
    }

    .header-sale-announcement {
      margin-left: 10px;
      min-width: 100px;
      min-height: 16px;
      max-width: 150px;
    }

    .sale-announcement__text {
      font-size: 12px;
    }
  }

  @media screen and (max-width: 480px) {
    .header-sale-announcement {
      display: none;
    }

    .header-wrapper,
    .nav-wrapper {
      padding: 0 15px;
    }

    /* Tighter spacing for very small screens */
    .header-left {
      margin-right: 10px;
    }

    .header-logo img {
      max-height: 35px;
    }

    .header-icon {
      margin-left: 15px;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Mobile navigation toggle
    const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
    const mobileNavClose = document.querySelector('.mobile-nav__close');
    const mobileNav = document.getElementById('MobileNav');
    let mobileNavFocusTrap = null;

    if (mobileNavToggle && mobileNavClose && mobileNav) {
      // Create a focus trap for the mobile nav
      mobileNavFocusTrap = window.accessibilityUtils.createFocusTrap(mobileNav);

      mobileNavToggle.addEventListener('click', function() {
        const isHidden = mobileNav.getAttribute('aria-hidden') === 'true';

        if (isHidden) {
          // Opening the mobile nav
          mobileNav.setAttribute('aria-hidden', 'false');
          mobileNavToggle.setAttribute('aria-expanded', 'true');

          // Enable all focusable elements inside
          const focusableElements = mobileNav.querySelectorAll('a, button, input, select, textarea, [tabindex="-1"]');
          focusableElements.forEach(el => {
            if (el.hasAttribute('data-aria-hidden-tabindex')) {
              el.removeAttribute('tabindex');
            }
          });

          // Activate focus trap
          mobileNavFocusTrap.activate();

          // Prevent scrolling on body
          document.body.style.overflow = 'hidden';
        } else {
          // Closing the mobile nav
          mobileNav.setAttribute('aria-hidden', 'true');
          mobileNavToggle.setAttribute('aria-expanded', 'false');

          // Deactivate focus trap
          mobileNavFocusTrap.deactivate();

          // Restore scrolling
          document.body.style.overflow = '';

          // Return focus to toggle button
          mobileNavToggle.focus();
        }
      });

      mobileNavClose.addEventListener('click', function() {
        mobileNav.setAttribute('aria-hidden', 'true');
        mobileNavToggle.setAttribute('aria-expanded', 'false');

        // Deactivate focus trap
        mobileNavFocusTrap.deactivate();

        // Restore scrolling
        document.body.style.overflow = '';

        // Return focus to toggle button
        mobileNavToggle.focus();
      });

      // Close on escape key
      mobileNav.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          mobileNav.setAttribute('aria-hidden', 'true');
          mobileNavToggle.setAttribute('aria-expanded', 'false');

          // Deactivate focus trap
          mobileNavFocusTrap.deactivate();

          // Restore scrolling
          document.body.style.overflow = '';

          // Return focus to toggle button
          mobileNavToggle.focus();
        }
      });
    }

    // Mobile dropdown toggles
    const mobileNavItems = document.querySelectorAll('.mobile-nav__item');

    mobileNavItems.forEach(item => {
      const dropdown = item.querySelector('.mobile-nav__dropdown');
      if (dropdown) {
        const link = item.querySelector('.mobile-nav__link');

        link.addEventListener('click', function(e) {
          e.preventDefault();
          dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        });
      }
    });

    // Rotating sale announcements
    const saleAnnouncementContainer = document.querySelector('.header-sale-announcement');
    if (saleAnnouncementContainer) {
      const announcements = saleAnnouncementContainer.querySelectorAll('.sale-announcement__item');
      if (announcements.length > 1) {
        const rotationSpeed = parseInt(saleAnnouncementContainer.getAttribute('data-rotation-speed')) || 5000;
        let currentIndex = 0;

        // Function to rotate announcements
        function rotateAnnouncements() {
          // Hide current announcement
          announcements[currentIndex].classList.remove('active');

          // Move to next announcement
          currentIndex = (currentIndex + 1) % announcements.length;

          // Show next announcement
          announcements[currentIndex].classList.add('active');
        }

        // Start rotation
        setInterval(rotateAnnouncements, rotationSpeed);
      }
    }

    // Fixed header on scroll
    const headerTopContainer = document.querySelector('.header-top-container');
    const headerNavContainer = document.getElementById('headerNavContainer');
    let lastScrollTop = 0;
    let headerHeight = headerTopContainer ? headerTopContainer.offsetHeight : 0;

    function handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // Add fixed class when scrolling down past the header height
      if (scrollTop > headerHeight) {
        headerNavContainer.classList.add('fixed');
        document.body.style.paddingTop = headerNavContainer.offsetHeight + 'px';
      } else {
        headerNavContainer.classList.remove('fixed');
        document.body.style.paddingTop = '0';
      }

      lastScrollTop = scrollTop;
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Recalculate header height on resize
    window.addEventListener('resize', function() {
      headerHeight = headerTopContainer ? headerTopContainer.offsetHeight : 0;
      if (!headerNavContainer.classList.contains('fixed')) {
        document.body.style.paddingTop = '0';
      }
    });
  });
</script>

{% schema %}
{
  "name": "Header",
  "settings": [
    {
      "type": "header",
      "content": "Announcement bar"
    },
    {
      "type": "checkbox",
      "id": "announcement_enable",
      "label": "Show announcement",
      "default": false
    },
    {
      "type": "text",
      "id": "announcement_text",
      "label": "Announcement text",
      "default": "Announce something here"
    },
    {
      "type": "header",
      "content": "Sale Announcements"
    },
    {
      "type": "checkbox",
      "id": "sale_enable",
      "label": "Show sale announcements",
      "default": false
    },
    {
      "type": "range",
      "id": "sale_rotation_speed",
      "min": 2,
      "max": 10,
      "step": 1,
      "unit": "s",
      "label": "Rotation speed",
      "default": 5,
      "info": "Time in seconds between announcements"
    },
    {
      "type": "text",
      "id": "sale_text_1",
      "label": "Sale text 1",
      "default": "SALE: Up to 50% off"
    },
    {
      "type": "url",
      "id": "sale_link_1",
      "label": "Sale link 1",
      "info": "Optional link to sale page"
    },
    {
      "type": "text",
      "id": "sale_text_2",
      "label": "Sale text 2",
      "default": "Free shipping on all orders"
    },
    {
      "type": "url",
      "id": "sale_link_2",
      "label": "Sale link 2",
      "info": "Optional link to sale page"
    },
    {
      "type": "text",
      "id": "sale_text_3",
      "label": "Sale text 3",
      "default": "New arrivals - Shop now"
    },
    {
      "type": "url",
      "id": "sale_link_3",
      "label": "Sale link 3",
      "info": "Optional link to sale page"
    },
    {
      "type": "header",
      "content": "Logo Bar"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo image"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 50,
      "max": 250,
      "step": 5,
      "unit": "px",
      "label": "Logo maximum width",
      "default": 100
    },
    {
      "type": "header",
      "content": "Menu Bar Logo"
    },
    {
      "type": "checkbox",
      "id": "menu_logo_different",
      "label": "Use different logo for menu bar",
      "default": false,
      "info": "When enabled, you can upload a different logo for the menu bar"
    },
    {
      "type": "image_picker",
      "id": "menu_logo",
      "label": "Menu bar logo image"
    },
    {
      "type": "range",
      "id": "menu_logo_max_width",
      "min": 30,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Menu bar logo maximum width",
      "default": 50
    },
    {
      "type": "color",
      "id": "logo_bar_bg_color",
      "label": "Logo bar background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "logo_bar_text_color",
      "label": "Logo bar text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "cart_notification_color",
      "label": "Cart notification badge color",
      "default": "#ff0000"
    },
    {
      "type": "color",
      "id": "cart_notification_text_color",
      "label": "Cart notification text color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "Menu",
      "default": "main-menu"
    }
  ]
}
{% endschema %}
