<div class="featured-collection" data-section-id="{{ section.id }}" data-section-type="featured-collection">
  <div class="page-width">
    {% if section.settings.heading_first_word != blank and section.settings.heading_second_word != blank %}
      {% render 'section-heading',
        first_word: section.settings.heading_first_word,
        second_word: section.settings.heading_second_word,
        outline_color: section.settings.heading_outline_color,
        filled_color: section.settings.heading_filled_color
      %}
    {% elsif section.settings.title != blank %}
      <div class="section-header text-center">
        <h2 class="section-title">{{ section.settings.title | escape }}</h2>
        {% if section.settings.description != blank %}
          <div class="section-description rte">{{ section.settings.description }}</div>
        {% endif %}
      </div>
    {% endif %}

    {%- assign collection_handle = section.settings.collection | default: '' -%}
    {%- if collection_handle == blank -%}
      {%- assign collection = collections['445425778991'] -%}
    {%- else -%}
      {%- assign collection = collections[collection_handle] -%}
    {%- endif -%}

    {% if collection != blank and collection.products.size > 0 %}
      <div class="featured-collection__grid">
        {% for product in collection.products limit: section.settings.products_limit %}
          <div class="featured-collection__grid-item">
            <div class="product-card">
              <a href="{{ product.url }}" class="product-card__link">
                <div class="product-card__image-wrapper">
                  {% if product.featured_media %}
                    <img
                      src="{{ product.featured_media | img_url: '300x300', crop: 'center' }}"
                      srcset="{{ product.featured_media | img_url: '300x300', crop: 'center' }} 1x, {{ product.featured_media | img_url: '600x600', crop: 'center' }} 2x"
                      alt="{{ product.featured_media.alt | escape }}"
                      loading="lazy"
                      width="300"
                      height="300"
                      class="product-card__image"
                    >
                  {% else %}
                    {{ 'product-1' | placeholder_svg_tag: 'product-card__image placeholder-svg' }}
                  {% endif %}

                  {% if product.compare_at_price > product.price %}
                    <span class="product-card__badge">{{ 'products.product.on_sale' | t }}</span>
                  {% endif %}
                </div>

                <div class="product-card__info">
                  <h3 class="product-card__title">{{ product.title }}</h3>

                  <div class="product-card__price">
                    {% if product.compare_at_price > product.price %}
                      <span class="product-card__price--sale">{{ product.price | money }}</span>
                      <span class="product-card__price--compare">{{ product.compare_at_price | money }}</span>
                    {% else %}
                      <span class="product-card__price--regular">{{ product.price | money }}</span>
                    {% endif %}
                  </div>
                </div>
              </a>

              {% if product.available %}
                <form method="post" action="/cart/add" class="product-card__form">
                  <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                  <button type="submit" class="button button--small">
                    {{ 'products.product.add_to_cart' | t }}
                  </button>
                </form>
              {% else %}
                <button type="button" class="button button--small button--disabled" disabled>
                  {{ 'products.product.sold_out' | t }}
                </button>
              {% endif %}
            </div>
          </div>
        {% endfor %}
      </div>

      {% if section.settings.show_view_all and section.settings.collection != blank %}
        <div class="featured-collection__view-all text-center">
          {% render 'button',
            text: 'collections.general.view_all' | t,
            url: collection.url,
            with_arrow: true
          %}
        </div>
      {% endif %}
    {% else %}
      <div class="featured-collection__placeholder">
        <div class="placeholder-message">
          {% if collection == blank and section.settings.collection != blank %}
            {{ 'collections.general.collection_not_exist' | t }}
          {% elsif section.settings.collection == blank %}
            {{ 'homepage.onboarding.no_collection_selected' | t }}
          {% else %}
            {{ 'collections.general.no_matches' | t }}
          {% endif %}
        </div>
      </div>
    {% endif %}
  </div>
</div>

<style>
  .featured-collection {
    margin: var(--spacing-extra-loose) 0;
  }

  .section-header {
    margin-bottom: var(--spacing-base);
  }

  .section-title {
    margin-bottom: var(--spacing-tight);
  }

  .section-description {
    max-width: 700px;
    margin: 0 auto;
  }

  .featured-collection__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-base);
  }

  .product-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
    overflow: hidden;
  }

  .product-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .product-card__link {
    display: block;
    text-decoration: none;
    color: inherit;
  }

  .product-card__image-wrapper {
    position: relative;
    padding-top: 100%;
    overflow: hidden;
  }

  .product-card__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .product-card:hover .product-card__image {
    transform: scale(1.05);
  }

  .product-card__badge {
    position: absolute;
    top: var(--spacing-tight);
    right: var(--spacing-tight);
    background-color: var(--color-primary);
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: var(--border-radius);
  }

  .product-card__info {
    padding: var(--spacing-base);
    flex-grow: 1;
  }

  .product-card__title {
    font-size: 16px;
    margin-bottom: var(--spacing-tight);
    font-weight: 500;
  }

  .product-card__price {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-tight);
  }

  .product-card__price--sale {
    color: #e32c2b;
    font-weight: 700;
  }

  .product-card__price--compare {
    text-decoration: line-through;
    opacity: 0.6;
    font-size: 14px;
  }

  .product-card__price--regular {
    font-weight: 700;
  }

  .product-card__form {
    padding: 0 var(--spacing-base) var(--spacing-base);
  }

  .product-card__add-to-cart {
    width: 100%;
    font-size: 14px;
    padding: 8px 16px;
  }

  .featured-collection__view-all {
    margin-top: var(--spacing-loose);
  }

  .featured-collection__placeholder {
    padding: var(--spacing-extra-loose) 0;
    text-align: center;
    background-color: #f7f7f7;
    border-radius: var(--border-radius);
  }

  .placeholder-message {
    font-size: 16px;
    color: var(--color-secondary);
  }

  @media screen and (max-width: 989px) {
    .featured-collection__grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media screen and (max-width: 749px) {
    .featured-collection__grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media screen and (max-width: 480px) {
    .featured-collection__grid {
      grid-template-columns: 1fr;
    }
  }
</style>

{% schema %}
{
  "name": "Featured Collection",
  "settings": [
    {
      "type": "header",
      "content": "Heading Style"
    },
    {
      "type": "text",
      "id": "heading_first_word",
      "label": "Heading First Word (Outlined)",
      "default": "Exclusive"
    },
    {
      "type": "text",
      "id": "heading_second_word",
      "label": "Heading Second Word (Filled)",
      "default": "Finds"
    },
    {
      "type": "color",
      "id": "heading_outline_color",
      "label": "Outline Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "heading_filled_color",
      "label": "Filled Text Color",
      "default": "#000000"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Alternative Heading (Legacy)",
      "default": "Featured Collection",
      "info": "Used if the outlined/filled heading words are not provided"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "range",
      "id": "products_limit",
      "min": 4,
      "max": 12,
      "step": 4,
      "label": "Number of products to show",
      "default": 4
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "label": "Show 'View all' button",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Featured Collection",
      "category": "Collection",
      "settings": {
        "heading_first_word": "Exclusive",
        "heading_second_word": "Finds",
        "heading_outline_color": "#000000",
        "heading_filled_color": "#000000",
        "collection": "",
        "products_limit": 4,
        "show_view_all": true
      }
    }
  ]
}
{% endschema %}
