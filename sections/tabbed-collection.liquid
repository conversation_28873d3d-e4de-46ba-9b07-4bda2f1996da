<div class="tabbed-collection" data-section-id="{{ section.id }}" data-section-type="tabbed-collection">
  <div class="page-width">
    {% comment %}
      The issue might be with how we're handling the collections setting.
      Let's try a different approach.
    {% endcomment %}

    {% comment %}
      Pass the collections parameter directly to the snippet.
      The snippet has been updated to handle CollectionListDrop type properly.
    {% endcomment %}

    {% comment %}
      Make sure we're passing the collections parameter correctly.
      The collection_list setting type returns a special object that needs to be handled properly.
    {% endcomment %}
    {% comment %}
      Extract collection handles from the collection_list setting type
      and pass them directly to the snippet
    {% endcomment %}
    {% assign collection_handles = '' %}
    {% if section.settings.collections.size > 0 %}
      {% for collection_item in section.settings.collections %}
        {% if collection_item.value != blank %}
          {% assign collection_handles = collection_handles | append: collection_item.value | append: ',' %}
        {% endif %}
      {% endfor %}
    {% else %}
      {% comment %}If no collections are selected, use all collections as a fallback{% endcomment %}
      {% for collection in collections limit: 5 %}
        {% if collection.products.size > 0 %}
          {% assign collection_handles = collection_handles | append: collection.handle | append: ',' %}
        {% endif %}
      {% endfor %}
    {% endif %}

    {% render 'tabbed-products',
      heading: section.settings.title,
      heading_first_word: section.settings.heading_first_word,
      heading_second_word: section.settings.heading_second_word,
      heading_outline_color: section.settings.heading_outline_color,
      heading_filled_color: section.settings.heading_filled_color,
      collection_handles: collection_handles,
      products_per_row: section.settings.products_per_row,
      products_per_row_tablet: section.settings.products_per_row_tablet,
      products_per_row_mobile: section.settings.products_per_row_mobile,
      products_limit: section.settings.products_limit,
      show_view_all: section.settings.show_view_all,
      unique_id: section.id,
      tab_text_color: section.settings.tab_text_color,
      tab_border_color: section.settings.tab_border_color,
      tab_hover_background: section.settings.tab_hover_background,
      tab_hover_text_color: section.settings.tab_hover_text_color,
      tab_active_background: section.settings.tab_active_background,
      tab_active_text_color: section.settings.tab_active_text_color
    %}
  </div>
</div>

{% schema %}
{
  "name": "Tabbed Collection",
  "settings": [
    {
      "type": "header",
      "content": "Heading Style"
    },
    {
      "type": "text",
      "id": "heading_first_word",
      "label": "Heading First Word (Outlined)",
      "default": "Shop"
    },
    {
      "type": "text",
      "id": "heading_second_word",
      "label": "Heading Second Word (Filled)",
      "default": "Categories"
    },
    {
      "type": "color",
      "id": "heading_outline_color",
      "label": "Outline Color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "heading_filled_color",
      "label": "Filled Text Color",
      "default": "#000000"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Alternative Heading (Legacy)",
      "default": "Shop by Category",
      "info": "Used if the outlined/filled heading words are not provided"
    },
    {
      "type": "collection_list",
      "id": "collections",
      "label": "Collections",
      "limit": 10,
      "info": "Each collection will be displayed as a tab"
    },
    {
      "type": "header",
      "content": "Tab Styles"
    },
    {
      "type": "color",
      "id": "tab_text_color",
      "label": "Tab text color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "tab_border_color",
      "label": "Tab border color",
      "default": "#e8e8e8"
    },
    {
      "type": "color",
      "id": "tab_hover_background",
      "label": "Tab hover background",
      "default": "#f5f5f5"
    },
    {
      "type": "color",
      "id": "tab_hover_text_color",
      "label": "Tab hover text color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "tab_active_background",
      "label": "Active tab background",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "tab_active_text_color",
      "label": "Active tab text color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Products"
    },
    {
      "type": "range",
      "id": "products_per_row",
      "min": 2,
      "max": 5,
      "step": 1,
      "label": "Products per row (desktop)",
      "default": 4
    },
    {
      "type": "range",
      "id": "products_per_row_tablet",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Products per row (tablet)",
      "default": 2
    },
    {
      "type": "range",
      "id": "products_per_row_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Products per row (mobile)",
      "default": 1
    },
    {
      "type": "range",
      "id": "products_limit",
      "min": 4,
      "max": 24,
      "step": 4,
      "label": "Maximum products per tab",
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "label": "Show 'View all' button",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Tabbed Collection",
      "category": "Collection",
      "settings": {
        "title": "Shop by Category",
        "products_per_row": 4,
        "products_limit": 8,
        "show_view_all": true
      }
    }
  ]
}
{% endschema %}
