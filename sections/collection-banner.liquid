{% comment %}
  Collection Banner

  A full-width banner for collection pages with a background image and text overlay.
{% endcomment %}

<div class="collection-banner" data-section-id="{{ section.id }}" data-section-type="collection-banner">
  <div class="collection-banner__wrapper">
    {% if section.settings.use_collection_image and collection.image %}
      {% assign banner_image = collection.image %}
    {% elsif section.settings.banner_image != blank %}
      {% assign banner_image = section.settings.banner_image %}
    {% endif %}

    {% if banner_image %}
      <div class="collection-banner__image-container">
        {%- assign img_url = banner_image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
        <img class="collection-banner__image lazyload"
             data-src="{{ img_url }}"
             data-widths="[360, 540, 720, 900, 1080, 1296, 1512, 1728, 1944, 2048]"
             data-aspectratio="{{ banner_image.aspect_ratio }}"
             data-sizes="auto"
             alt="{{ banner_image.alt | escape }}"
             width="{{ banner_image.width }}"
             height="{{ banner_image.height }}">
      </div>
    {% else %}
      <div class="collection-banner__placeholder">
        {{ 'collection-1' | placeholder_svg_tag: 'collection-banner__placeholder-svg' }}
      </div>
    {% endif %}

    {% if section.settings.show_tagline and section.settings.tagline != blank %}
      <div class="collection-banner__content-wrapper">
        <div class="page-width">
          <div class="collection-banner__content">
            <div class="collection-banner__tagline">
              {{ section.settings.tagline }}
            </div>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
</div>

<style>
  .collection-banner {
    margin-bottom: 0;
    position: relative;
  }

  .collection-banner__wrapper {
    position: relative;
    overflow: hidden;
  }

  .collection-banner__image-container {
    position: relative;
    height: 300px;
    width: 100%;
    overflow: hidden;
  }

  .collection-banner__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .collection-banner__placeholder {
    height: 300px;
    width: 100%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .collection-banner__placeholder-svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    opacity: 0.2;
  }

  .collection-banner__content-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
  }

  .collection-banner__content {
    text-align: center;
    padding: 20px;
  }

  .collection-banner__tagline {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto;
    color: #fff;
  }

  @media screen and (max-width: 749px) {
    .collection-banner__image-container {
      height: 200px;
    }

    .collection-banner__tagline {
      font-size: 16px;
    }
  }
</style>

{% schema %}
{
  "name": "Collection Banner",
  "settings": [
    {
      "type": "checkbox",
      "id": "use_collection_image",
      "label": "Use collection image",
      "default": true,
      "info": "If the collection has an image, it will be used as the banner background"
    },
    {
      "type": "image_picker",
      "id": "banner_image",
      "label": "Banner image",
      "info": "Used as a fallback if collection image is not available or if 'Use collection image' is unchecked"
    },
    {
      "type": "checkbox",
      "id": "show_tagline",
      "label": "Show tagline",
      "default": true
    },
    {
      "type": "text",
      "id": "tagline",
      "label": "Tagline",
      "default": "Find uncommon ground."
    }
  ],
  "presets": [
    {
      "name": "Collection Banner",
      "category": "Collection"
    }
  ]
}
{% endschema %}
