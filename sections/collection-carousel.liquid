<div class="collection-carousel"
  data-section-id="{{ section.id }}"
  data-section-type="collection-carousel"
  data-slides-per-view="{{ section.settings.collections_per_row }}"
  data-slides-per-view-tablet="{{ section.settings.collections_per_row_tablet }}"
  data-slides-per-view-mobile="{{ section.settings.collections_per_row_mobile }}"
  data-autoplay-speed="{{ section.settings.autoplay_speed }}"
  data-autoplay="{{ section.settings.autoplay }}">
  <div class="page-width">
    {% if section.settings.title != blank %}
      <div class="section-header text-center">
        <h2 class="section-title">{{ section.settings.title | escape }}</h2>
        {% if section.settings.description != blank %}
          <div class="section-description rte">{{ section.settings.description }}</div>
        {% endif %}
      </div>
    {% endif %}

    <div class="collection-carousel__wrapper">
      <div id="collection-carousel-{{ section.id }}" class="keen-slider collection-carousel__slider">
        {% for block in section.blocks %}
          {% if block.settings.collection != blank %}
            {% assign collection = collections[block.settings.collection] %}
            {% if collection != blank %}
              <div class="keen-slider__slide collection-carousel__slide" {{ block.shopify_attributes }}>
                <div class="collection-card">
                  <a href="{{ collection.url }}" class="collection-card__link">
                    <div class="collection-card__image-wrapper">
                      <div class="collection-card__image-placeholder"></div>
                      {% if collection.image != blank %}
                        <img
                          src="{{ collection.image | img_url: '600x600', crop: 'center' }}"
                          srcset="{{ collection.image | img_url: '600x600', crop: 'center' }} 1x, {{ collection.image | img_url: '1200x1200', crop: 'center' }} 2x"
                          alt="{{ collection.title | escape }}"
                          loading="lazy"
                          width="600"
                          height="600"
                          class="collection-card__image"
                          style="background-color: #f7f7f7;"
                        >
                      {% elsif collection.products.first.featured_media != blank %}
                        <img
                          src="{{ collection.products.first.featured_media | img_url: '600x600', crop: 'center' }}"
                          srcset="{{ collection.products.first.featured_media | img_url: '600x600', crop: 'center' }} 1x, {{ collection.products.first.featured_media | img_url: '1200x1200', crop: 'center' }} 2x"
                          alt="{{ collection.title | escape }}"
                          loading="lazy"
                          width="600"
                          height="600"
                          class="collection-card__image"
                          style="background-color: #f7f7f7;"
                        >
                      {% else %}
                        {{ 'collection-1' | placeholder_svg_tag: 'collection-card__image placeholder-svg' }}
                      {% endif %}
                    </div>
                    <div class="collection-card__info">
                      <h3 class="collection-card__title">{{ collection.title }}</h3>
                      {% if section.settings.show_product_count %}
                        <div class="collection-card__count">
                          {{ collection.products_count }} {{ 'collections.general.products_count' | t: count: collection.products_count }}
                        </div>
                      {% endif %}
                    </div>
                  </a>
                </div>
              </div>
            {% else %}
              <div class="keen-slider__slide collection-carousel__slide" {{ block.shopify_attributes }}>
                <div class="collection-card collection-card--placeholder">
                  <div class="collection-card__image-wrapper">
                    {{ 'collection-1' | placeholder_svg_tag: 'collection-card__image placeholder-svg' }}
                  </div>
                  <div class="collection-card__info">
                    <h3 class="collection-card__title">{{ 'collections.general.collection_not_exist' | t }}</h3>
                  </div>
                </div>
              </div>
            {% endif %}
          {% else %}
            <div class="keen-slider__slide collection-carousel__slide" {{ block.shopify_attributes }}>
              <div class="collection-card collection-card--placeholder">
                <div class="collection-card__image-wrapper">
                  {{ 'collection-1' | placeholder_svg_tag: 'collection-card__image placeholder-svg' }}
                </div>
                <div class="collection-card__info">
                  <h3 class="collection-card__title">{{ 'homepage.onboarding.no_collection_selected' | t }}</h3>
                </div>
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>

      {% if section.blocks.size > section.settings.collections_per_row %}
        <div class="collection-carousel__controls">
          <button type="button" class="collection-carousel__arrow collection-carousel__arrow--prev" data-carousel-prev aria-label="Previous collections">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M16.67 0l2.83 2.829-9.339 9.175 9.339 9.167-2.83 2.829-12.17-11.996z"/>
            </svg>
          </button>
          <button type="button" class="collection-carousel__arrow collection-carousel__arrow--next" data-carousel-next aria-label="Next collections">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon" viewBox="0 0 24 24">
              <path d="M7.33 24l-2.83-2.829 9.339-9.175-9.339-9.167 2.83-2.829 12.17 11.996z"/>
            </svg>
          </button>
        </div>
      {% endif %}

      {% if section.settings.show_dots %}
        <div class="collection-carousel__dots" data-carousel-dots></div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .collection-carousel {
    margin: var(--spacing-extra-loose) 0;
  }

  .collection-carousel__wrapper {
    position: relative;
    padding: 0 40px;
  }

  .collection-carousel__slider {
    position: relative;
    overflow: hidden;
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
  }

  .collection-carousel__slide {
    padding: var(--spacing-base);
    box-sizing: border-box;
  }

  .collection-card {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
  }

  .collection-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .collection-card__link {
    display: block;
    text-decoration: none;
    color: inherit;
  }

  .collection-card__image-wrapper {
    position: relative;
    padding-top: 100%;
    overflow: hidden;
    background-color: #f7f7f7;
  }

  .collection-card__image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f7f7f7;
  }

  .collection-card__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    will-change: transform; /* Optimize for animations */
  }

  .collection-card:hover .collection-card__image {
    transform: scale(1.05);
  }

  .collection-card__info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-base);
    background-color: rgba(255, 255, 255, 0.9);
    text-align: center;
  }

  .collection-card__title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }

  .collection-card__count {
    margin-top: 5px;
    font-size: 14px;
    color: var(--color-secondary);
  }

  .collection-card--placeholder .collection-card__image-wrapper {
    background-color: #f7f7f7;
  }

  .collection-carousel__controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
  }

  .collection-carousel__arrow {
    background-color: white;
    border: 1px solid var(--color-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    pointer-events: auto;
  }

  .collection-carousel__arrow:hover {
    background-color: var(--color-background-light);
  }

  .collection-carousel__arrow svg {
    width: 12px;
    height: 12px;
  }

  .collection-carousel__dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: var(--spacing-base);
  }

  .collection-carousel__dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--color-border);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .collection-carousel__dot--active {
    background-color: var(--color-primary);
  }

  @media screen and (max-width: 749px) {
    .collection-carousel__wrapper {
      padding: 0 30px;
    }

    .collection-carousel__arrow {
      width: 30px;
      height: 30px;
    }

    .collection-card__title {
      font-size: 16px;
    }
  }
</style>

<script>
  // Collection carousel will be initialized by carousel-init.js
  // All configuration is passed via data attributes on the carousel element

  // Add a direct initialization script as a fallback
  document.addEventListener('DOMContentLoaded', function() {
    // Wait for keen-slider.js to load
    setTimeout(function() {
      if (typeof KeenSlider === 'function') {
        const container = document.querySelector('[data-section-id="{{ section.id }}"]');
        if (container && !container.keenSlider) {
          // Get settings from data attributes
          const slidesPerView = parseInt(container.getAttribute('data-slides-per-view') || 3, 10);
          const slidesPerViewTablet = parseInt(container.getAttribute('data-slides-per-view-tablet') || 2, 10);
          const slidesPerViewMobile = parseInt(container.getAttribute('data-slides-per-view-mobile') || 1, 10);

          // Set slide widths directly
          const slides = container.querySelectorAll('.keen-slider__slide');
          const windowWidth = window.innerWidth;
          let currentSlidesPerView = slidesPerView;

          if (windowWidth < 750) {
            currentSlidesPerView = slidesPerViewMobile;
          } else if (windowWidth < 990) {
            currentSlidesPerView = slidesPerViewTablet;
          }

          const slideWidth = 100 / currentSlidesPerView;
          slides.forEach(slide => {
            slide.style.cssText = `min-width: ${slideWidth}% !important; max-width: ${slideWidth}% !important; width: ${slideWidth}% !important;`;
          });

          // Initialize carousel if not already initialized
          if (typeof window.initCarousels === 'function') {
            window.initCarousels('{{ section.id }}');
          }
        }
      }
    }, 500); // Wait for other scripts to load
  });
</script>

{% schema %}
{
  "name": "Collection Carousel",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Collection Carousel"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "range",
      "id": "collections_per_row",
      "min": 2,
      "max": 5,
      "step": 1,
      "label": "Collections per row (desktop)",
      "default": 3
    },
    {
      "type": "range",
      "id": "collections_per_row_tablet",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Collections per row (tablet)",
      "default": 2
    },
    {
      "type": "range",
      "id": "collections_per_row_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Collections per row (mobile)",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "show_product_count",
      "label": "Show product count",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "label": "Show dots",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 3000,
      "max": 8000,
      "step": 500,
      "unit": "ms",
      "label": "Autoplay speed",
      "default": 5000
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Collection Carousel",
      "category": "Collection",
      "blocks": [
        {
          "type": "collection"
        },
        {
          "type": "collection"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}
