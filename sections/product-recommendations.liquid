{%- if recommendations.performed and recommendations.products_count > 0 -%}
  <div class="product-recommendations" data-section-id="{{ section.id }}" data-section-type="product-recommendations">
    <div class="page-width">
      <div class="section-header text-center">
        <h2 class="product-recommendations__title">{{ section.settings.heading | escape }}</h2>
      </div>

      <ul class="product-recommendations__grid grid grid--uniform grid--view-items">
        {%- for product in recommendations.products -%}
          <li class="product-recommendations__grid-item grid__item">
            {% render 'product-card',
              product: product,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating
            %}
          </li>
        {%- endfor -%}
      </ul>
    </div>
  </div>
{%- endif -%}

<style>
  .product-recommendations {
    padding: var(--spacing-large) 0;
  }

  .product-recommendations__title {
    margin: 0 0 var(--spacing-large);
    font-size: var(--font-size-heading-base);
    text-align: center;
  }

  .product-recommendations__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-base);
    list-style: none;
    padding: 0;
    margin: 0;
  }

  @media screen and (min-width: 750px) {
    .product-recommendations__grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
</style>

{% schema %}
{
  "name": "Product recommendations",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "You may also like",
      "label": "Heading"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show product vendor",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "label": "Show product rating",
      "info": "To display a rating, add a product rating app",
      "default": false
    }
  ]
}
{% endschema %}

<script>
  class ProductRecommendations extends HTMLElement {
    constructor() {
      super();
    }

    connectedCallback() {
      const handleIntersection = (entries, observer) => {
        if (!entries[0].isIntersecting) return;
        observer.unobserve(this);

        fetch(this.dataset.url)
          .then(response => response.text())
          .then(text => {
            const html = document.createElement('div');
            html.innerHTML = text;
            const recommendations = html.querySelector('.product-recommendations');

            if (recommendations && recommendations.innerHTML.trim().length) {
              this.innerHTML = recommendations.innerHTML;
            }
          })
          .catch(e => {
            console.error(e);
          });
      };

      new IntersectionObserver(handleIntersection.bind(this), {rootMargin: '0px 0px 200px 0px'}).observe(this);
    }
  }

  customElements.define('product-recommendations', ProductRecommendations);

  document.addEventListener('DOMContentLoaded', function() {
    const productId = document.querySelector('.product-template').dataset.productId;
    const sectionId = document.querySelector('.product-recommendations').dataset.sectionId;
    const limit = 4;
    const recommendationsUrl = `${window.Shopify.routes.root}recommendations/products?product_id=${productId}&limit=${limit}&section_id=${sectionId}`;
    
    const recommendationsSection = document.querySelector('.product-recommendations');
    if (recommendationsSection) {
      recommendationsSection.dataset.url = recommendationsUrl;
    }
  });
</script>
