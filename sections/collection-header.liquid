<div class="collection-header" data-section-id="{{ section.id }}" data-section-type="collection-header">
  <div class="page-width">
    <div class="collection-header__inner">
      {% if section.settings.show_collection_image and collection.image %}
        <div class="collection-header__image-wrapper">
          <div class="collection-header__image">
            {%- assign img_url = collection.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
            <img class="lazyload"
                 data-src="{{ img_url }}"
                 data-widths="[360, 540, 720, 900, 1080, 1296, 1512, 1728, 1944, 2048]"
                 data-aspectratio="{{ collection.image.aspect_ratio }}"
                 data-sizes="auto"
                 alt="{{ collection.image.alt | escape }}"
                 width="{{ collection.image.width }}"
                 height="{{ collection.image.height }}"
                 style="max-width: {{ section.settings.image_width }}px">
          </div>
        </div>
      {% endif %}

      <div class="collection-header__content">
        <h1 class="collection-header__title">{{ collection.title }}</h1>
        
        {% if collection.description != blank and section.settings.show_collection_description %}
          <div class="collection-header__description rte">
            {{ collection.description }}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<style>
  .collection-header {
    margin-bottom: var(--spacing-large);
  }

  .collection-header__inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-large) 0;
  }

  .collection-header__image-wrapper {
    margin-bottom: var(--spacing-medium);
    width: 100%;
    max-width: {{ section.settings.image_width }}px;
  }

  .collection-header__image {
    position: relative;
    overflow: hidden;
    padding-bottom: {{ 100 | divided_by: collection.image.aspect_ratio }}%;
  }

  .collection-header__image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .collection-header__title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-heading-large);
  }

  .collection-header__description {
    max-width: 720px;
    margin: 0 auto;
  }

  @media screen and (min-width: 750px) {
    .collection-header__inner {
      padding: var(--spacing-extra-large) 0;
    }
  }
</style>

{% schema %}
{
  "name": "Collection header",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_collection_image",
      "label": "Show collection image",
      "default": true
    },
    {
      "type": "range",
      "id": "image_width",
      "min": 400,
      "max": 2000,
      "step": 100,
      "unit": "px",
      "label": "Image width",
      "default": 1000
    },
    {
      "type": "checkbox",
      "id": "show_collection_description",
      "label": "Show collection description",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Collection header",
      "category": "Collection"
    }
  ]
}
{% endschema %}
