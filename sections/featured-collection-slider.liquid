<div class="featured-collection-slider" data-section-id="{{ section.id }}" data-section-type="featured-collection-slider">
  <div class="page-width">
    {% if section.settings.title != blank %}
      <div class="section-header text-center">
        <h2 class="section-title">{{ section.settings.title | escape }}</h2>
        {% if section.settings.description != blank %}
          <div class="section-description rte">{{ section.settings.description }}</div>
        {% endif %}
      </div>
    {% endif %}

    {%- assign collection_handle = section.settings.collection | default: '' -%}
    {%- if collection_handle == blank -%}
      {%- assign collection = collections['445425778991'] -%}
    {%- else -%}
      {%- assign collection = collections[collection_handle] -%}
    {%- endif -%}

    {% if collection != blank and collection.products.size > 0 %}
      {% render 'product-slider',
        products: collection.products,
        title: '',
        slides_per_view: section.settings.products_per_row,
        slides_per_view_mobile: section.settings.products_per_row_mobile,
        slides_per_view_tablet: section.settings.products_per_row_tablet,
        show_dots: section.settings.show_dots,
        show_arrows: section.settings.show_arrows,
        autoplay: section.settings.autoplay,
        autoplay_speed: section.settings.autoplay_speed,
        slider_id: 'featured-collection-slider-' | append: section.id
      %}

      {% if section.settings.show_view_all and section.settings.collection != blank %}
        <div class="featured-collection-slider__view-all text-center">
          <a href="{{ collection.url }}" class="button">
            {{ 'collections.general.view_all' | t }}
          </a>
        </div>
      {% endif %}
    {% else %}
      <div class="featured-collection-slider__placeholder">
        <div class="placeholder-message">
          {% if collection == blank and section.settings.collection != blank %}
            {{ 'collections.general.collection_not_exist' | t }}
          {% elsif section.settings.collection == blank %}
            {{ 'homepage.onboarding.no_collection_selected' | t }}
          {% else %}
            {{ 'collections.general.no_matches' | t }}
          {% endif %}
        </div>
      </div>
    {% endif %}
  </div>
</div>

<style>
  .featured-collection-slider {
    margin: var(--spacing-extra-loose) 0;
  }

  .section-header {
    margin-bottom: var(--spacing-base);
  }

  .section-title {
    margin-bottom: var(--spacing-tight);
  }

  .section-description {
    max-width: 700px;
    margin: 0 auto;
  }

  .featured-collection-slider__view-all {
    margin-top: var(--spacing-loose);
  }

  .featured-collection-slider__placeholder {
    padding: var(--spacing-extra-loose) 0;
    text-align: center;
    background-color: #f7f7f7;
    border-radius: var(--border-radius);
  }

  .placeholder-message {
    font-size: 16px;
    color: var(--color-secondary);
  }
</style>

{% schema %}
{
  "name": "Product Slider",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Featured Collection"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "products_per_row",
      "min": 2,
      "max": 6,
      "step": 1,
      "label": "Products per row (desktop)",
      "default": 4
    },
    {
      "type": "range",
      "id": "products_per_row_tablet",
      "min": 1,
      "max": 4,
      "step": 1,
      "label": "Products per row (tablet)",
      "default": 2
    },
    {
      "type": "range",
      "id": "products_per_row_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Products per row (mobile)",
      "default": 1
    },
    {
      "type": "header",
      "content": "Slider Settings"
    },
    {
      "type": "checkbox",
      "id": "show_arrows",
      "label": "Show arrows",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "label": "Show dots",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay",
      "default": false
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 2000,
      "max": 8000,
      "step": 500,
      "label": "Autoplay speed (ms)",
      "default": 5000
    },
    {
      "type": "header",
      "content": "Additional Options"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "label": "Show 'View all' button",
      "default": true
    }
  ],
  "presets": [
    {
      "name": "Product Slider",
      "category": "Collection",
      "settings": {
        "collection": "",
        "products_per_row": 4,
        "show_view_all": true
      }
    }
  ]
}
{% endschema %}
