<footer class="site-footer" data-section-id="{{ section.id }}" data-section-type="footer">
  <div class="page-width">
    <div class="footer-wrapper">
      <div class="footer-block footer-block--menu">
        <h2 class="footer-block__title">{{ section.settings.menu_title }}</h2>
        {% if section.settings.footer_menu != blank %}
          <ul class="footer-nav">
            {% for link in linklists[section.settings.footer_menu].links %}
              <li><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endfor %}
          </ul>
        {% endif %}
      </div>

      {% if section.settings.secondary_menu != blank %}
        <div class="footer-block footer-block--menu">
          <h2 class="footer-block__title">{{ section.settings.secondary_menu_title }}</h2>
          <ul class="footer-nav">
            {% for link in linklists[section.settings.secondary_menu].links %}
              <li><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      <div class="footer-block footer-block--newsletter">
        <h2 class="footer-block__title">{{ section.settings.newsletter_title }}</h2>
        {% if section.settings.newsletter_enable %}
          <div class="footer-newsletter">
            {%- form 'customer', id: 'ContactFooter' -%}
              <input type="hidden" name="contact[tags]" value="newsletter">
              <div class="newsletter-form">
                <div class="field">
                  <input
                    id="NewsletterForm--{{ section.id }}"
                    type="email"
                    name="contact[email]"
                    class="field__input"
                    value="{{ form.email }}"
                    aria-required="true"
                    autocorrect="off"
                    autocapitalize="off"
                    autocomplete="email"
                    placeholder="{{ 'general.newsletter_form.email_placeholder' | t }}"
                    required
                  >
                  <label class="field__label" for="NewsletterForm--{{ section.id }}">
                    {{ 'general.newsletter_form.email_placeholder' | t }}
                  </label>
                </div>
                <button type="submit" class="button" name="commit">
                  {{ 'general.newsletter_form.submit' | t }}
                </button>
              </div>

              {%- if form.errors -%}
                <div class="form-message form-message--error">
                  {{ form.errors | default_errors }}
                </div>
              {%- endif -%}

              {%- if form.posted_successfully? -%}
                <div class="form-message form-message--success">
                  {{ 'general.newsletter_form.confirmation' | t }}
                </div>
              {%- endif -%}
            {%- endform -%}
          </div>
        {% endif %}

        {% if section.settings.show_social %}
          <div class="footer-social-wrapper">
            <h2 class="footer-block__title">{{ section.settings.social_title }}</h2>
            <ul class="footer-social">
              {% if settings.social_facebook_link != blank %}
                <li>
                  <a href="{{ settings.social_facebook_link }}" target="_blank" aria-label="Facebook">
                    <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-facebook" viewBox="0 0 24 24">
                      <path d="M22.675 0H1.325C.593 0 0 .593 0 1.325v21.351C0 23.407.593 24 1.325 24H12.82v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116c.73 0 1.323-.593 1.323-1.325V1.325C24 .593 23.407 0 22.675 0z"/>
                    </svg>
                  </a>
                </li>
              {% endif %}
              {% if settings.social_instagram_link != blank %}
                <li>
                  <a href="{{ settings.social_instagram_link }}" target="_blank" aria-label="Instagram">
                    <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-instagram" viewBox="0 0 24 24">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
                    </svg>
                  </a>
                </li>
              {% endif %}
              {% if settings.social_twitter_link != blank %}
                <li>
                  <a href="{{ settings.social_twitter_link }}" target="_blank" aria-label="Twitter">
                    <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-twitter" viewBox="0 0 24 24">
                      <path d="M23.954 4.569a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.691 8.094 4.066 6.13 1.64 3.161a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.061a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.937 4.937 0 004.604 3.417 9.868 9.868 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.054 0 13.999-7.496 13.999-13.986 0-.209 0-.42-.015-.63a9.936 9.936 0 002.46-2.548l-.047-.02z"/>
                    </svg>
                  </a>
                </li>
              {% endif %}
              {% if settings.social_linkedin_link != blank %}
                <li>
                  <a href="{{ settings.social_linkedin_link }}" target="_blank" aria-label="LinkedIn">
                    <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-linkedin" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </a>
                </li>
              {% endif %}
              {% if settings.social_youtube_link != blank %}
                <li>
                  <a href="{{ settings.social_youtube_link }}" target="_blank" aria-label="YouTube">
                    <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-youtube" viewBox="0 0 24 24">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                  </a>
                </li>
              {% endif %}
              {% if settings.social_snapchat_link != blank %}
                <li>
                  <a href="{{ settings.social_snapchat_link }}" target="_blank" aria-label="Snapchat">
                    <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-snapchat" viewBox="0 0 24 24">
                      <path d="M12.065 2c1.277 0 5.6.257 7.65 5.498.713 1.856.813 5.058.813 5.058s.037 5.221-4.025 7.42c-.36 0-1.013-.363-1.013-.363-1.785 0-3.1 1.157-5.425 1.157-2.324 0-3.64-1.157-5.424-1.157 0 0-.652.363-1.013.363-4.062-2.199-4.025-7.42-4.025-7.42s.1-3.202.813-5.058C2.465 2.257 6.788 2 8.065 2c.309 0 .617.01.926.03.309-.02.617-.03.926-.03h2.148zm-.065 1.294h-2.074c-.309 0-.617.01-.926.03-.309-.02-.617-.03-.926-.03-1.045 0-4.563.2-6.211 4.455-.652 1.69-.727 4.61-.727 4.675 0 .065-.037 4.375 3.298 6.11.326.175.909.35 1.04.35.435 0 1.812-.642 3.526-.642 1.85 0 2.901.642 4.026.642 1.122 0 2.173-.642 4.025-.642 1.713 0 3.09.642 3.526.642.13 0 .713-.175 1.04-.35 3.334-1.735 3.298-6.045 3.298-6.11 0-.065-.074-2.985-.727-4.675-1.648-4.255-5.166-4.455-6.211-4.455zm.13 3.757c.652 0 1.304.257 1.848.771.435.385.652.898.652 1.412 0 .257-.074.514-.217.706.037.065.074.13.11.2.074.13.148.257.148.386 0 .13-.074.257-.217.321-.074.065-.184.065-.258.065-.074 0-.184 0-.258-.065a.972.972 0 01-.326-.321 1.296 1.296 0 00-.11-.2 1.532 1.532 0 01-.87.257c-.184 0-.37-.065-.544-.13-.11.065-.258.13-.4.13-.109 0-.217-.065-.326-.13a1.354 1.354 0 01-.544.13c-.326 0-.617-.065-.87-.257a1.11 1.11 0 00-.11.2.972.972 0 01-.326.321c-.074.065-.184.065-.258.065-.074 0-.184 0-.258-.065-.143-.064-.217-.192-.217-.321 0-.13.074-.257.148-.386.037-.065.074-.13.11-.2a.906.906 0 01-.217-.706c0-.514.217-1.027.652-1.412a2.863 2.863 0 011.848-.771zm4.062 7.935c-.074 0-.184.065-.258.065-1.156.192-1.522.898-1.67 1.284-.11.321-.11.642-.11.706 0 .065 0 .13-.037.13-.037.065-.074.065-.148.065h-.037c-.11.065-.217.065-.326.065-.184 0-.37-.065-.544-.13-.326-.129-.652-.193-1.013-.193-.435 0-.87.064-1.304.257-.258.129-.544.193-.87.193-.326 0-.652-.064-.87-.193-.435-.193-.87-.257-1.304-.257-.37 0-.687.064-1.013.193-.184.065-.37.13-.544.13-.11 0-.217 0-.326-.065h-.037c-.074 0-.11 0-.148-.065-.037 0-.037-.065-.037-.13 0-.064 0-.385-.11-.706-.148-.386-.514-1.092-1.67-1.284-.074 0-.184-.065-.258-.065-.11 0-.217.065-.258.193-.037.129 0 .321.148.45.11.129.258.257.4.385.184.13.4.257.617.386.258.13.544.257.87.386.326.13.652.257 1.013.386.37.13.761.257 1.195.386.435.129.87.257 1.34.321.11.065.258.13.4.13.184 0 .326-.065.435-.193.11-.13.184-.257.258-.386.074-.13.11-.257.184-.321.037-.065.074-.065.11-.065.037 0 .074 0 .11.065.074.064.11.192.184.321.074.13.148.257.258.386.11.128.252.193.435.193.143 0 .29-.065.4-.13.47-.064.905-.192 1.34-.321.435-.129.826-.257 1.195-.386.37-.129.687-.257 1.013-.386.326-.129.613-.257.87-.386.217-.129.4-.257.617-.386.148-.128.29-.256.4-.385.148-.129.185-.321.148-.45-.04-.128-.147-.193-.258-.193z"/>
                    </svg>
                  </a>
                </li>
              {% endif %}
              {% if settings.social_pinterest_link != blank %}
                <li>
                  <a href="{{ settings.social_pinterest_link }}" target="_blank" aria-label="Pinterest">
                    <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-pinterest" viewBox="0 0 24 24">
                      <path d="M12 0C5.373 0 0 5.372 0 12c0 5.084 3.163 9.426 7.627 11.174-.105-.949-.2-2.405.042-3.441.218-.937 1.407-5.965 1.407-5.965s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.655 2.568-.994 3.995-.283 1.194.599 2.169 1.777 2.169 2.133 0 3.772-2.249 3.772-5.495 0-2.873-2.064-4.882-5.012-4.882-3.414 0-5.418 2.561-5.418 5.207 0 1.031.397 2.138.893 2.738a.36.36 0 01.083.345l-.333 1.36c-.053.22-.174.267-.402.161-1.499-.698-2.436-2.889-2.436-4.649 0-3.785 2.75-7.262 7.929-7.262 4.163 0 7.398 2.967 7.398 6.931 0 4.136-2.607 7.464-6.227 7.464-1.216 0-2.359-.631-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24 12 24c6.627 0 12-5.373 12-12 0-6.628-5.373-12-12-12z"/>
                    </svg>
                  </a>
                </li>
              {% endif %}
              {% if settings.social_tiktok_link != blank %}
                <li>
                  <a href="{{ settings.social_tiktok_link }}" target="_blank" aria-label="TikTok">
                    <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-tiktok" viewBox="0 0 24 24">
                      <path d="M19.589 6.686a4.793 4.793 0 01-3.77-4.245V2h-3.445v13.672a2.896 2.896 0 01-5.201 1.743l-.002-.001.002.001a2.895 2.895 0 013.183-4.51v-3.5a6.329 6.329 0 00-5.394 10.692 6.33 6.33 0 0010.857-4.424V8.687a8.182 8.182 0 004.773 1.526V6.79a4.831 4.831 0 01-1.003-.104z"/>
                    </svg>
                  </a>
                </li>
              {% endif %}
            </ul>
          </div>
        {% endif %}
      </div>
    </div>

    <div class="footer-bottom">
      <div class="copyright">
        &copy; {{ 'now' | date: "%Y" }} {{ shop.name }}. {{ section.settings.copyright_text }}
      </div>
      {% if section.settings.show_payment_icons %}
        <div class="payment-icons">
          {% for type in shop.enabled_payment_types %}
            {{ type | payment_type_svg_tag: class: 'icon icon--payment' }}
          {% endfor %}
        </div>
      {% endif %}
    </div>
  </div>
</footer>

{% schema %}
{
  "name": "Footer",
  "settings": [
    {
      "type": "header",
      "content": "Primary menu"
    },
    {
      "type": "text",
      "id": "menu_title",
      "label": "Menu title",
      "default": "Quick links"
    },
    {
      "type": "link_list",
      "id": "footer_menu",
      "label": "Menu",
      "default": "footer"
    },
    {
      "type": "header",
      "content": "Secondary menu"
    },
    {
      "type": "text",
      "id": "secondary_menu_title",
      "label": "Menu title",
      "default": "Information"
    },
    {
      "type": "link_list",
      "id": "secondary_menu",
      "label": "Menu"
    },
    {
      "type": "header",
      "content": "Newsletter"
    },
    {
      "type": "text",
      "id": "newsletter_title",
      "label": "Newsletter title",
      "default": "Subscribe to our newsletter"
    },
    {
      "type": "checkbox",
      "id": "newsletter_enable",
      "label": "Enable newsletter",
      "default": true
    },
    {
      "type": "header",
      "content": "Social media"
    },
    {
      "type": "checkbox",
      "id": "show_social",
      "label": "Show social icons",
      "default": true
    },
    {
      "type": "text",
      "id": "social_title",
      "label": "Social title",
      "default": "Follow us"
    },
    {
      "type": "header",
      "content": "Footer bottom"
    },
    {
      "type": "text",
      "id": "copyright_text",
      "label": "Copyright text",
      "default": "All rights reserved."
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "Show payment icons",
      "default": true
    }
  ]
}
{% endschema %}
