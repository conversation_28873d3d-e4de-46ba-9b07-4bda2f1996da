<div class="collection-template" data-section-id="{{ section.id }}" data-section-type="collection">
  <div class="page-width">
    <div class="collection-template__inner">
      {% comment %}Always show filters for now{% endcomment %}
      {% if true %}
        <div class="collection-template__filters-wrapper">
          <button class="collection-filters-mobile__toggle" aria-expanded="false" aria-controls="collection-filters">
            {{ 'collections.general.filter_by' | t }}
            <span class="collection-filters-mobile__toggle-icon">
              <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </span>
          </button>

          <div id="collection-filters" class="collection-filters">
            <form id="filter-form" class="collection-filters__form" action="{{ request.path }}" method="get">
              {% if collection.filters.size > 0 %}
                {% for filter in collection.filters %}
                  <div class="collection-filters__group">
                    <h3 class="collection-filters__heading">{{ filter.label }}</h3>

                    {% case filter.type %}
                      {% when 'boolean' or 'list' %}
                        {% if filter.label == 'Availability' or filter.label == 'Product type' or filter.label == 'Brand' %}
                          <ul class="collection-filters__checkbox-list">
                            {% for filter_value in filter.values %}
                              <li class="collection-filters__checkbox-item">
                                <label class="collection-filters__checkbox-label">
                                  <input type="checkbox"
                                         class="collection-filters__checkbox-input"
                                         name="{{ filter_value.param_name }}"
                                         value="{{ filter_value.value }}"
                                         {% if filter_value.active %}checked{% endif %}
                                         data-filter-update>
                                  <span class="collection-filters__checkbox-custom"></span>
                                  <span>{{ filter_value.label }}</span>
                                  <span class="collection-filters__checkbox-count">({{ filter_value.count }})</span>
                                </label>
                              </li>
                            {% endfor %}
                          </ul>
                        {% elsif filter.label == 'Color' %}
                          <ul class="collection-filters__color-list">
                            {% for filter_value in filter.values %}
                              <li class="collection-filters__color-item">
                                <label class="collection-filters__color-label" title="{{ filter_value.label }}">
                                  <input type="checkbox"
                                         class="collection-filters__color-input"
                                         name="{{ filter_value.param_name }}"
                                         value="{{ filter_value.value }}"
                                         {% if filter_value.active %}checked{% endif %}
                                         data-filter-update>
                                  <span class="collection-filters__color-swatch" style="background-color: {{ filter_value.label | handleize }}"></span>
                                </label>
                              </li>
                            {% endfor %}
                          </ul>
                        {% elsif filter.label == 'Size' %}
                          <ul class="collection-filters__size-list">
                            {% for filter_value in filter.values %}
                              <li class="collection-filters__size-item">
                                <label class="collection-filters__size-label">
                                  <input type="checkbox"
                                         class="collection-filters__size-input"
                                         name="{{ filter_value.param_name }}"
                                         value="{{ filter_value.value }}"
                                         {% if filter_value.active %}checked{% endif %}
                                         data-filter-update>
                                  <span class="collection-filters__size-button">{{ filter_value.label }}</span>
                                </label>
                              </li>
                            {% endfor %}
                          </ul>
                        {% else %}
                          <ul class="collection-filters__checkbox-list">
                            {% for filter_value in filter.values %}
                              <li class="collection-filters__checkbox-item">
                                <label class="collection-filters__checkbox-label">
                                  <input type="checkbox"
                                         class="collection-filters__checkbox-input"
                                         name="{{ filter_value.param_name }}"
                                         value="{{ filter_value.value }}"
                                         {% if filter_value.active %}checked{% endif %}
                                         data-filter-update>
                                  <span class="collection-filters__checkbox-custom"></span>
                                  <span>{{ filter_value.label }}</span>
                                  <span class="collection-filters__checkbox-count">({{ filter_value.count }})</span>
                                </label>
                              </li>
                            {% endfor %}
                          </ul>
                        {% endif %}
                      {% when 'price_range' %}
                        <div class="collection-filters__price-range">
                          {% comment %}Find the highest price among products{% endcomment %}
                          {% assign highest_price = 0 %}
                          {% for product in collection.products %}
                            {% if product.price > highest_price %}
                              {% assign highest_price = product.price %}
                            {% endif %}
                          {% endfor %}
                          {% assign highest_price_dollars = highest_price | divided_by: 100.0 | ceil %}
                          {% if highest_price_dollars < 10 %}{% assign highest_price_dollars = 100 %}{% endif %}

                          {% comment %}Handle URL parameters for price filtering{% endcomment %}
                          {% assign url_min_price = 0 %}
                          {% assign url_max_price = highest_price_dollars %}

                          {% if filter.min_value.value != blank %}
                            {% assign url_min_price = filter.min_value.value %}
                          {% endif %}

                          {% if filter.max_value.value != blank %}
                            {% assign url_max_price = filter.max_value.value %}
                            {% comment %}Fix for URL parameter issue where price is multiplied by 100{% endcomment %}
                            {% if url_max_price > highest_price_dollars %}
                              {% assign ten_times_highest = highest_price_dollars | times: 10 %}
                              {% if url_max_price > ten_times_highest %}
                                {% assign url_max_price = url_max_price | divided_by: 100.0 %}
                              {% endif %}
                            {% endif %}
                          {% endif %}

                          <!-- Debug: Min: {{ url_min_price }}, Max: {{ url_max_price }} (Highest: {{ highest_price_dollars }}) -->
                          <div class="collection-filters__price-slider"
                               data-min="0"
                               data-max="{{ highest_price_dollars }}"
                               data-current-min="{{ url_min_price }}"
                               data-current-max="{{ url_max_price }}"
                               data-currency="{{ cart.currency.symbol }}">
                            <div class="collection-filters__price-progress"></div>
                            <div class="collection-filters__price-handle collection-filters__price-handle--min" data-handle="min"></div>
                            <div class="collection-filters__price-handle collection-filters__price-handle--max" data-handle="max"></div>
                          </div>

                          <div class="collection-filters__price-display">
                            <span class="collection-filters__price-display-min">{{ cart.currency.symbol }}{{ url_min_price }}.00</span>
                            -
                            <span class="collection-filters__price-display-max">{{ cart.currency.symbol }}{{ url_max_price }}.00</span>
                          </div>

                          <div class="collection-filters__price-inputs">
                            <div class="collection-filters__price-field">
                              <label for="filter-min-price">{{ 'collections.general.from' | t }}</label>
                              <input type="number"
                                     id="filter-min-price"
                                     name="{{ filter.min_value.param_name }}"
                                     value="{{ url_min_price }}"
                                     min="0"
                                     max="{{ highest_price_dollars }}"
                                     placeholder="0"
                                     data-filter-update
                                     data-input="min">
                            </div>
                            <div class="collection-filters__price-field">
                              <label for="filter-max-price">{{ 'collections.general.to' | t }}</label>
                              <input type="number"
                                     id="filter-max-price"
                                     name="{{ filter.max_value.param_name }}"
                                     value="{{ url_max_price }}"
                                     min="0"
                                     max="{{ highest_price_dollars }}"
                                     placeholder="{{ highest_price_dollars }}"
                                     data-filter-update
                                     data-input="max">
                            </div>
                          </div>
                        </div>
                    {% endcase %}
                  </div>
                {% endfor %}
              {% else %}
                <!-- Fallback for older Shopify versions or if filters are not available -->
                <!-- Availability -->
                <div class="collection-filters__group">
                  <h3 class="collection-filters__heading">Availability</h3>
                  <ul class="collection-filters__checkbox-list">
                    <li class="collection-filters__checkbox-item">
                      <label class="collection-filters__checkbox-label">
                        <input type="checkbox" class="collection-filters__checkbox-input" name="filter.v.availability" value="1">
                        <span class="collection-filters__checkbox-custom"></span>
                        <span>In stock</span>
                        <span class="collection-filters__checkbox-count">({{ collection.products.size }})</span>
                      </label>
                    </li>
                    <li class="collection-filters__checkbox-item">
                      <label class="collection-filters__checkbox-label">
                        <input type="checkbox" class="collection-filters__checkbox-input" name="filter.v.availability" value="0">
                        <span class="collection-filters__checkbox-custom"></span>
                        <span>Out of stock</span>
                        <span class="collection-filters__checkbox-count">(0)</span>
                      </label>
                    </li>
                  </ul>
                </div>

                <!-- Color -->
                <div class="collection-filters__group">
                  <h3 class="collection-filters__heading">Color</h3>
                  <ul class="collection-filters__color-list">
                    {% assign color_list = 'black,white,gray,blue,green,red,yellow,purple,brown,navy' | split: ',' %}
                    {% for color in color_list %}
                      <li class="collection-filters__color-item">
                        <label class="collection-filters__color-label" title="{{ color | capitalize }}">
                          <input type="checkbox" class="collection-filters__color-input" name="filter.v.option.color" value="{{ color }}">
                          <span class="collection-filters__color-swatch" style="background-color: {{ color }}"></span>
                        </label>
                      </li>
                    {% endfor %}
                  </ul>
                </div>

                <!-- Size -->
                <div class="collection-filters__group">
                  <h3 class="collection-filters__heading">Size</h3>
                  <ul class="collection-filters__size-list">
                    {% assign size_list = 'S,M,L,XL,28,32,38,40' | split: ',' %}
                    {% for size in size_list %}
                      <li class="collection-filters__size-item">
                        <label class="collection-filters__size-label">
                          <input type="checkbox" class="collection-filters__size-input" name="filter.v.option.size" value="{{ size }}">
                          <span class="collection-filters__size-button">{{ size }}</span>
                        </label>
                      </li>
                    {% endfor %}
                  </ul>
                </div>

                <!-- Product type -->
                <div class="collection-filters__group">
                  <h3 class="collection-filters__heading">Product type</h3>
                  <ul class="collection-filters__checkbox-list">
                    <li class="collection-filters__checkbox-item">
                      <label class="collection-filters__checkbox-label">
                        <input type="checkbox" class="collection-filters__checkbox-input" name="filter.p.product_type" value="clothing">
                        <span class="collection-filters__checkbox-custom"></span>
                        <span>clothing</span>
                        <span class="collection-filters__checkbox-count">({{ collection.products.size }})</span>
                      </label>
                    </li>
                  </ul>
                </div>

                <!-- Brand -->
                <div class="collection-filters__group">
                  <h3 class="collection-filters__heading">Brand</h3>
                  <ul class="collection-filters__checkbox-list">
                    <li class="collection-filters__checkbox-item">
                      <label class="collection-filters__checkbox-label">
                        <input type="checkbox" class="collection-filters__checkbox-input" name="filter.p.vendor" value="theking-castle">
                        <span class="collection-filters__checkbox-custom"></span>
                        <span>theking-castle</span>
                        <span class="collection-filters__checkbox-count">({{ collection.products.size }})</span>
                      </label>
                    </li>
                  </ul>
                </div>
              {% endif %}
            </form>

            {% if collection.filters.size > 0 and collection.all_tags.size > 0 %}
              <div class="collection-filters__active">
                <h3 class="collection-filters__heading">{{ 'collections.general.active_filters' | t }}</h3>
                <ul class="collection-filters__active-list">
                  {% for filter in collection.filters %}
                    {% for active_value in filter.active_values %}
                      <li class="collection-filters__active-item">
                        <a href="{{ active_value.url_to_remove }}" class="collection-filters__active-remove">
                          <span>{{ filter.label }}: {{ active_value.label }}</span>
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 1L11 11M1 11L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                          </svg>
                        </a>
                      </li>
                    {% endfor %}
                  {% endfor %}
                </ul>

                {% if collection.filters.size > 0 and collection.all_tags.size > 0 %}
                  <a href="{{ collection.url }}?sort_by={{ collection.sort_by }}" class="collection-filters__clear-all">
                    {{ 'collections.general.clear_all' | t }}
                  </a>
                {% endif %}
              </div>
            {% endif %}
          </div>
        </div>
      {% endif %}

      <div class="collection-template__products">
        {% if section.settings.enable_sorting %}
          <div class="collection-template__sort">
            <label for="SortBy" class="collection-template__sort-label">{{ 'collections.general.sort_by' | t }}</label>
            <div class="collection-template__sort-wrapper">
              <select id="SortBy" class="collection-template__sort-select" aria-describedby="a11y-refresh-page-message">
                <option value="manual" {% if collection.sort_by == 'manual' %}selected="selected"{% endif %}>{{ 'collections.general.featured' | t }}</option>
                <option value="best-selling" {% if collection.sort_by == 'best-selling' %}selected="selected"{% endif %}>{{ 'collections.general.best_selling' | t }}</option>
                <option value="title-ascending" {% if collection.sort_by == 'title-ascending' %}selected="selected"{% endif %}>{{ 'collections.general.alphabetically_a_to_z' | t }}</option>
                <option value="title-descending" {% if collection.sort_by == 'title-descending' %}selected="selected"{% endif %}>{{ 'collections.general.alphabetically_z_to_a' | t }}</option>
                <option value="price-ascending" {% if collection.sort_by == 'price-ascending' %}selected="selected"{% endif %}>{{ 'collections.general.price_low_to_high' | t }}</option>
                <option value="price-descending" {% if collection.sort_by == 'price-descending' %}selected="selected"{% endif %}>{{ 'collections.general.price_high_to_low' | t }}</option>
                <option value="created-descending" {% if collection.sort_by == 'created-descending' %}selected="selected"{% endif %}>{{ 'collections.general.date_new_to_old' | t }}</option>
                <option value="created-ascending" {% if collection.sort_by == 'created-ascending' %}selected="selected"{% endif %}>{{ 'collections.general.date_old_to_new' | t }}</option>
              </select>
            </div>
          </div>
        {% endif %}

        <div class="collection-template__product-count">
          {% if collection.products.size > 0 %}
            <p>{{ 'collections.general.products_count' | t: count: collection.products.size }}</p>
          {% endif %}
        </div>

        <div class="collection-template__product-grid" style="--products-per-row: {{ section.settings.products_per_row }};">
          {% if collection.products.size > 0 %}
            {% for product in collection.products %}
              <div class="collection-template__product-item">
                {% render 'product-card',
                  product: product,
                  show_vendor: section.settings.show_vendor,
                  show_rating: section.settings.show_rating
                %}
              </div>
            {% endfor %}
          {% else %}
            <div class="collection-template__empty">
              <p>{{ 'collections.general.no_matches' | t }}</p>
            </div>
          {% endif %}
        </div>

        {% if paginate.pages > 1 %}
          <div class="collection-template__pagination">
            {% render 'pagination', paginate: paginate %}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<style>
  .collection-template__inner {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-large);
  }

  .collection-template__product-grid {
    display: grid;
    grid-template-columns: repeat(var(--products-per-row), 1fr);
    gap: var(--spacing-base);
  }

  .collection-template__sort {
    margin-bottom: var(--spacing-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-small);
  }

  .collection-template__sort-label {
    font-size: var(--font-size-base);
    margin-right: var(--spacing-small);
    white-space: nowrap;
  }

  .collection-template__sort-wrapper {
    position: relative;
    min-width: 200px;
  }

  .collection-template__sort-select {
    width: 100%;
    padding: var(--spacing-small);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    background-color: var(--color-background);
    font-size: var(--font-size-base);
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 36px;
  }

  .collection-template__product-count {
    margin-bottom: var(--spacing-medium);
  }

  .collection-template__empty {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-large);
  }

  .collection-template__pagination {
    margin-top: var(--spacing-large);
  }

  /* Filters styling */
  .collection-filters {
    background-color: #f8f8f8;
    padding: 20px;
    border-radius: 8px;
  }

  .collection-filters__group {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
  }

  .collection-filters__group:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .collection-filters__heading {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
  }

  .collection-filters__checkbox-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .collection-filters__checkbox-item {
    margin-bottom: 10px;
  }

  .collection-filters__checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
  }

  .collection-filters__checkbox-input {
    position: absolute;
    opacity: 0;
  }

  .collection-filters__checkbox-custom {
    width: 16px;
    height: 16px;
    border: 1px solid #ccc;
    border-radius: 3px;
    margin-right: 10px;
    position: relative;
  }

  .collection-filters__checkbox-input:checked + .collection-filters__checkbox-custom::after {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 8px;
    height: 8px;
    background-color: #000;
    border-radius: 1px;
  }

  .collection-filters__checkbox-count {
    margin-left: 5px;
    color: #999;
  }

  /* Mobile filters */
  .collection-filters-mobile__toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10px 15px;
    background-color: #f8f8f8;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-bottom: 20px;
  }

  .collection-filters-mobile__toggle-icon {
    transition: transform 0.3s ease;
  }

  .collection-filters-mobile__toggle[aria-expanded="true"] .collection-filters-mobile__toggle-icon {
    transform: rotate(180deg);
  }

  @media screen and (min-width: 990px) {
    .collection-template__inner {
      grid-template-columns: 250px 1fr;
    }

    .collection-template__product-grid {
      --products-per-row: 3;
    }

    .collection-filters-mobile__toggle {
      display: none;
    }

    .collection-filters {
      display: block !important;
    }
  }

  @media screen and (min-width: 750px) and (max-width: 989px) {
    .collection-template__inner {
      grid-template-columns: 220px 1fr;
    }

    .collection-template__product-grid {
      --products-per-row: 2;
    }

    .collection-filters-mobile__toggle {
      display: none;
    }

    .collection-filters {
      display: block !important;
    }
  }

  @media screen and (max-width: 749px) {
    .collection-template__inner {
      grid-template-columns: 1fr;
    }

    .collection-template__product-grid {
      --products-per-row: 2;
    }

    .collection-filters {
      display: none;
    }

    .collection-filters.is-open {
      display: block;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Sort by functionality
    var sortSelect = document.getElementById('SortBy');
    if (sortSelect) {
      // Set the current sort value on page load
      var currentSort = '{{ collection.sort_by | default: collection.default_sort_by }}';
      if (currentSort && sortSelect.querySelector('option[value="' + currentSort + '"]')) {
        sortSelect.value = currentSort;
      }

      sortSelect.addEventListener('change', function() {
        var url = new URL(window.location.href);
        url.searchParams.set('sort_by', this.value);

        // If we're in the Shopify Theme Editor, don't redirect
        if (window.Shopify && window.Shopify.designMode) {
          console.log('Sort change prevented in Theme Editor. Would navigate to:', url.href);
          return;
        }

        window.location.href = url.href;
      });
    }

    // Mobile filter toggle
    var filterToggle = document.querySelector('.collection-filters-mobile__toggle');
    var filterContainer = document.getElementById('collection-filters');

    if (filterToggle && filterContainer) {
      filterToggle.addEventListener('click', function() {
        var isExpanded = filterToggle.getAttribute('aria-expanded') === 'true';
        filterToggle.setAttribute('aria-expanded', !isExpanded);

        if (isExpanded) {
          filterContainer.classList.remove('is-open');
        } else {
          filterContainer.classList.add('is-open');
        }
      });
    }

    // Filter update functionality
    var filterInputs = document.querySelectorAll('[data-filter-update]');
    filterInputs.forEach(function(input) {
      input.addEventListener('change', function() {
        var form = input.closest('form');
        if (form) {
          // If we're in the Shopify Theme Editor, don't submit
          if (window.Shopify && window.Shopify.designMode) {
            console.log('Filter update prevented in Theme Editor');
            return;
          }

          form.submit();
        }
      });
    });

    // Price range slider functionality
    initPriceRangeSliders();

    function initPriceRangeSliders() {
      const priceSliders = document.querySelectorAll('.collection-filters__price-slider');

      priceSliders.forEach(function(slider) {
        const minHandle = slider.querySelector('.collection-filters__price-handle--min');
        const maxHandle = slider.querySelector('.collection-filters__price-handle--max');
        const progress = slider.querySelector('.collection-filters__price-progress');
        const minInput = slider.closest('.collection-filters__price-range').querySelector('[data-input="min"]');
        const maxInput = slider.closest('.collection-filters__price-range').querySelector('[data-input="max"]');
        const minDisplay = slider.closest('.collection-filters__price-range').querySelector('.collection-filters__price-display-min');
        const maxDisplay = slider.closest('.collection-filters__price-range').querySelector('.collection-filters__price-display-max');

        // Get min and max values from data attributes
        const minValue = 0;
        const maxValue = parseInt(slider.getAttribute('data-max')) || 100;
        const currency = slider.getAttribute('data-currency') || '$';

        // Get current values from data attributes (these are already corrected for URL parameter issues)
        const currentMinFromData = parseFloat(slider.getAttribute('data-current-min')) || 0;
        const currentMaxFromData = parseFloat(slider.getAttribute('data-current-max')) || maxValue;

        // Set initial values from inputs or data attributes
        let currentMinValue = parseFloat(minInput.value);
        let currentMaxValue = parseFloat(maxInput.value);

        // If no values are set in inputs, use the data attribute values
        if (isNaN(currentMinValue)) {
          currentMinValue = currentMinFromData;
          minInput.value = currentMinFromData;
        }

        if (isNaN(currentMaxValue)) {
          currentMaxValue = currentMaxFromData;
          maxInput.value = currentMaxFromData;
        }

        // Ensure max value is not greater than the highest price
        if (currentMaxValue > maxValue) {
          currentMaxValue = maxValue;
          maxInput.value = maxValue;
        }

        // Update display values immediately
        updateDisplayValues();

        // Initialize slider positions
        updateSliderPositions();

        // Handle dragging
        let isDragging = false;
        let activeHandle = null;

        // Mouse events for handles
        [minHandle, maxHandle].forEach(handle => {
          handle.addEventListener('mousedown', startDrag);
          handle.addEventListener('touchstart', startDrag, { passive: false });
        });

        document.addEventListener('mousemove', drag);
        document.addEventListener('touchmove', drag, { passive: false });
        document.addEventListener('mouseup', endDrag);
        document.addEventListener('touchend', endDrag);

        // Input change events
        minInput.addEventListener('change', function() {
          currentMinValue = Math.max(minValue, Math.min(currentMaxValue, parseFloat(this.value) || minValue));
          this.value = currentMinValue;
          updateSliderPositions();
          updateDisplayValues();
        });

        maxInput.addEventListener('change', function() {
          currentMaxValue = Math.max(currentMinValue, Math.min(maxValue, parseFloat(this.value) || maxValue));
          this.value = currentMaxValue;
          updateSliderPositions();
          updateDisplayValues();
        });

        function startDrag(e) {
          e.preventDefault();
          isDragging = true;
          activeHandle = e.target;

          // Add dragging class
          activeHandle.classList.add('dragging');

          // Capture initial position
          const initialX = e.clientX || (e.touches && e.touches[0].clientX);
          activeHandle.setAttribute('data-start-x', initialX);

          // Prevent text selection during drag
          document.body.style.userSelect = 'none';
        }

        function drag(e) {
          if (!isDragging || !activeHandle) return;

          e.preventDefault();

          const sliderRect = slider.getBoundingClientRect();
          const sliderWidth = sliderRect.width;
          const x = e.clientX || (e.touches && e.touches[0].clientX);

          // Calculate position as percentage of slider width
          let percentage = Math.max(0, Math.min(100, ((x - sliderRect.left) / sliderWidth) * 100));

          // Convert percentage to value
          const value = minValue + ((maxValue - minValue) * percentage / 100);

          // Update the appropriate value based on which handle is active
          if (activeHandle === minHandle) {
            // Ensure min value stays within bounds and doesn't exceed max value
            currentMinValue = Math.min(currentMaxValue, Math.max(minValue, value));
            minInput.value = Math.round(currentMinValue);
          } else if (activeHandle === maxHandle) {
            // Ensure max value stays within bounds and doesn't go below min value
            currentMaxValue = Math.max(currentMinValue, Math.min(maxValue, value));
            maxInput.value = Math.round(currentMaxValue);
          }

          updateSliderPositions();
          updateDisplayValues();
        }

        function endDrag() {
          if (!isDragging) return;

          isDragging = false;

          if (activeHandle) {
            activeHandle.classList.remove('dragging');
            activeHandle = null;

            // Trigger change event on inputs to update the filter
            if (minInput.value) {
              minInput.dispatchEvent(new Event('change', { bubbles: true }));
            }

            if (maxInput.value) {
              maxInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
          }

          // Restore text selection
          document.body.style.userSelect = '';
        }

        function updateSliderPositions() {
          // Calculate percentages - ensure we don't divide by zero
          const range = maxValue - minValue;
          const minPercentage = range > 0 ? ((currentMinValue - minValue) / range) * 100 : 0;
          const maxPercentage = range > 0 ? ((currentMaxValue - minValue) / range) * 100 : 100;

          // Ensure percentages are within bounds
          const safeMinPercentage = Math.max(0, Math.min(100, minPercentage));
          const safeMaxPercentage = Math.max(0, Math.min(100, maxPercentage));

          // Update handle positions
          minHandle.style.left = safeMinPercentage + '%';
          maxHandle.style.left = safeMaxPercentage + '%';

          // Update progress bar
          progress.style.left = safeMinPercentage + '%';
          progress.style.width = (safeMaxPercentage - safeMinPercentage) + '%';
        }

        function updateDisplayValues() {
          // Format currency values
          minDisplay.textContent = formatMoney(currentMinValue, currency);
          maxDisplay.textContent = formatMoney(currentMaxValue, currency);
        }

        function formatMoney(amount, currency) {
          return currency + amount.toFixed(2);
        }
      });
    }

    // Color swatch background color mapping
    var colorSwatches = document.querySelectorAll('.collection-filters__color-swatch');
    var colorMap = {
      'black': '#000000',
      'white': '#ffffff',
      'gray': '#808080',
      'grey': '#808080',
      'red': '#ff0000',
      'blue': '#0000ff',
      'green': '#008000',
      'yellow': '#ffff00',
      'purple': '#800080',
      'pink': '#ffc0cb',
      'orange': '#ffa500',
      'brown': '#a52a2a',
      'navy': '#000080',
      'beige': '#f5f5dc',
      'tan': '#d2b48c',
      'olive': '#808000',
      'maroon': '#800000',
      'teal': '#008080',
      'lavender': '#e6e6fa',
      'coral': '#ff7f50',
      'turquoise': '#40e0d0',
      'mint': '#98fb98',
      'khaki': '#f0e68c',
      'silver': '#c0c0c0',
      'gold': '#ffd700',
      'cream': '#fffdd0'
    };

    colorSwatches.forEach(function(swatch) {
      var bgColor = swatch.style.backgroundColor;
      if (!bgColor || bgColor === 'transparent') {
        var colorName = swatch.closest('label').getAttribute('title').toLowerCase();

        // Try to match the color name to our map
        for (var color in colorMap) {
          if (colorName.includes(color)) {
            swatch.style.backgroundColor = colorMap[color];
            break;
          }
        }
      }
    });
  });
</script>

{% schema %}
{
  "name": "Collection page",
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "label": "Enable filtering",
      "default": true,
      "info": "Filters will only appear if your collection has more than one product"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "label": "Enable sorting",
      "default": true
    },
    {
      "type": "range",
      "id": "products_per_row",
      "min": 2,
      "max": 5,
      "step": 1,
      "label": "Products per row",
      "default": 4
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show product vendor",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "label": "Show product rating",
      "default": false,
      "info": "To display a rating, add a product rating app"
    }
  ],
  "presets": [
    {
      "name": "Collection page",
      "category": "Collection"
    }
  ]
}
{% endschema %}