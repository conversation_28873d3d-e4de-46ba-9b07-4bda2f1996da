<style>
  .product-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-large);
  }

  /* Product Gallery Styles */
  .product-gallery-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .product-gallery__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4px;
  }

  .product-gallery__item {
    position: relative;
    cursor: pointer;
  }

  .product-gallery__item img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    aspect-ratio: 1/1.3;
  }

  .product-gallery__item--last {
    position: relative;
  }

  .product-gallery__view-all {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
  }

  /* Product Details Styles */
  .product-details {
    padding-left: 20px;
  }

  .product-title {
    margin: 0 0 10px;
    font-size: 24px;
    font-weight: 600;
    line-height: 1.2;
  }

  .product-vendor {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
  }

  .product-rating {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .product-rating__stars {
    color: #000;
    margin-right: 5px;
    font-weight: 600;
  }

  .product-rating__count {
    color: #666;
    text-decoration: underline;
    cursor: pointer;
  }

  .product-price {
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
  }

  .product-price span {
    /* Default to no text decoration */
    text-decoration: none;
  }

  /* Override for compare price */
  .product-price .price-item--compare {
    text-decoration: line-through !important;
  }

  .price-item--regular {
    font-weight: 600;
    text-decoration: none;
  }

  .price-item--compare {
    text-decoration: line-through !important;
    color: #666;
    margin-left: 10px;
    font-weight: normal;
    position: relative;
  }

  /* Additional strikethrough effect */
  .price-item--compare::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 1px;
    background-color: #666;
  }

  .price-item--sale {
    color: #000000;
    font-weight: 600;
    text-decoration: none;
  }

  .discount-badge {
    display: inline-block;
    background-color: #4CAF50;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  /* Color Swatches */
  .product-form__option {
    margin-bottom: 20px;
  }

  .product-form__option-label {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .product-form__option-value {
    color: #666;
    font-weight: normal;
    text-transform: none;
    letter-spacing: normal;
    margin-left: 5px;
  }

  .color-swatch-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
  }

  .color-swatch {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .color-swatch.active {
    box-shadow: 0 0 0 2px #fff, 0 0 0 3px #000;
  }

  .color-swatch__checkmark {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
  }

  .color-swatch[style*="background-color: black"] .color-swatch__checkmark,
  .color-swatch[style*="background-color: #000"] .color-swatch__checkmark {
    background-color: rgba(255, 255, 255, 0.3);
  }

  /* Size Selection */
  .size-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .size-option {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    padding: 0 10px;
  }

  .size-option.active {
    border-color: #000;
    background-color: #000;
    color: #fff;
  }

  .size-option.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    text-decoration: line-through;
  }

  /* Quantity Selector */
  .product-form__quantity {
    margin-bottom: 20px;
  }

  .quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    width: fit-content;
  }

  .quantity-button {
    background: #f5f5f5;
    border: none;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
  }

  .quantity-input {
    width: 50px;
    border: none;
    text-align: center;
    padding: 10px 0;
    font-size: 16px;
    -moz-appearance: textfield;
  }

  .quantity-input::-webkit-outer-spin-button,
  .quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Add to Cart Button */
  .product-form__buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
  }

  .product-form__submit {
    flex: 1;
    padding: 15px;
    background-color: #000;
    color: #fff;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .product-form__submit:hover {
    background-color: #333;
  }

  .product-form__submit:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .wishlist-button {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e0e0e0;
    border-radius: 50%;
    background: #fff;
    cursor: pointer;
  }

  /* Shipping Info */
  .shipping-info {
    margin: 20px 0;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
  }

  .shipping-info__title {
    font-weight: 600;
    margin-bottom: 5px;
  }

  .shipping-info__text {
    font-size: 14px;
    color: #666;
  }

  /* Product Accordion */
  .product-accordion {
    margin-top: 30px;
    border-top: 1px solid #e0e0e0;
  }

  .accordion-item {
    border-bottom: 1px solid #e0e0e0;
  }

  .accordion-header {
    padding: 15px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .accordion-title {
    font-weight: 600;
    font-size: 16px;
  }

  .accordion-icon {
    width: 20px;
    height: 20px;
    position: relative;
  }

  .accordion-icon:before,
  .accordion-icon:after {
    content: '';
    position: absolute;
    background-color: #000;
    transition: transform 0.3s ease;
  }

  .accordion-icon:before {
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    transform: translateY(-50%);
  }

  .accordion-icon:after {
    top: 0;
    left: 50%;
    width: 2px;
    height: 100%;
    transform: translateX(-50%);
  }

  .accordion-item.active .accordion-icon:after {
    transform: translateX(-50%) rotate(90deg);
  }

  .accordion-content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
  }

  .accordion-item.active .accordion-content {
    padding: 0 0 15px;
    max-height: 1000px;
  }



  .page-width {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-medium);
  }

  @media screen and (min-width: 750px) {
    .product-container {
      grid-template-columns: 60% 40%;
      gap: var(--spacing-large);
    }
  }

  /* Similar Products Section Styles */
  .similar-products-section {
    margin: 60px 0;
  }

  .section-title {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    font-weight: 600;
  }

  .similar-products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  .similar-product-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .similar-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .similar-product-link {
    text-decoration: none;
    color: inherit;
    display: block;
  }

  .similar-product-image-container {
    position: relative;
    padding-bottom: 100%;
    overflow: hidden;
    background-color: #f5f5f5;
  }

  .similar-product-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
  }

  .similar-product-card:hover .similar-product-image {
    transform: scale(1.05);
  }

  .similar-product-discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #4CAF50;
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
  }

  .similar-product-info {
    padding: 15px;
  }

  .similar-product-title {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .similar-product-price {
    font-size: 16px;
  }

  .similar-price-item--regular {
    font-weight: 600;
  }

  .similar-price-item--sale {
    font-weight: 600;
    color: #000000;
  }

  .similar-price-item--compare {
    text-decoration: line-through;
    color: #666;
    margin-left: 8px;
    font-size: 14px;
  }

  @media screen and (max-width: 990px) {
    .similar-products-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media screen and (max-width: 749px) {
    .similar-products-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media screen and (max-width: 480px) {
    .similar-products-grid {
      grid-template-columns: 1fr;
    }
  }

  /* Cart Drawer Styles */
  .cart-drawer {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    max-width: 100%;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
  }

  .cart-drawer.active {
    right: 0;
  }

  .cart-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .cart-drawer-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  .cart-drawer__header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .cart-drawer__title {
    margin: 0;
    font-size: 20px;
  }

  .cart-drawer__close {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: #333;
  }

  .cart-drawer__content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
  }

  .cart-drawer__empty-state {
    text-align: center;
    padding: 40px 0;
  }

  .cart-drawer__empty-state p {
    margin-bottom: 20px;
    color: #666;
  }

  .cart-drawer__items {
    display: none;
  }

  .cart-drawer__item {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
  }

  .cart-drawer__item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-right: 15px;
  }

  .cart-drawer__item-details {
    flex: 1;
  }

  .cart-drawer__item-title {
    margin: 0 0 5px;
    font-size: 16px;
    font-weight: 500;
  }

  .cart-drawer__item-variant {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
  }

  .cart-drawer__item-price {
    font-weight: 600;
  }

  .cart-drawer__item-quantity {
    display: flex;
    align-items: center;
    margin-top: 10px;
  }

  .cart-drawer__item-quantity-button {
    width: 24px;
    height: 24px;
    background-color: #f5f5f5;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .cart-drawer__item-quantity-input {
    width: 40px;
    height: 24px;
    text-align: center;
    border: 1px solid #e0e0e0;
    margin: 0 5px;
  }

  .cart-drawer__item-remove {
    background: none;
    border: none;
    color: #999;
    text-decoration: underline;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    margin-top: 10px;
  }

  .cart-drawer__footer {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
  }

  .cart-drawer__subtotal {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .cart-drawer__buttons {
    display: flex;
    gap: 10px;
  }

  .cart-drawer__buttons .button {
    flex: 1;
    text-align: center;
  }

  .button--secondary {
    background-color: #f5f5f5;
    color: #333;
  }

  @media screen and (max-width: 480px) {
    .cart-drawer {
      width: 100%;
    }
  }

  /* Checkout Modal Styles */
  .checkout-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .checkout-modal.active {
    opacity: 1;
    visibility: visible;
  }

  .checkout-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .checkout-modal-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  .checkout-modal__content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 30px;
    position: relative;
    z-index: 1002;
    display: flex;
    flex-direction: column;
  }

  .checkout-modal__main {
    display: flex;
    flex-direction: row;
    gap: 30px;
    width: 100%;
  }

  .checkout-modal__left {
    flex: 1;
    width: calc(100% - 380px);
  }

  .checkout-modal__right {
    width: 350px;
    flex-shrink: 0;
    position: sticky;
    top: 30px;
    align-self: flex-start;
    max-height: calc(90vh - 60px);
    overflow-y: auto;
    padding-left: 30px;
    border-left: 1px solid #eee;
  }

  .checkout-modal__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
  }

  .checkout-modal__title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
  }

  .checkout-modal__close {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: #333;
  }

  .checkout-modal__logo {
    text-align: center;
    margin-bottom: 20px;
  }

  .checkout-modal__logo img {
    border-radius: 50%;
  }

  .checkout-modal__progress-bar {
    margin-bottom: 20px;
    padding: 0 20px;
  }

  .checkout-modal__progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .checkout-modal__progress-step {
    position: relative;
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #ccc;
    transition: color 0.3s ease;
  }

  .checkout-modal__progress-step.active {
    color: #000;
    font-weight: 600;
  }

  .checkout-modal__progress-step.completed {
    color: #00A651;
  }

  .checkout-modal__progress-line {
    position: relative;
    height: 1px;
    background-color: #e0e0e0;
    margin-top: 5px;
  }

  .checkout-modal__progress-line-active {
    position: absolute;
    top: 0;
    left: 0;
    height: 1px;
    background-color: #00A651;
    width: 0%; /* Will be updated by JS based on current step */
    transition: width 0.3s ease;
  }

  .checkout-modal__discount-banner {
    background-color: #e8f5e9;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .checkout-modal__discount-icon {
    margin-right: 10px;
    display: flex;
    align-items: center;
  }

  .checkout-modal__discount-text {
    color: #00A651;
    font-size: 14px;
  }

  .checkout-modal__steps {
    margin-bottom: 30px;
  }

  .checkout-modal__step {
    display: none;
  }

  .checkout-modal__step.active {
    display: block;
  }

  .checkout-modal__step-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px;
    color: #333;
  }

  /* Order Summary Section */
  .checkout-modal__order-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  .checkout-modal__order-icon {
    margin-right: 10px;
  }

  .checkout-modal__order-title {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
  }

  .checkout-modal__order-toggle {
    cursor: pointer;
  }

  .checkout-modal__order-price {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .checkout-modal__price-original {
    text-decoration: line-through;
    color: #999;
    margin-right: 10px;
    font-size: 16px;
  }

  .checkout-modal__price-current {
    font-size: 24px;
    font-weight: 600;
  }

  /* Loyalty Section */
  .checkout-modal__loyalty-section {
    margin-bottom: 30px;
  }

  .checkout-modal__loyalty-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
  }

  .checkout-modal__savings-banner {
    background-color: #00A651;
    color: white;
    padding: 10px 15px;
    text-align: center;
    border-radius: 4px;
    margin-bottom: 15px;
    font-weight: 500;
  }

  .checkout-modal__coupon-applied {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 10px 15px;
    border-radius: 4px;
  }

  .checkout-modal__coupon-icon {
    margin-right: 10px;
  }

  .checkout-modal__coupon-code {
    font-weight: 600;
    margin-right: 10px;
  }

  .checkout-modal__coupon-status {
    color: #00A651;
    font-weight: 500;
    margin-left: auto;
  }

  /* Mobile Section */
  .checkout-modal__mobile-section {
    margin-top: 30px;
  }

  .checkout-modal__mobile-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .checkout-modal__mobile-input-container {
    display: flex;
    border: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .checkout-modal__country-code {
    padding: 12px 15px;
    background-color: #f5f5f5;
    border-right: 1px solid #ddd;
    font-weight: 500;
    display: flex;
    align-items: center;
  }

  .checkout-modal__mobile-input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    font-size: 16px;
    outline: none;
  }

  .checkout-modal__notification-option {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .checkout-modal__checkbox {
    margin-right: 10px;
    width: 20px;
    height: 20px;
  }

  .checkout-modal__checkbox-label {
    font-size: 14px;
    color: #333;
  }

  .checkout-modal__shipping-methods,
  .checkout-modal__payment-methods {
    margin-bottom: 30px;
  }

  .checkout-modal__shipping-method,
  .checkout-modal__payment-method {
    margin-bottom: 15px;
  }

  .checkout-modal__shipping-method input[type="radio"],
  .checkout-modal__payment-method input[type="radio"] {
    display: none;
  }

  .checkout-modal__shipping-label,
  .checkout-modal__payment-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: border-color 0.3s ease, background-color 0.3s ease;
  }

  .checkout-modal__shipping-method input[type="radio"]:checked + .checkout-modal__shipping-label,
  .checkout-modal__payment-method input[type="radio"]:checked + .checkout-modal__payment-label {
    border-color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.05);
  }

  .checkout-modal__shipping-info {
    flex: 1;
  }

  .checkout-modal__shipping-name,
  .checkout-modal__payment-name {
    font-weight: 500;
    margin-bottom: 5px;
  }

  .checkout-modal__shipping-description {
    font-size: 14px;
    color: #666;
  }

  .checkout-modal__shipping-price {
    font-weight: 600;
  }

  .checkout-modal__payment-icon {
    margin-right: 15px;
    color: #333;
  }

  .checkout-modal__payment-label {
    justify-content: flex-start;
  }

  .checkout-modal__card-form,
  .checkout-modal__paypal-form,
  .checkout-modal__apple-form {
    margin-bottom: 30px;
  }

  .checkout-modal__paypal-message,
  .checkout-modal__apple-message {
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 4px;
    text-align: center;
    color: #666;
  }

  .checkout-modal__cart-summary {
    margin-bottom: 20px;
  }

  .checkout-modal__cart-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .checkout-modal__cart-title-count {
    font-weight: 500;
  }

  .checkout-modal__cart-items {
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
  }

  .checkout-modal__cart-item {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
  }

  .checkout-modal__cart-item:last-child {
    border-bottom: none;
  }

  .checkout-modal__cart-image {
    width: 70px;
    height: 70px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 15px;
  }

  .checkout-modal__cart-details {
    flex: 1;
  }

  .checkout-modal__cart-name {
    font-weight: 500;
    margin: 0 0 5px;
    font-size: 14px;
  }

  .checkout-modal__cart-variant {
    color: #666;
    font-size: 12px;
    margin-bottom: 5px;
  }

  .checkout-modal__cart-quantity {
    font-size: 12px;
    color: #666;
  }

  .checkout-modal__cart-price {
    font-weight: 600;
    font-size: 14px;
    text-align: right;
  }

  .checkout-modal__order-summary {
    margin-top: 30px;
  }

  .checkout-modal__order-summary-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  .checkout-modal__summary-row,
  .checkout-modal__order-summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
  }

  .checkout-modal__summary-row--total,
  .checkout-modal__order-summary-row.total {
    font-weight: 600;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
  }

  .checkout-modal__free-shipping,
  .checkout-modal__free-text {
    color: #4CAF50;
    font-weight: 500;
  }

  .checkout-modal__points-row {
    display: flex;
    align-items: center;
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
    font-size: 14px;
  }

  .checkout-modal__points-icon {
    margin-right: 10px;
  }

  .checkout-modal__points-highlight {
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 2px;
    font-weight: 600;
    margin: 0 5px;
  }

  .checkout-modal__chat-support {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #666;
  }

  .checkout-modal__chat-link {
    color: #333;
    text-decoration: underline;
    font-weight: 500;
  }

  .checkout-modal__footer {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
    width: 100%;
    background-color: #fff;
    z-index: 10;
  }

  .checkout-modal__back-button {
    background-color: transparent;
    border: 1px solid #e0e0e0;
    color: #333;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  .checkout-modal__back-button:hover {
    background-color: #f5f5f5;
  }

  .checkout-modal__continue-button,
  .checkout-modal__submit-button {
    background-color: #666;
    border: none;
    color: #fff;
    padding: 15px 30px;
    border-radius: 0;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .checkout-modal__continue-button svg {
    margin-left: 10px;
  }

  .checkout-modal__continue-button:hover,
  .checkout-modal__submit-button:hover {
    background-color: #555;
  }

  .checkout-modal__continue-button:focus,
  .checkout-modal__submit-button:focus {
    outline: none;
  }

  .checkout-modal__footer {
    padding: 0;
    border-top: none;
  }

  @media screen and (max-width: 768px) {
    .checkout-modal__content {
      padding: 20px;
      max-width: 90%;
    }

    .checkout-modal__main {
      flex-direction: column;
      gap: 20px;
    }

    .checkout-modal__left {
      width: 100%;
    }

    .checkout-modal__right {
      width: 100%;
      margin-top: 30px;
      padding-left: 0;
      border-left: none;
      border-top: 1px solid #eee;
      padding-top: 20px;
    }

    .checkout-modal__form-row {
      flex-direction: column;
      gap: 0;
    }

    .checkout-modal__progress-label {
      font-size: 12px;
    }
  }

  /* Checkout Success Styles */
  .checkout-modal__success {
    text-align: center;
    padding: 30px 0;
  }

  .checkout-modal__success svg {
    margin-bottom: 20px;
  }

  .checkout-modal__success h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #4CAF50;
  }

  .checkout-modal__success p {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
  }

  @media screen and (max-width: 480px) {
    .checkout-modal__content {
      padding: 15px;
      max-width: 95%;
      max-height: 95vh;
    }

    .checkout-modal__title {
      font-size: 20px;
    }

    .checkout-modal__progress-indicator {
      width: 24px;
      height: 24px;
      font-size: 12px;
    }

    .checkout-modal__progress-label {
      font-size: 10px;
    }

    .checkout-modal__continue-button,
    .checkout-modal__submit-button,
    .checkout-modal__back-button {
      padding: 12px 20px;
      font-size: 14px;
      min-width: auto;
      width: 100%;
      margin-top: 10px;
    }

    .checkout-modal__footer {
      flex-direction: column;
    }

    .checkout-modal__back-button {
      order: 2;
    }

    .checkout-modal__continue-button,
    .checkout-modal__submit-button {
      order: 1;
    }

    .checkout-modal__right {
      position: relative;
      top: 0;
      max-height: none;
      padding-left: 0;
      border-left: none;
      width: 100%;
    }

    .checkout-modal__success h2 {
      font-size: 20px;
    }

    .checkout-modal__success p {
      font-size: 14px;
    }
  }
</style>

<div class="product-template" data-section-id="{{ section.id }}" data-section-type="product" data-product-id="{{ product.id }}">
  <div class="page-width">
    <div class="product-container">
      <div class="product-media">
        {% if product.media.size > 0 %}
          <div class="product-gallery-container">
            <div class="product-gallery__grid">
              {% for media in product.media limit: 8 %}
                <div class="product-gallery__item" data-media-id="{{ media.id }}">
                  {% case media.media_type %}
                    {% when 'image' %}
                      <img src="{{ media | img_url: '600x800', crop: 'center' }}"
                           alt="{{ media.alt | escape }}"
                           loading="lazy"
                           width="600"
                           height="800"
                           data-product-media>
                    {% when 'external_video' %}
                      {{ media | external_video_tag }}
                    {% when 'video' %}
                      {{ media | video_tag: controls: true }}
                    {% when 'model' %}
                      {{ media | model_viewer_tag }}
                  {% endcase %}
                </div>
              {% endfor %}

              {% if product.media.size > 8 %}
                <div class="product-gallery__item product-gallery__item--last">
                  <img src="{{ product.media[8] | img_url: '600x800', crop: 'center' }}"
                       alt="{{ product.media[8].alt | escape }}"
                       loading="lazy"
                       width="600"
                       height="800">
                  <div class="product-gallery__view-all">
                    +{{ product.media.size | minus: 8 }} more
                  </div>
                </div>
              {% endif %}
            </div>
          </div>
        {% endif %}
      </div>

      <div class="product-details">
        <h1 class="product-title">{{ product.title }}</h1>

        {% if product.vendor != blank and section.settings.show_vendor %}
          <div class="product-vendor">
            {{ product.vendor }}
          </div>
        {% endif %}

        {% if section.settings.show_product_rating %}
          <div class="product-rating">
            {% if product.metafields.reviews.rating %}
              <div class="product-rating__stars">
                {{ product.metafields.reviews.rating | round: 1 }} ★★★★★
              </div>
              <div class="product-rating__count">
                {{ product.metafields.reviews.rating_count }} reviews
              </div>
            {% else %}
              <div class="product-rating__stars">
                4.8 ★★★★★
              </div>
              <div class="product-rating__count">
                165 reviews
              </div>
            {% endif %}
          </div>
        {% endif %}

        {% if product.compare_at_price and product.compare_at_price > product.price %}
          {% assign discount_amount = product.compare_at_price | minus: product.price %}
          {% assign discount_percentage = discount_amount | times: 100 | divided_by: product.compare_at_price | round %}
          <div class="discount-badge">
            Save {{ discount_percentage }}%
          </div>
        {% endif %}
        <div class="product-price">
          {% comment %}Only show compare at price if it's higher than the regular price{% endcomment %}
          {% if product.compare_at_price and product.compare_at_price > product.price %}
            <span class="price-item price-item--sale" data-regular-price>
              {{ product.price | money }}
            </span>
            <span class="price-item price-item--compare" style="text-decoration: line-through !important;">
              {{ product.compare_at_price | money }}
            </span>
          {% else %}
            <span class="price-item price-item--regular" data-regular-price style="text-decoration: none !important;">
              {{ product.price | money }}
            </span>
          {% endif %}
        </div>

        <script type="application/json" id="ProductVariants">
          {{ product.variants | json }}
        </script>

        {% form 'product', product, id: 'product-form', class: 'product-form', data-ajax-cart-form: '' %}
          <input type="hidden" name="id" id="variant-id" value="{{ product.selected_or_first_available_variant.id }}">

          {% if product.has_only_default_variant == false %}
            <div class="product-form__variants">
              {% for option in product.options_with_values %}
                <div class="product-form__option">
                  <div class="product-form__option-label">
                    {{ option.name }}: <span class="product-form__option-value">
                      {% if option.selected_value %}
                        {{ option.selected_value }}
                      {% else %}
                        Select {{ option.name | downcase }}
                      {% endif %}
                    </span>
                  </div>

                  {% if option.name == 'Color' %}
                    <div class="color-swatch-list" data-option-index="{{ forloop.index0 }}">
                      {% for value in option.values %}
                        {% assign color_handle = value | handle %}
                        <div class="color-swatch{% if option.selected_value == value %} active{% endif %}"
                             data-value="{{ value | escape }}"
                             style="background-color: {{ color_handle }};"
                             title="{{ value }}">
                          {% if option.selected_value == value %}
                            <span class="color-swatch__checkmark">
                              <svg width="10" height="8" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 4L4 7L11 1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              </svg>
                            </span>
                          {% endif %}
                        </div>
                      {% endfor %}
                    </div>
                  {% elsif option.name == 'Size' %}
                    <div class="size-list" data-option-index="{{ forloop.index0 }}">
                      {% for value in option.values %}
                        <div class="size-option{% if option.selected_value == value %} active{% endif %}"
                             data-value="{{ value | escape }}">
                          {{ value }}
                        </div>
                      {% endfor %}
                    </div>
                  {% else %}
                    <select
                      id="Option-{{ section.id }}-{{ forloop.index0 }}"
                      class="product-form__select"
                      name="options[{{ option.name | escape }}]"
                      data-option-index="{{ forloop.index0 }}"
                    >
                      {% for value in option.values %}
                        <option
                          value="{{ value | escape }}"
                          {% if option.selected_value == value %}selected="selected"{% endif %}
                        >
                          {{ value }}
                        </option>
                      {% endfor %}
                    </select>
                  {% endif %}
                </div>
              {% endfor %}
            </div>
          {% endif %}

          <div class="product-form__quantity">
            <div class="product-form__option-label">
              <span>Quantity</span>
            </div>
            <div class="quantity-selector">
              <button type="button" class="quantity-button quantity-button--decrease"
                      aria-label="{{ 'products.product.quantity.decrease' | t: product: product.title }}"
                      data-quantity-button="decrease">
                -
              </button>
              <input
                type="number"
                id="Quantity-{{ section.id }}"
                name="quantity"
                value="1"
                min="1"
                aria-label="{{ 'products.product.quantity.input_label' | t: product: product.title }}"
                class="quantity-input"
                data-quantity-input
              >
              <button type="button" class="quantity-button quantity-button--increase"
                      aria-label="{{ 'products.product.quantity.increase' | t: product: product.title }}"
                      data-quantity-button="increase">
                +
              </button>
            </div>
          </div>

          {% if section.settings.show_shipping_info %}
            <div class="shipping-info">
              <div class="shipping-info__title">{{ section.settings.shipping_info_title }}</div>
              <div class="shipping-info__text">{{ section.settings.shipping_info_text }}</div>
            </div>
          {% endif %}

          <div class="product-form__buttons">
            <button
              type="submit"
              name="add"
              class="product-form__submit"
              {% if product.available == false %}disabled{% endif %}
            >
              {% if product.available %}
                {{ section.settings.add_to_cart_text }}
              {% else %}
                {{ section.settings.sold_out_text }}
              {% endif %}
            </button>
            {% if section.settings.show_wishlist_button %}
              <button type="button" class="wishlist-button" aria-label="Add to wishlist">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" stroke="currentColor" stroke-width="2" fill="none"/>
                </svg>
              </button>
            {% endif %}
          </div>
        {% endform %}

        <div class="product-accordion">
          {% if section.settings.show_details_tab %}
            <div class="accordion-item">
              <div class="accordion-header" data-accordion-toggle>
                <div class="accordion-title">Details</div>
                <div class="accordion-icon"></div>
              </div>
              <div class="accordion-content">
                {% if product.description != blank %}
                  <div class="product-description rte">
                    {{ product.description }}
                  </div>
                {% endif %}
              </div>
            </div>
          {% endif %}

          {% if section.settings.show_features_tab %}
            <div class="accordion-item">
              <div class="accordion-header" data-accordion-toggle>
                <div class="accordion-title">Features</div>
                <div class="accordion-icon"></div>
              </div>
              <div class="accordion-content">
                <div class="rte">
                  {{ section.settings.features_content }}
                </div>
              </div>
            </div>
          {% endif %}

          {% if section.settings.show_shipping_returns_tab %}
            <div class="accordion-item">
              <div class="accordion-header" data-accordion-toggle>
                <div class="accordion-title">Shipping & Returns</div>
                <div class="accordion-icon"></div>
              </div>
              <div class="accordion-content">
                <div class="rte">
                  {{ section.settings.shipping_returns_content }}
                </div>
              </div>
            </div>
          {% endif %}
        </div>


      </div>
    </div>
  </div>

  <!-- Similar Products Section -->
  <div class="similar-products-section">
    <div class="page-width">
      <h2 class="section-title">You May Also Like</h2>

      <div class="similar-products-grid">
        {% assign current_product = product %}
        {% assign current_collection = product.collections.first %}
        {% assign same_collection_count = 0 %}
        {% assign different_collection_count = 0 %}

        {% comment %}First try to get products from the same collection{% endcomment %}
        {% if current_collection != blank %}
          {% for collection_product in current_collection.products limit: 10 %}
            {% if collection_product.id != current_product.id and same_collection_count < 2 %}
              <div class="similar-product-card">
                <a href="{{ collection_product.url | within: current_collection }}" class="similar-product-link">
                  <div class="similar-product-image-container">
                    <img src="{{ collection_product.featured_image | img_url: '300x300', crop: 'center' }}"
                         alt="{{ collection_product.title | escape }}"
                         class="similar-product-image"
                         loading="lazy">
                    {% if collection_product.compare_at_price > collection_product.price %}
                      {% assign discount_amount = collection_product.compare_at_price | minus: collection_product.price %}
                      {% assign discount_percentage = discount_amount | times: 100 | divided_by: collection_product.compare_at_price | round %}
                      <div class="similar-product-discount-badge">
                        Save {{ discount_percentage }}%
                      </div>
                    {% endif %}
                  </div>
                  <div class="similar-product-info">
                    <h3 class="similar-product-title">{{ collection_product.title }}</h3>
                    <div class="similar-product-price">
                      {% if collection_product.compare_at_price > collection_product.price %}
                        <span class="similar-price-item similar-price-item--sale">
                          {{ collection_product.price | money }}
                        </span>
                        <span class="similar-price-item similar-price-item--compare">
                          {{ collection_product.compare_at_price | money }}
                        </span>
                      {% else %}
                        <span class="similar-price-item similar-price-item--regular">
                          {{ collection_product.price | money }}
                        </span>
                      {% endif %}
                    </div>
                  </div>
                </a>
              </div>
              {% assign same_collection_count = same_collection_count | plus: 1 %}
            {% endif %}
            {% if same_collection_count >= 2 %}
              {% break %}
            {% endif %}
          {% endfor %}
        {% endif %}

        {% comment %}Then get products from different collections{% endcomment %}
        {% for collection in collections limit: 10 %}
          {% if collection.handle != current_collection.handle %}
            {% for diff_product in collection.products limit: 5 %}
              {% if diff_product.id != current_product.id and different_collection_count < 2 %}
                <div class="similar-product-card">
                  <a href="{{ diff_product.url }}" class="similar-product-link">
                    <div class="similar-product-image-container">
                      <img src="{{ diff_product.featured_image | img_url: '300x300', crop: 'center' }}"
                           alt="{{ diff_product.title | escape }}"
                           class="similar-product-image"
                           loading="lazy">
                      {% if diff_product.compare_at_price > diff_product.price %}
                        {% assign discount_amount = diff_product.compare_at_price | minus: diff_product.price %}
                        {% assign discount_percentage = discount_amount | times: 100 | divided_by: diff_product.compare_at_price | round %}
                        <div class="similar-product-discount-badge">
                          Save {{ discount_percentage }}%
                        </div>
                      {% endif %}
                    </div>
                    <div class="similar-product-info">
                      <h3 class="similar-product-title">{{ diff_product.title }}</h3>
                      <div class="similar-product-price">
                        {% if diff_product.compare_at_price > diff_product.price %}
                          <span class="similar-price-item similar-price-item--sale">
                            {{ diff_product.price | money }}
                          </span>
                          <span class="similar-price-item similar-price-item--compare">
                            {{ diff_product.compare_at_price | money }}
                          </span>
                        {% else %}
                          <span class="similar-price-item similar-price-item--regular">
                            {{ diff_product.price | money }}
                          </span>
                        {% endif %}
                      </div>
                    </div>
                  </a>
                </div>
                {% assign different_collection_count = different_collection_count | plus: 1 %}
              {% endif %}
              {% if different_collection_count >= 2 %}
                {% break %}
              {% endif %}
            {% endfor %}
          {% endif %}
          {% if different_collection_count >= 2 %}
            {% break %}
          {% endif %}
        {% endfor %}

        {% comment %}If we don't have 4 products yet, fill with fallback products{% endcomment %}
        {% assign total_products = same_collection_count | plus: different_collection_count %}
        {% if total_products < 4 %}
          {% assign needed_products = 4 | minus: total_products %}
          {% assign fallback_count = 0 %}

          {% for fallback_product in collections.all.products limit: 20 %}
            {% if fallback_product.id != current_product.id and fallback_count < needed_products %}
              <div class="similar-product-card">
                <a href="{{ fallback_product.url }}" class="similar-product-link">
                  <div class="similar-product-image-container">
                    <img src="{{ fallback_product.featured_image | img_url: '300x300', crop: 'center' }}"
                         alt="{{ fallback_product.title | escape }}"
                         class="similar-product-image"
                         loading="lazy">
                    {% if fallback_product.compare_at_price > fallback_product.price %}
                      {% assign discount_amount = fallback_product.compare_at_price | minus: fallback_product.price %}
                      {% assign discount_percentage = discount_amount | times: 100 | divided_by: fallback_product.compare_at_price | round %}
                      <div class="similar-product-discount-badge">
                        Save {{ discount_percentage }}%
                      </div>
                    {% endif %}
                  </div>
                  <div class="similar-product-info">
                    <h3 class="similar-product-title">{{ fallback_product.title }}</h3>
                    <div class="similar-product-price">
                      {% if fallback_product.compare_at_price > fallback_product.price %}
                        <span class="similar-price-item similar-price-item--sale">
                          {{ fallback_product.price | money }}
                        </span>
                        <span class="similar-price-item similar-price-item--compare">
                          {{ fallback_product.compare_at_price | money }}
                        </span>
                      {% else %}
                        <span class="similar-price-item similar-price-item--regular">
                          {{ fallback_product.price | money }}
                        </span>
                      {% endif %}
                    </div>
                  </div>
                </a>
              </div>
              {% assign fallback_count = fallback_count | plus: 1 %}
            {% endif %}
            {% if fallback_count >= needed_products %}
              {% break %}
            {% endif %}
          {% endfor %}
        {% endif %}

        {% comment %}If we still don't have products, show placeholders{% endcomment %}
        {% assign total_products = same_collection_count | plus: different_collection_count | plus: fallback_count %}
        {% if total_products == 0 %}
          <div class="similar-product-card">
            <div class="similar-product-image-container">
              <img src="https://dummyimage.com/300x300/cccccc/ffffff&text=Product+1"
                   alt="Product 1"
                   class="similar-product-image"
                   loading="lazy">
            </div>
            <div class="similar-product-info">
              <h3 class="similar-product-title">Product 1</h3>
              <div class="similar-product-price">
                <span class="similar-price-item similar-price-item--regular">
                  $79.99
                </span>
              </div>
            </div>
          </div>

          <div class="similar-product-card">
            <div class="similar-product-image-container">
              <img src="https://dummyimage.com/300x300/cccccc/ffffff&text=Product+2"
                   alt="Product 2"
                   class="similar-product-image"
                   loading="lazy">
            </div>
            <div class="similar-product-info">
              <h3 class="similar-product-title">Product 2</h3>
              <div class="similar-product-price">
                <span class="similar-price-item similar-price-item--regular">
                  $89.99
                </span>
              </div>
            </div>
          </div>

          <div class="similar-product-card">
            <div class="similar-product-image-container">
              <img src="https://dummyimage.com/300x300/cccccc/ffffff&text=Product+3"
                   alt="Product 3"
                   class="similar-product-image"
                   loading="lazy">
            </div>
            <div class="similar-product-info">
              <h3 class="similar-product-title">Product 3</h3>
              <div class="similar-product-price">
                <span class="similar-price-item similar-price-item--regular">
                  $59.99
                </span>
              </div>
            </div>
          </div>

          <div class="similar-product-card">
            <div class="similar-product-image-container">
              <img src="https://dummyimage.com/300x300/cccccc/ffffff&text=Product+4"
                   alt="Product 4"
                   class="similar-product-image"
                   loading="lazy">
            </div>
            <div class="similar-product-info">
              <h3 class="similar-product-title">Product 4</h3>
              <div class="similar-product-price">
                <span class="similar-price-item similar-price-item--regular">
                  $49.99
                </span>
              </div>
            </div>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Cart Drawer -->
<div id="cart-drawer" class="cart-drawer">
  <div class="cart-drawer__header">
    <h2 class="cart-drawer__title">Your Cart</h2>
    <button class="cart-drawer__close" aria-label="Close cart">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>
  <div class="cart-drawer__content">
    <div class="cart-drawer__empty-state">
      <p>Your cart is empty</p>
      <a href="/collections/all" class="button">Continue Shopping</a>
    </div>
    <div class="cart-drawer__items">
      <!-- Cart items will be loaded here -->
    </div>
  </div>
  <div class="cart-drawer__footer">
    <div class="cart-drawer__subtotal">
      <span>Subtotal</span>
      <span class="cart-drawer__subtotal-price">$0.00</span>
    </div>
    <div class="cart-drawer__buttons">
      <a href="/cart" class="button button--secondary">View Cart</a>
      <a href="/checkout" class="button">Checkout</a>
    </div>
  </div>
</div>
<div id="cart-drawer-overlay" class="cart-drawer-overlay"></div>



<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Cart drawer functionality
    const cartDrawer = document.getElementById('cart-drawer');
    const cartDrawerOverlay = document.getElementById('cart-drawer-overlay');
    const cartDrawerClose = document.querySelector('.cart-drawer__close');

    function openCartDrawer() {
      cartDrawer.classList.add('active');
      cartDrawerOverlay.classList.add('active');
      document.body.style.overflow = 'hidden';
    }

    function closeCartDrawer() {
      cartDrawer.classList.remove('active');
      cartDrawerOverlay.classList.remove('active');
      document.body.style.overflow = '';
    }

    if (cartDrawerClose) {
      cartDrawerClose.addEventListener('click', closeCartDrawer);
    }

    if (cartDrawerOverlay) {
      cartDrawerOverlay.addEventListener('click', closeCartDrawer);
    }

    // AJAX cart functionality
    const ajaxCartForm = document.querySelector('[data-ajax-cart-form]');

    if (ajaxCartForm) {
      ajaxCartForm.addEventListener('submit', function(event) {
        event.preventDefault();

        const formData = new FormData(ajaxCartForm);
        const submitButton = ajaxCartForm.querySelector('button[type="submit"]');

        // Change button text to loading state
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = 'Adding...';
        submitButton.disabled = true;

        fetch('/cart/add.js', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          // Reset button
          submitButton.innerHTML = originalButtonText;
          submitButton.disabled = false;

          if (data.status && data.status === 422) {
            // Handle error
            console.error('Error adding to cart:', data.description);
            return;
          }

          // Dispatch an event that the cart-drawer.js can listen for
          document.dispatchEvent(new CustomEvent('product:added'));
        })
        .catch(error => {
          console.error('Error adding to cart:', error);
          submitButton.innerHTML = originalButtonText;
          submitButton.disabled = false;
        });
      });
    }

    function updateCartDrawer() {
      fetch('/cart.js')
        .then(response => response.json())
        .then(cart => {
          const cartItemsContainer = document.querySelector('.cart-drawer__items');
          const cartEmptyState = document.querySelector('.cart-drawer__empty-state');
          const cartSubtotalPrice = document.querySelector('.cart-drawer__subtotal-price');

          if (cart.item_count === 0) {
            // Cart is empty
            if (cartItemsContainer) cartItemsContainer.style.display = 'none';
            if (cartEmptyState) cartEmptyState.style.display = 'block';
            if (cartSubtotalPrice) cartSubtotalPrice.textContent = '$0.00';
            return;
          }

          // Cart has items
          if (cartItemsContainer) {
            cartItemsContainer.style.display = 'block';

            // Clear existing items
            cartItemsContainer.innerHTML = '';

            // Add each item to the drawer
            cart.items.forEach(item => {
              const itemElement = document.createElement('div');
              itemElement.className = 'cart-drawer__item';

              const itemImage = item.featured_image ? item.featured_image.url : '';
              const itemImageAlt = item.featured_image ? item.featured_image.alt : item.title;

              let variantInfo = '';
              if (item.variant_title) {
                variantInfo = `<div class="cart-drawer__item-variant">${item.variant_title}</div>`;
              }

              itemElement.innerHTML = `
                <img src="${itemImage}" alt="${itemImageAlt}" class="cart-drawer__item-image">
                <div class="cart-drawer__item-details">
                  <h3 class="cart-drawer__item-title">${item.title}</h3>
                  ${variantInfo}
                  <div class="cart-drawer__item-price">${formatMoney(item.price)}</div>
                  <div class="cart-drawer__item-quantity">
                    <button type="button" class="cart-drawer__item-quantity-button" data-cart-update="${item.key}" data-cart-quantity="${item.quantity - 1}">-</button>
                    <input type="number" class="cart-drawer__item-quantity-input" value="${item.quantity}" min="1" data-cart-quantity-input="${item.key}">
                    <button type="button" class="cart-drawer__item-quantity-button" data-cart-update="${item.key}" data-cart-quantity="${item.quantity + 1}">+</button>
                  </div>
                  <button type="button" class="cart-drawer__item-remove" data-cart-remove="${item.key}">Remove</button>
                </div>
              `;

              cartItemsContainer.appendChild(itemElement);
            });

            // Add event listeners for quantity changes and remove buttons
            setupCartItemEvents();
          }

          if (cartEmptyState) cartEmptyState.style.display = 'none';
          if (cartSubtotalPrice) cartSubtotalPrice.textContent = formatMoney(cart.total_price);
        })
        .catch(error => {
          console.error('Error fetching cart:', error);
        });
    }

    function setupCartItemEvents() {
      // Quantity update buttons
      document.querySelectorAll('[data-cart-update]').forEach(button => {
        button.addEventListener('click', function() {
          const key = this.getAttribute('data-cart-update');
          const quantity = parseInt(this.getAttribute('data-cart-quantity'));

          updateCartItem(key, quantity);
        });
      });

      // Quantity input fields
      document.querySelectorAll('[data-cart-quantity-input]').forEach(input => {
        input.addEventListener('change', function() {
          const key = this.getAttribute('data-cart-quantity-input');
          const quantity = parseInt(this.value);

          if (quantity >= 1) {
            updateCartItem(key, quantity);
          }
        });
      });

      // Remove buttons
      document.querySelectorAll('[data-cart-remove]').forEach(button => {
        button.addEventListener('click', function() {
          const key = this.getAttribute('data-cart-remove');

          updateCartItem(key, 0);
        });
      });
    }

    function updateCartItem(key, quantity) {
      fetch('/cart/change.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: key,
          quantity: quantity
        })
      })
      .then(response => response.json())
      .then(cart => {
        updateCartDrawer();
      })
      .catch(error => {
        console.error('Error updating cart:', error);
      });
    }

    // Format money helper function
    function formatMoney(cents) {
      const value = (cents / 100).toFixed(2);
      return '$' + value;
    }


    // Quantity selector functionality
    const quantityInputs = document.querySelectorAll('[data-quantity-input]');
    const decreaseButtons = document.querySelectorAll('[data-quantity-button="decrease"]');
    const increaseButtons = document.querySelectorAll('[data-quantity-button="increase"]');

    // Handle quantity decrease
    decreaseButtons.forEach(button => {
      button.addEventListener('click', function() {
        const input = this.closest('.quantity-selector').querySelector('[data-quantity-input]');
        let value = parseInt(input.value);
        if (value > 1) {
          input.value = value - 1;
        }
      });
    });

    // Handle quantity increase
    increaseButtons.forEach(button => {
      button.addEventListener('click', function() {
        const input = this.closest('.quantity-selector').querySelector('[data-quantity-input]');
        let value = parseInt(input.value);
        input.value = value + 1;
      });
    });

    // Product gallery functionality
    const galleryItems = document.querySelectorAll('.product-gallery__item');

    if (galleryItems.length > 0) {
      galleryItems.forEach(item => {
        item.addEventListener('click', function() {
          // Skip if this is the "view all" item
          if (this.classList.contains('product-gallery__item--last')) {
            return;
          }

          // Open a lightbox or modal with the full-size image
          const img = this.querySelector('img');
          if (img) {
            console.log('Open lightbox with image:', img.src);
            // Here you would typically open a lightbox with the full-size image
          }
        });
      });
    }

    // View all images functionality
    const viewAllButton = document.querySelector('.product-gallery__view-all');
    if (viewAllButton) {
      viewAllButton.addEventListener('click', function(e) {
        e.stopPropagation(); // Prevent triggering the parent click event

        // This would typically open a lightbox gallery with all images
        // For now, we'll just log a message
        console.log('Open full gallery view with all images');

        // In a real implementation, you would open a modal with all product images
        // const productId = document.querySelector('.product-template').dataset.productId;
        // window.location.href = `/products/${productId}?view=gallery`;
      });
    }

    // Accordion functionality
    const accordionToggles = document.querySelectorAll('[data-accordion-toggle]');

    accordionToggles.forEach(toggle => {
      toggle.addEventListener('click', function() {
        const accordionItem = this.closest('.accordion-item');
        const isActive = accordionItem.classList.contains('active');

        // Close all accordion items
        document.querySelectorAll('.accordion-item').forEach(item => {
          item.classList.remove('active');
        });

        // If the clicked item wasn't active, open it
        if (!isActive) {
          accordionItem.classList.add('active');
        }
      });
    });

    // Open the first accordion by default
    const firstAccordion = document.querySelector('.accordion-item');
    if (firstAccordion) {
      firstAccordion.classList.add('active');
    }

    // Color swatch functionality
    const colorSwatches = document.querySelectorAll('.color-swatch');

    colorSwatches.forEach(swatch => {
      swatch.addEventListener('click', function() {
        const optionIndex = parseInt(this.closest('.color-swatch-list').dataset.optionIndex);
        const value = this.dataset.value;

        // Update active state
        const swatchList = this.closest('.color-swatch-list');
        swatchList.querySelectorAll('.color-swatch').forEach(s => {
          s.classList.remove('active');
          // Remove checkmark if it exists
          const checkmark = s.querySelector('.color-swatch__checkmark');
          if (checkmark) {
            checkmark.remove();
          }
        });

        this.classList.add('active');

        // Add checkmark if it doesn't exist
        if (!this.querySelector('.color-swatch__checkmark')) {
          const checkmark = document.createElement('span');
          checkmark.className = 'color-swatch__checkmark';
          checkmark.innerHTML = '<svg width="10" height="8" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 4L4 7L11 1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
          this.appendChild(checkmark);
        }

        // Update the displayed selected value
        const optionLabel = this.closest('.product-form__option').querySelector('.product-form__option-value');
        if (optionLabel) {
          optionLabel.textContent = value;
        }

        // Update hidden select if it exists
        updateVariantSelection(optionIndex, value);
      });
    });

    // Size option functionality
    const sizeOptions = document.querySelectorAll('.size-option');

    sizeOptions.forEach(option => {
      option.addEventListener('click', function() {
        if (this.classList.contains('disabled')) return;

        const optionIndex = parseInt(this.closest('.size-list').dataset.optionIndex);
        const value = this.dataset.value;

        // Update active state
        const sizeList = this.closest('.size-list');
        sizeList.querySelectorAll('.size-option').forEach(o => {
          o.classList.remove('active');
        });
        this.classList.add('active');

        // Update the displayed selected value
        const optionLabel = this.closest('.product-form__option').querySelector('.product-form__option-value');
        if (optionLabel) {
          optionLabel.textContent = value;
        }

        // Update hidden select if it exists
        updateVariantSelection(optionIndex, value);
      });
    });

    // Wishlist button functionality
    const wishlistButton = document.querySelector('.wishlist-button');
    if (wishlistButton) {
      wishlistButton.addEventListener('click', function() {
        // This would typically add the product to a wishlist
        // For now, we'll just toggle a filled state
        const path = this.querySelector('path');
        if (path) {
          if (path.getAttribute('fill') === 'none') {
            path.setAttribute('fill', 'currentColor');
          } else {
            path.setAttribute('fill', 'none');
          }
        }
      });
    }

    // Variant selection functionality
    const variantSelects = document.querySelectorAll('.product-form__select');
    const productForm = document.querySelector('.product-form');
    let productVariants = [];

    if (document.getElementById('ProductVariants')) {
      // Get the available variants
      productVariants = JSON.parse(document.getElementById('ProductVariants').textContent);

      // Update the variant ID when options change
      variantSelects.forEach(select => {
        select.addEventListener('change', function() {
          const optionIndex = parseInt(this.dataset.optionIndex);
          updateVariantSelection(optionIndex, this.value);
        });
      });
    }

    function updateVariantSelection(changedOptionIndex, optionValue) {
      // If we don't have variants data, exit
      if (!productVariants.length) return;

      // Get all selected options
      const selectedOptions = [];

      // First check selects
      variantSelects.forEach(select => {
        selectedOptions[parseInt(select.dataset.optionIndex)] = select.value;
      });

      // Then check color swatches
      document.querySelectorAll('.color-swatch.active').forEach(swatch => {
        const swatchList = swatch.closest('.color-swatch-list');
        selectedOptions[parseInt(swatchList.dataset.optionIndex)] = swatch.dataset.value;
      });

      // Then check size options
      document.querySelectorAll('.size-option.active').forEach(option => {
        const sizeList = option.closest('.size-list');
        selectedOptions[parseInt(sizeList.dataset.optionIndex)] = option.dataset.value;
      });

      // If we have a changed option, update that index
      if (changedOptionIndex !== undefined && optionValue) {
        selectedOptions[changedOptionIndex] = optionValue;
      }

      // Find the matching variant
      const matchingVariant = productVariants.find(variant => {
        return variant.options.every((option, index) => {
          return !selectedOptions[index] || option === selectedOptions[index];
        });
      });

      if (matchingVariant) {
        // Update the variant ID
        const variantIdInput = document.querySelector('input[name="id"]');
        if (variantIdInput) {
          variantIdInput.value = matchingVariant.id;
        }

        // Update the price and discount badge
        const priceContainer = document.querySelector('.product-price');
        let discountBadge = document.querySelector('.discount-badge');

        // Only show compare at price if it's higher than the regular price
        if (matchingVariant.compare_at_price && matchingVariant.compare_at_price > matchingVariant.price) {
          // Calculate discount percentage
          const discountAmount = matchingVariant.compare_at_price - matchingVariant.price;
          const discountPercentage = Math.round((discountAmount / matchingVariant.compare_at_price) * 100);

          // Create or update discount badge
          if (!discountBadge) {
            discountBadge = document.createElement('div');
            discountBadge.className = 'discount-badge';
            priceContainer.parentNode.insertBefore(discountBadge, priceContainer);
          }
          discountBadge.innerHTML = `Save ${discountPercentage}%`;
          discountBadge.style.display = 'inline-block';

          // Update price display
          if (priceContainer) {
            priceContainer.innerHTML = `
              <span class="price-item price-item--sale" data-regular-price>
                ${formatMoney(matchingVariant.price)}
              </span>
              <span class="price-item price-item--compare" style="text-decoration: line-through !important;">
                ${formatMoney(matchingVariant.compare_at_price)}
              </span>
            `;
          }
        } else {
          // Hide discount badge if no sale
          if (discountBadge) {
            discountBadge.style.display = 'none';
          }

          // Update price display
          if (priceContainer) {
            priceContainer.innerHTML = `
              <span class="price-item price-item--regular" data-regular-price style="text-decoration: none !important;">
                ${formatMoney(matchingVariant.price)}
              </span>
            `;
          }
        }

        // Update availability
        const submitButton = document.querySelector('.product-form__submit');
        if (submitButton) {
          if (matchingVariant.available) {
            submitButton.disabled = false;
            submitButton.textContent = {{ section.settings.add_to_cart_text | json }};
          } else {
            submitButton.disabled = true;
            submitButton.textContent = {{ section.settings.sold_out_text | json }};
          }
        }

        // Update option availability
        updateOptionAvailability();
      }
    }

    function updateOptionAvailability() {
      // This would typically disable unavailable combinations
      // For a complete implementation, we would need to check which combinations are valid
      // For now, we'll leave all options enabled
    }

    // Format money helper function
    function formatMoney(cents) {
      const moneyFormat = {{ shop.money_format | json }};
      const value = (cents / 100).toFixed(2);
      return moneyFormat.replace(/\{\{.*?\}\}/, value);
    }


  });
</script>

{% schema %}
{
  "name": "Product",
  "settings": [
    {
      "type": "header",
      "content": "Product Information"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_sku",
      "label": "Show SKU",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_quantity_selector",
      "label": "Show quantity selector",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_wishlist_button",
      "label": "Show wishlist button",
      "default": true
    },
    {
      "type": "header",
      "content": "Media Gallery"
    },
    {
      "type": "select",
      "id": "gallery_layout",
      "label": "Gallery layout",
      "options": [
        {
          "value": "grid",
          "label": "Grid"
        },
        {
          "value": "slider",
          "label": "Slider"
        }
      ],
      "default": "grid"
    },
    {
      "type": "checkbox",
      "id": "enable_image_zoom",
      "label": "Enable image zoom on hover",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplay",
      "label": "Enable video autoplay",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_product_rating",
      "label": "Show product rating",
      "default": true
    },
    {
      "type": "header",
      "content": "Shipping Information"
    },
    {
      "type": "checkbox",
      "id": "show_shipping_info",
      "label": "Show shipping information",
      "default": true
    },
    {
      "type": "text",
      "id": "shipping_info_title",
      "label": "Shipping info title",
      "default": "Free shipping"
    },
    {
      "type": "text",
      "id": "shipping_info_text",
      "label": "Shipping info text",
      "default": "Estimated delivery: 3-5 business days"
    },
    {
      "type": "header",
      "content": "Product Tabs"
    },
    {
      "type": "checkbox",
      "id": "show_details_tab",
      "label": "Show details tab",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_features_tab",
      "label": "Show features tab",
      "default": true
    },
    {
      "type": "richtext",
      "id": "features_content",
      "label": "Features content",
      "default": "<ul><li>Waterproof, breathable material</li><li>Secure pockets</li><li>Adjustable hood</li><li>Velcro® adjustable cuffs</li></ul>"
    },
    {
      "type": "checkbox",
      "id": "show_shipping_returns_tab",
      "label": "Show shipping & returns tab",
      "default": true
    },
    {
      "type": "richtext",
      "id": "shipping_returns_content",
      "label": "Shipping & returns content",
      "default": "<p>Free standard shipping on all orders over $50.</p><p>Returns accepted within 30 days of delivery. Items must be unworn with original tags attached.</p>"
    },
    {
      "type": "header",
      "content": "Buttons"
    },
    {
      "type": "text",
      "id": "add_to_cart_text",
      "label": "Add to cart text",
      "default": "Add to bag"
    },
    {
      "type": "text",
      "id": "sold_out_text",
      "label": "Sold out text",
      "default": "Sold out"
    },
    {
      "type": "checkbox",
      "id": "enable_payment_button",
      "label": "Show dynamic checkout button",
      "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
      "default": true
    }
  ]
}
{% endschema %}
