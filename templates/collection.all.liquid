{% paginate collections.all.products by 24 %}
  {% render 'breadcrumbs' %}

  <div class="collection-banner" data-section-type="collection-banner">
    <div class="collection-banner__wrapper">
      <div class="collection-banner__image-container">
        {% if settings.all_products_banner_image != blank %}
          <img class="collection-banner__image"
               src="{{ settings.all_products_banner_image | img_url: '2000x' }}"
               alt="{{ settings.all_products_banner_image.alt | escape }}"
               width="{{ settings.all_products_banner_image.width }}"
               height="{{ settings.all_products_banner_image.height }}">
        {% else %}
          {{ 'collection-1' | placeholder_svg_tag: 'collection-banner__placeholder-svg' }}
        {% endif %}
      </div>

      {% if settings.all_products_tagline != blank %}
        <div class="collection-banner__content-wrapper">
          <div class="page-width">
            <div class="collection-banner__content">
              <div class="collection-banner__tagline">
                {{ settings.all_products_tagline }}
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>

  <div class="collection-title">
    <div class="page-width">
      <div class="collection-title__wrapper">
        <h1 class="collection-title__heading">All Products</h1>
        {% if settings.all_products_description != blank %}
          <div class="collection-title__description rte">
            {{ settings.all_products_description }}
          </div>
        {% else %}
          <div class="collection-title__description rte">
            <p>Browse our complete collection of products. Use the filters on the left to narrow down your search.</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  <div class="collection-template" data-section-type="collection">
    <div class="page-width">
      <div class="collection-template__inner">
        <div class="collection-template__filters-wrapper">
          <button class="collection-filters-mobile__toggle" aria-expanded="false" aria-controls="collection-filters">
            {{ 'collections.general.filter_by' | t }}
            <span class="collection-filters-mobile__toggle-icon">
              <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </span>
          </button>

          <div id="collection-filters" class="collection-filters">
            <form id="filter-form" class="collection-filters__form" action="{{ request.path }}" method="get">
              <!-- Availability -->
              <div class="collection-filters__group">
                <h3 class="collection-filters__heading">Availability</h3>
                <ul class="collection-filters__checkbox-list">
                  <li class="collection-filters__checkbox-item">
                    <label class="collection-filters__checkbox-label">
                      <input type="checkbox" class="collection-filters__checkbox-input" name="filter.v.availability" value="1">
                      <span class="collection-filters__checkbox-custom"></span>
                      <span>In stock</span>
                      <span class="collection-filters__checkbox-count">({{ collections.all.products.size }})</span>
                    </label>
                  </li>
                </ul>
              </div>

              <!-- Product type -->
              <div class="collection-filters__group">
                <h3 class="collection-filters__heading">Product type</h3>
                <ul class="collection-filters__checkbox-list">
                  {% assign product_types = collections.all.products | map: 'type' | uniq %}
                  {% for type in product_types %}
                    <li class="collection-filters__checkbox-item">
                      <label class="collection-filters__checkbox-label">
                        <input type="checkbox" class="collection-filters__checkbox-input" name="filter.p.product_type" value="{{ type | handleize }}">
                        <span class="collection-filters__checkbox-custom"></span>
                        <span>{{ type }}</span>
                        <span class="collection-filters__checkbox-count">({{ collections.all.products | where: 'type', type | size }})</span>
                      </label>
                    </li>
                  {% endfor %}
                </ul>
              </div>

              <!-- Size -->
              <div class="collection-filters__group">
                <h3 class="collection-filters__heading">Size</h3>
                <ul class="collection-filters__size-list">
                  {% assign size_list = 'S,M,L,XL,28,32,38,40' | split: ',' %}
                  {% for size in size_list %}
                    <li class="collection-filters__size-item">
                      <label class="collection-filters__size-label">
                        <input type="checkbox" class="collection-filters__size-input" name="filter.v.option.size" value="{{ size }}">
                        <span class="collection-filters__size-button">{{ size }}</span>
                      </label>
                    </li>
                  {% endfor %}
                </ul>
              </div>

              <!-- Technology -->
              <div class="collection-filters__group">
                <h3 class="collection-filters__heading">Technology</h3>
                <ul class="collection-filters__checkbox-list">
                  <li class="collection-filters__checkbox-item">
                    <label class="collection-filters__checkbox-label">
                      <input type="checkbox" class="collection-filters__checkbox-input" name="filter.v.option.technology" value="waterproof">
                      <span class="collection-filters__checkbox-custom"></span>
                      <span>Waterproof</span>
                      <span class="collection-filters__checkbox-count">(12)</span>
                    </label>
                  </li>
                  <li class="collection-filters__checkbox-item">
                    <label class="collection-filters__checkbox-label">
                      <input type="checkbox" class="collection-filters__checkbox-input" name="filter.v.option.technology" value="rugged">
                      <span class="collection-filters__checkbox-custom"></span>
                      <span>Rugged</span>
                      <span class="collection-filters__checkbox-count">(8)</span>
                    </label>
                  </li>
                </ul>
              </div>

              <!-- Price -->
              <div class="collection-filters__group">
                <h3 class="collection-filters__heading">Price</h3>
                <div class="collection-filters__price-range">
                  <div class="collection-filters__price-slider"
                       data-min="0"
                       data-max="200"
                       data-current-min="0"
                       data-current-max="200"
                       data-currency="{{ cart.currency.symbol }}">
                    <div class="collection-filters__price-progress"></div>
                    <div class="collection-filters__price-handle collection-filters__price-handle--min" data-handle="min"></div>
                    <div class="collection-filters__price-handle collection-filters__price-handle--max" data-handle="max"></div>
                  </div>

                  <div class="collection-filters__price-display">
                    <span class="collection-filters__price-display-min">{{ cart.currency.symbol }}0.00</span>
                    -
                    <span class="collection-filters__price-display-max">{{ cart.currency.symbol }}200.00</span>
                  </div>

                  <div class="collection-filters__price-inputs">
                    <div class="collection-filters__price-field">
                      <label for="filter-min-price">{{ 'collections.general.from' | t }}</label>
                      <input type="number"
                             id="filter-min-price"
                             name="filter.v.price.gte"
                             value="0"
                             min="0"
                             max="200"
                             placeholder="0"
                             data-filter-update
                             data-input="min">
                    </div>
                    <div class="collection-filters__price-field">
                      <label for="filter-max-price">{{ 'collections.general.to' | t }}</label>
                      <input type="number"
                             id="filter-max-price"
                             name="filter.v.price.lte"
                             value="200"
                             min="0"
                             max="200"
                             placeholder="200"
                             data-filter-update
                             data-input="max">
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        <div class="collection-template__products">
          <div class="collection-template__sort">
            <label for="SortBy" class="collection-template__sort-label">{{ 'collections.general.sort_by' | t }}</label>
            <div class="collection-template__sort-wrapper">
              <select id="SortBy" class="collection-template__sort-select" aria-describedby="a11y-refresh-page-message">
                <option value="manual">{{ 'collections.general.featured' | t }}</option>
                <option value="best-selling">{{ 'collections.general.best_selling' | t }}</option>
                <option value="title-ascending">{{ 'collections.general.alphabetically_a_to_z' | t }}</option>
                <option value="title-descending">{{ 'collections.general.alphabetically_z_to_a' | t }}</option>
                <option value="price-ascending">{{ 'collections.general.price_low_to_high' | t }}</option>
                <option value="price-descending">{{ 'collections.general.price_high_to_low' | t }}</option>
                <option value="created-descending">{{ 'collections.general.date_new_to_old' | t }}</option>
                <option value="created-ascending">{{ 'collections.general.date_old_to_new' | t }}</option>
              </select>
            </div>
          </div>

          <div class="collection-template__product-count">
            <p>{{ collections.all.products.size }} products</p>
          </div>

          <div class="collection-template__product-grid">
            {% for product in collections.all.products %}
              <div class="collection-template__product-item">
                {% render 'product-card',
                  product: product,
                  show_vendor: true,
                  show_rating: true
                %}
              </div>
            {% endfor %}
          </div>

          {% if paginate.pages > 1 %}
            <div class="collection-template__pagination">
              {% render 'pagination', paginate: paginate %}
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Mobile filter toggle
      var filterToggle = document.querySelector('.collection-filters-mobile__toggle');
      var filterContainer = document.getElementById('collection-filters');

      if (filterToggle && filterContainer) {
        filterToggle.addEventListener('click', function() {
          var isExpanded = filterToggle.getAttribute('aria-expanded') === 'true';
          filterToggle.setAttribute('aria-expanded', !isExpanded);

          if (isExpanded) {
            filterContainer.classList.remove('is-open');
          } else {
            filterContainer.classList.add('is-open');
          }
        });
      }
    });
  </script>
{% endpaginate %}
