<div class="customer-addresses" data-customer-addresses>
  <div class="page-width">
    <div class="customer-addresses__header">
      <h1 class="customer-addresses__title">{{ 'customer.addresses.title' | t }}</h1>
      <a href="{{ routes.account_url }}" class="customer-addresses__return-link">
        {{ 'customer.account.return' | t }}
      </a>
    </div>

    <div class="customer-addresses__content">
      <div class="customer-addresses__add">
        <button type="button" 
                class="customer-addresses__add-button" 
                aria-expanded="false"
                aria-controls="AddAddress"
                data-address-toggle="new">
          {{ 'customer.addresses.add_new' | t }}
        </button>

        <div id="AddAddress" class="customer-addresses__form-wrapper" hidden data-address-form="new">
          {% form 'customer_address', customer.new_address %}
            <div class="customer-addresses__form">
              <h2 class="customer-addresses__form-title">{{ 'customer.addresses.add_new' | t }}</h2>

              <div class="customer-addresses__form-fields">
                <div class="customer-addresses__form-field">
                  <label for="AddressFirstName" class="customer-addresses__form-label">
                    {{ 'customer.addresses.first_name' | t }}
                  </label>
                  <input type="text" 
                         id="AddressFirstName" 
                         name="address[first_name]" 
                         value="{{ form.first_name }}" 
                         autocomplete="given-name" 
                         class="customer-addresses__form-input">
                </div>

                <div class="customer-addresses__form-field">
                  <label for="AddressLastName" class="customer-addresses__form-label">
                    {{ 'customer.addresses.last_name' | t }}
                  </label>
                  <input type="text" 
                         id="AddressLastName" 
                         name="address[last_name]" 
                         value="{{ form.last_name }}" 
                         autocomplete="family-name" 
                         class="customer-addresses__form-input">
                </div>

                <div class="customer-addresses__form-field">
                  <label for="AddressCompany" class="customer-addresses__form-label">
                    {{ 'customer.addresses.company' | t }}
                  </label>
                  <input type="text" 
                         id="AddressCompany" 
                         name="address[company]" 
                         value="{{ form.company }}" 
                         autocomplete="organization" 
                         class="customer-addresses__form-input">
                </div>

                <div class="customer-addresses__form-field">
                  <label for="AddressAddress1" class="customer-addresses__form-label">
                    {{ 'customer.addresses.address1' | t }}
                  </label>
                  <input type="text" 
                         id="AddressAddress1" 
                         name="address[address1]" 
                         value="{{ form.address1 }}" 
                         autocomplete="address-line1" 
                         class="customer-addresses__form-input">
                </div>

                <div class="customer-addresses__form-field">
                  <label for="AddressAddress2" class="customer-addresses__form-label">
                    {{ 'customer.addresses.address2' | t }}
                  </label>
                  <input type="text" 
                         id="AddressAddress2" 
                         name="address[address2]" 
                         value="{{ form.address2 }}" 
                         autocomplete="address-line2" 
                         class="customer-addresses__form-input">
                </div>

                <div class="customer-addresses__form-field">
                  <label for="AddressCity" class="customer-addresses__form-label">
                    {{ 'customer.addresses.city' | t }}
                  </label>
                  <input type="text" 
                         id="AddressCity" 
                         name="address[city]" 
                         value="{{ form.city }}" 
                         autocomplete="address-level2" 
                         class="customer-addresses__form-input">
                </div>

                <div class="customer-addresses__form-field">
                  <label for="AddressCountry" class="customer-addresses__form-label">
                    {{ 'customer.addresses.country' | t }}
                  </label>
                  <select id="AddressCountry" 
                          name="address[country]" 
                          autocomplete="country" 
                          class="customer-addresses__form-select" 
                          data-address-country-select 
                          data-default="{{ form.country }}">
                    {{ all_country_option_tags }}
                  </select>
                </div>

                <div class="customer-addresses__form-field" id="AddressProvinceContainer" style="display:none;">
                  <label for="AddressProvince" class="customer-addresses__form-label">
                    {{ 'customer.addresses.province' | t }}
                  </label>
                  <select id="AddressProvince" 
                          name="address[province]" 
                          autocomplete="address-level1" 
                          class="customer-addresses__form-select" 
                          data-default="{{ form.province }}">
                  </select>
                </div>

                <div class="customer-addresses__form-field">
                  <label for="AddressZip" class="customer-addresses__form-label">
                    {{ 'customer.addresses.zip' | t }}
                  </label>
                  <input type="text" 
                         id="AddressZip" 
                         name="address[zip]" 
                         value="{{ form.zip }}" 
                         autocapitalize="characters" 
                         autocomplete="postal-code" 
                         class="customer-addresses__form-input">
                </div>

                <div class="customer-addresses__form-field">
                  <label for="AddressPhone" class="customer-addresses__form-label">
                    {{ 'customer.addresses.phone' | t }}
                  </label>
                  <input type="tel" 
                         id="AddressPhone" 
                         name="address[phone]" 
                         value="{{ form.phone }}" 
                         autocomplete="tel" 
                         class="customer-addresses__form-input">
                </div>

                <div class="customer-addresses__form-field customer-addresses__form-field--checkbox">
                  <input type="checkbox" 
                         id="address_default_address_new" 
                         name="address[default]" 
                         value="1">
                  <label for="address_default_address_new" class="customer-addresses__form-checkbox-label">
                    {{ 'customer.addresses.set_default' | t }}
                  </label>
                </div>
              </div>

              <div class="customer-addresses__form-actions">
                <button type="submit" class="customer-addresses__form-submit">
                  {{ 'customer.addresses.add' | t }}
                </button>
                <button type="button" 
                        class="customer-addresses__form-cancel" 
                        data-address-toggle="new">
                  {{ 'customer.addresses.cancel' | t }}
                </button>
              </div>
            </div>
          {% endform %}
        </div>
      </div>

      <div class="customer-addresses__list">
        <h2 class="customer-addresses__list-title">{{ 'customer.addresses.title' | t }}</h2>

        {% paginate customer.addresses by 10 %}
          {% if customer.addresses.size > 0 %}
            <ul class="customer-addresses__grid" role="list">
              {% for address in customer.addresses %}
                <li class="customer-addresses__item{% if address == customer.default_address %} customer-addresses__item--default{% endif %}">
                  <div class="customer-addresses__card">
                    {% if address == customer.default_address %}
                      <div class="customer-addresses__default-badge">
                        {{ 'customer.addresses.default' | t }}
                      </div>
                    {% endif %}

                    <div class="customer-addresses__card-content">
                      <p class="customer-addresses__name">
                        {{ address.first_name }} {{ address.last_name }}
                      </p>
                      {% if address.company != blank %}
                        <p class="customer-addresses__company">{{ address.company }}</p>
                      {% endif %}
                      <p class="customer-addresses__address">
                        {{ address.street }}<br>
                        {{ address.city }}, {{ address.province_code }} {{ address.zip }}<br>
                        {{ address.country }}<br>
                        {% if address.phone != blank %}
                          {{ address.phone }}
                        {% endif %}
                      </p>
                    </div>

                    <div class="customer-addresses__card-actions">
                      <button type="button" 
                              class="customer-addresses__edit-button" 
                              aria-expanded="false" 
                              aria-controls="EditAddress_{{ address.id }}" 
                              data-address-toggle="{{ address.id }}">
                        {{ 'customer.addresses.edit' | t }}
                      </button>
                      
                      <button type="button" 
                              class="customer-addresses__delete-button" 
                              data-address-delete="{{ address.id }}" 
                              data-confirm-message="{{ 'customer.addresses.delete_confirm' | t }}">
                        {{ 'customer.addresses.delete' | t }}
                      </button>
                    </div>
                  </div>

                  <div id="EditAddress_{{ address.id }}" class="customer-addresses__form-wrapper" hidden data-address-form="{{ address.id }}">
                    {% form 'customer_address', address %}
                      <div class="customer-addresses__form">
                        <h2 class="customer-addresses__form-title">{{ 'customer.addresses.edit_address' | t }}</h2>

                        <div class="customer-addresses__form-fields">
                          <div class="customer-addresses__form-field">
                            <label for="AddressFirstName_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.first_name' | t }}
                            </label>
                            <input type="text" 
                                   id="AddressFirstName_{{ form.id }}" 
                                   name="address[first_name]" 
                                   value="{{ form.first_name }}" 
                                   autocomplete="given-name" 
                                   class="customer-addresses__form-input">
                          </div>

                          <div class="customer-addresses__form-field">
                            <label for="AddressLastName_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.last_name' | t }}
                            </label>
                            <input type="text" 
                                   id="AddressLastName_{{ form.id }}" 
                                   name="address[last_name]" 
                                   value="{{ form.last_name }}" 
                                   autocomplete="family-name" 
                                   class="customer-addresses__form-input">
                          </div>

                          <div class="customer-addresses__form-field">
                            <label for="AddressCompany_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.company' | t }}
                            </label>
                            <input type="text" 
                                   id="AddressCompany_{{ form.id }}" 
                                   name="address[company]" 
                                   value="{{ form.company }}" 
                                   autocomplete="organization" 
                                   class="customer-addresses__form-input">
                          </div>

                          <div class="customer-addresses__form-field">
                            <label for="AddressAddress1_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.address1' | t }}
                            </label>
                            <input type="text" 
                                   id="AddressAddress1_{{ form.id }}" 
                                   name="address[address1]" 
                                   value="{{ form.address1 }}" 
                                   autocomplete="address-line1" 
                                   class="customer-addresses__form-input">
                          </div>

                          <div class="customer-addresses__form-field">
                            <label for="AddressAddress2_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.address2' | t }}
                            </label>
                            <input type="text" 
                                   id="AddressAddress2_{{ form.id }}" 
                                   name="address[address2]" 
                                   value="{{ form.address2 }}" 
                                   autocomplete="address-line2" 
                                   class="customer-addresses__form-input">
                          </div>

                          <div class="customer-addresses__form-field">
                            <label for="AddressCity_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.city' | t }}
                            </label>
                            <input type="text" 
                                   id="AddressCity_{{ form.id }}" 
                                   name="address[city]" 
                                   value="{{ form.city }}" 
                                   autocomplete="address-level2" 
                                   class="customer-addresses__form-input">
                          </div>

                          <div class="customer-addresses__form-field">
                            <label for="AddressCountry_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.country' | t }}
                            </label>
                            <select id="AddressCountry_{{ form.id }}" 
                                    name="address[country]" 
                                    autocomplete="country" 
                                    class="customer-addresses__form-select" 
                                    data-address-country-select 
                                    data-form-id="{{ form.id }}" 
                                    data-default="{{ form.country }}">
                              {{ all_country_option_tags }}
                            </select>
                          </div>

                          <div class="customer-addresses__form-field" id="AddressProvinceContainer_{{ form.id }}" style="display:none;">
                            <label for="AddressProvince_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.province' | t }}
                            </label>
                            <select id="AddressProvince_{{ form.id }}" 
                                    name="address[province]" 
                                    autocomplete="address-level1" 
                                    class="customer-addresses__form-select" 
                                    data-default="{{ form.province }}">
                            </select>
                          </div>

                          <div class="customer-addresses__form-field">
                            <label for="AddressZip_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.zip' | t }}
                            </label>
                            <input type="text" 
                                   id="AddressZip_{{ form.id }}" 
                                   name="address[zip]" 
                                   value="{{ form.zip }}" 
                                   autocapitalize="characters" 
                                   autocomplete="postal-code" 
                                   class="customer-addresses__form-input">
                          </div>

                          <div class="customer-addresses__form-field">
                            <label for="AddressPhone_{{ form.id }}" class="customer-addresses__form-label">
                              {{ 'customer.addresses.phone' | t }}
                            </label>
                            <input type="tel" 
                                   id="AddressPhone_{{ form.id }}" 
                                   name="address[phone]" 
                                   value="{{ form.phone }}" 
                                   autocomplete="tel" 
                                   class="customer-addresses__form-input">
                          </div>

                          <div class="customer-addresses__form-field customer-addresses__form-field--checkbox">
                            <input type="checkbox" 
                                   id="address_default_address_{{ form.id }}" 
                                   name="address[default]" 
                                   value="1" 
                                   {% if address == customer.default_address %}checked{% endif %}>
                            <label for="address_default_address_{{ form.id }}" class="customer-addresses__form-checkbox-label">
                              {{ 'customer.addresses.set_default' | t }}
                            </label>
                          </div>
                        </div>

                        <div class="customer-addresses__form-actions">
                          <button type="submit" class="customer-addresses__form-submit">
                            {{ 'customer.addresses.update' | t }}
                          </button>
                          <button type="button" 
                                  class="customer-addresses__form-cancel" 
                                  data-address-toggle="{{ address.id }}">
                            {{ 'customer.addresses.cancel' | t }}
                          </button>
                        </div>
                      </div>
                    {% endform %}
                  </div>
                </li>
              {% endfor %}
            </ul>

            {% if paginate.pages > 1 %}
              <div class="customer-addresses__pagination">
                {% render 'pagination', paginate: paginate %}
              </div>
            {% endif %}
          {% else %}
            <p class="customer-addresses__empty">
              {{ 'customer.addresses.no_addresses' | t }}
            </p>
          {% endif %}
        {% endpaginate %}
      </div>
    </div>
  </div>
</div>

<style>
  .customer-addresses {
    padding: var(--spacing-large) 0;
  }

  .customer-addresses__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-large);
    padding-bottom: var(--spacing-base);
    border-bottom: 1px solid var(--color-border);
  }

  .customer-addresses__title {
    margin: 0;
    font-size: var(--font-size-heading-large);
  }

  .customer-addresses__return-link {
    color: var(--color-accent);
    text-decoration: none;
  }

  .customer-addresses__return-link:hover {
    text-decoration: underline;
  }

  .customer-addresses__content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-large);
  }

  .customer-addresses__add-button {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-addresses__add-button:hover {
    background-color: var(--color-secondary);
  }

  .customer-addresses__form-wrapper {
    margin-top: var(--spacing-base);
    padding: var(--spacing-base);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    background-color: var(--color-background-light);
  }

  .customer-addresses__form-title {
    margin: 0 0 var(--spacing-base);
    font-size: var(--font-size-heading-small);
  }

  .customer-addresses__form-fields {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-base);
  }

  .customer-addresses__form-field {
    margin-bottom: var(--spacing-small);
  }

  .customer-addresses__form-label {
    display: block;
    margin-bottom: var(--spacing-extra-small);
    font-weight: var(--font-weight-medium);
  }

  .customer-addresses__form-input,
  .customer-addresses__form-select {
    width: 100%;
    padding: var(--spacing-small);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
  }

  .customer-addresses__form-field--checkbox {
    display: flex;
    align-items: center;
  }

  .customer-addresses__form-checkbox-label {
    margin-left: var(--spacing-extra-small);
  }

  .customer-addresses__form-actions {
    display: flex;
    gap: var(--spacing-small);
    margin-top: var(--spacing-base);
  }

  .customer-addresses__form-submit {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-addresses__form-submit:hover {
    background-color: var(--color-secondary);
  }

  .customer-addresses__form-cancel {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-background);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-addresses__form-cancel:hover {
    background-color: var(--color-border-light);
  }

  .customer-addresses__list-title {
    margin: 0 0 var(--spacing-base);
    font-size: var(--font-size-heading-base);
  }

  .customer-addresses__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-base);
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .customer-addresses__item {
    position: relative;
  }

  .customer-addresses__card {
    position: relative;
    padding: var(--spacing-base);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    height: 100%;
  }

  .customer-addresses__item--default .customer-addresses__card {
    border-color: var(--color-primary);
  }

  .customer-addresses__default-badge {
    position: absolute;
    top: var(--spacing-small);
    right: var(--spacing-small);
    padding: var(--spacing-extra-small) var(--spacing-small);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    font-size: var(--font-size-smaller);
    border-radius: var(--border-radius);
  }

  .customer-addresses__name {
    margin: 0 0 var(--spacing-small);
    font-weight: var(--font-weight-medium);
  }

  .customer-addresses__company {
    margin: 0 0 var(--spacing-small);
    color: var(--color-text-light);
  }

  .customer-addresses__address {
    margin: 0 0 var(--spacing-base);
    line-height: 1.5;
  }

  .customer-addresses__card-actions {
    display: flex;
    gap: var(--spacing-small);
    margin-top: var(--spacing-base);
  }

  .customer-addresses__edit-button {
    padding: var(--spacing-extra-small) var(--spacing-small);
    background-color: var(--color-background-light);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-small);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-addresses__edit-button:hover {
    background-color: var(--color-border-light);
  }

  .customer-addresses__delete-button {
    padding: var(--spacing-extra-small) var(--spacing-small);
    background-color: transparent;
    color: var(--color-error);
    border: 1px solid var(--color-error);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-small);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function),
                color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-addresses__delete-button:hover {
    background-color: var(--color-error);
    color: white;
  }

  .customer-addresses__empty {
    padding: var(--spacing-base);
    background-color: var(--color-background-light);
    border-radius: var(--border-radius);
  }

  .customer-addresses__pagination {
    margin-top: var(--spacing-large);
  }

  @media screen and (min-width: 750px) {
    .customer-addresses__form-fields {
      grid-template-columns: 1fr 1fr;
    }

    .customer-addresses__form-field:nth-child(3),
    .customer-addresses__form-field:nth-child(6),
    .customer-addresses__form-field:nth-child(7),
    .customer-addresses__form-field:nth-child(8),
    .customer-addresses__form-field:nth-child(9),
    .customer-addresses__form-field--checkbox {
      grid-column: span 2;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const addressContainer = document.querySelector('[data-customer-addresses]');
    if (!addressContainer) return;

    // Toggle address forms
    const toggleButtons = document.querySelectorAll('[data-address-toggle]');
    toggleButtons.forEach(button => {
      button.addEventListener('click', function() {
        const formId = this.dataset.addressToggle;
        const form = document.querySelector(`[data-address-form="${formId}"]`);
        
        if (form) {
          const isVisible = form.hasAttribute('hidden') === false;
          
          if (isVisible) {
            form.setAttribute('hidden', '');
            this.setAttribute('aria-expanded', 'false');
          } else {
            form.removeAttribute('hidden');
            this.setAttribute('aria-expanded', 'true');
          }
        }
      });
    });

    // Delete address confirmation
    const deleteButtons = document.querySelectorAll('[data-address-delete]');
    deleteButtons.forEach(button => {
      button.addEventListener('click', function() {
        const addressId = this.dataset.addressDelete;
        const confirmMessage = this.dataset.confirmMessage;
        
        if (confirm(confirmMessage)) {
          const deleteForm = document.createElement('form');
          deleteForm.method = 'post';
          deleteForm.action = `/account/addresses/${addressId}`;
          
          const methodInput = document.createElement('input');
          methodInput.type = 'hidden';
          methodInput.name = '_method';
          methodInput.value = 'delete';
          
          const authenticityToken = document.createElement('input');
          authenticityToken.type = 'hidden';
          authenticityToken.name = 'authenticity_token';
          authenticityToken.value = '{{ form.authenticity_token }}';
          
          deleteForm.appendChild(methodInput);
          deleteForm.appendChild(authenticityToken);
          document.body.appendChild(deleteForm);
          deleteForm.submit();
        }
      });
    });

    // Initialize country/province selectors
    const countrySelectors = document.querySelectorAll('[data-address-country-select]');
    countrySelectors.forEach(selector => {
      const formId = selector.dataset.formId || '';
      const countrySelector = selector;
      const provinceSelector = document.getElementById(`AddressProvince${formId ? '_' + formId : ''}`);
      const provinceContainer = document.getElementById(`AddressProvinceContainer${formId ? '_' + formId : ''}`);
      const defaultCountry = selector.dataset.default;
      const defaultProvince = provinceSelector ? provinceSelector.dataset.default : '';
      
      if (countrySelector && provinceSelector && provinceContainer) {
        initializeCountryProvinceSelector(countrySelector, provinceSelector, provinceContainer, defaultCountry, defaultProvince);
      }
    });

    function initializeCountryProvinceSelector(countrySelector, provinceSelector, provinceContainer, defaultCountry, defaultProvince) {
      // Create a new option element
      const createOption = (value, label, selected) => {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = label;
        if (selected) option.selected = true;
        return option;
      };
      
      // Build province selector options based on country selected
      const updateProvinceSelector = (provinces, defaultProvince) => {
        // Clear existing options
        provinceSelector.innerHTML = '';
        
        if (provinces && provinces.length) {
          // Add provinces
          provinces.forEach(province => {
            const selected = province[0] === defaultProvince;
            provinceSelector.appendChild(createOption(province[0], province[1], selected));
          });
          
          // Show the container
          provinceContainer.style.display = '';
        } else {
          // Hide the container if no provinces
          provinceContainer.style.display = 'none';
        }
      };
      
      // Country change handler
      countrySelector.addEventListener('change', function() {
        const selectedCountry = this.value;
        const provinces = window.theme.provinces[selectedCountry] || [];
        updateProvinceSelector(provinces, '');
      });
      
      // Initialize with default values
      if (defaultCountry) {
        countrySelector.value = defaultCountry;
        const provinces = window.theme.provinces[defaultCountry] || [];
        updateProvinceSelector(provinces, defaultProvince);
      }
    }

    // Add provinces data to window.theme
    window.theme = window.theme || {};
    window.theme.provinces = {{ country_option_tags | json }};
  });
</script>
