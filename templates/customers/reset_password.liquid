<div class="customer-reset-password">
  <div class="page-width">
    <div class="customer-reset-password__header">
      <h1 class="customer-reset-password__title">{{ 'customer.reset_password.title' | t }}</h1>
      <p class="customer-reset-password__subtitle">{{ 'customer.reset_password.subtext' | t: email: email }}</p>
    </div>

    <div class="customer-reset-password__content">
      {% form 'reset_customer_password' %}
        {%- if form.errors -%}
          <div class="customer-reset-password__error" role="alert">
            <h2 class="customer-reset-password__error-title">{{ 'general.errors.form_error' | t }}</h2>
            <ul class="customer-reset-password__error-list">
              {%- for error in form.errors -%}
                <li>
                  {%- if error == 'form' -%}
                    {{ form.errors.messages[error] }}
                  {%- else -%}
                    {{ 'general.errors.field_error' | t: field: error, error: form.errors.messages[error] }}
                  {%- endif -%}
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}

        <div class="customer-reset-password__form">
          <div class="customer-reset-password__form-field">
            <label for="CustomerPassword" class="customer-reset-password__form-label">
              {{ 'customer.reset_password.password' | t }}
            </label>
            <input type="password" 
                   name="customer[password]" 
                   id="CustomerPassword" 
                   autocomplete="new-password" 
                   class="customer-reset-password__form-input">
          </div>

          <div class="customer-reset-password__form-field">
            <label for="CustomerPasswordConfirmation" class="customer-reset-password__form-label">
              {{ 'customer.reset_password.password_confirm' | t }}
            </label>
            <input type="password" 
                   name="customer[password_confirmation]" 
                   id="CustomerPasswordConfirmation" 
                   autocomplete="new-password" 
                   class="customer-reset-password__form-input">
          </div>

          <div class="customer-reset-password__form-actions">
            <button type="submit" class="customer-reset-password__submit-button">
              {{ 'customer.reset_password.submit' | t }}
            </button>
          </div>
        </div>
      {% endform %}
    </div>
  </div>
</div>

<style>
  .customer-reset-password {
    padding: var(--spacing-large) 0;
  }

  .customer-reset-password__header {
    margin-bottom: var(--spacing-large);
    text-align: center;
  }

  .customer-reset-password__title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-heading-large);
  }

  .customer-reset-password__subtitle {
    margin: 0;
    color: var(--color-text-light);
  }

  .customer-reset-password__content {
    max-width: 500px;
    margin: 0 auto;
  }

  .customer-reset-password__error {
    margin-bottom: var(--spacing-base);
    padding: var(--spacing-base);
    background-color: rgba(var(--color-error-rgb), 0.1);
    border: 1px solid var(--color-error);
    border-radius: var(--border-radius);
  }

  .customer-reset-password__error-title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-base);
    color: var(--color-error);
  }

  .customer-reset-password__error-list {
    margin: 0;
    padding-left: var(--spacing-base);
    color: var(--color-error);
  }

  .customer-reset-password__form {
    margin-bottom: var(--spacing-large);
  }

  .customer-reset-password__form-field {
    margin-bottom: var(--spacing-base);
  }

  .customer-reset-password__form-label {
    display: block;
    margin-bottom: var(--spacing-extra-small);
    font-weight: var(--font-weight-medium);
  }

  .customer-reset-password__form-input {
    width: 100%;
    padding: var(--spacing-small);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
  }

  .customer-reset-password__form-input:focus {
    border-color: var(--color-primary);
    outline: none;
  }

  .customer-reset-password__form-actions {
    margin-top: var(--spacing-base);
  }

  .customer-reset-password__submit-button {
    width: 100%;
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-reset-password__submit-button:hover {
    background-color: var(--color-secondary);
  }
</style>
