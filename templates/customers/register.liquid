<div class="customer-register">
  <div class="page-width">
    <div class="customer-register__header">
      <h1 class="customer-register__title">{{ 'customer.register.title' | t }}</h1>
    </div>

    <div class="customer-register__content">
      {% form 'create_customer' %}
        {%- if form.errors -%}
          <div class="customer-register__error" role="alert">
            <h2 class="customer-register__error-title">{{ 'general.errors.form_error' | t }}</h2>
            <ul class="customer-register__error-list">
              {%- for error in form.errors -%}
                <li>
                  {%- if error == 'form' -%}
                    {{ form.errors.messages[error] }}
                  {%- else -%}
                    {{ 'general.errors.field_error' | t: field: error, error: form.errors.messages[error] }}
                  {%- endif -%}
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}

        <div class="customer-register__form">
          <div class="customer-register__form-field">
            <label for="RegisterFirstName" class="customer-register__form-label">
              {{ 'customer.register.first_name' | t }}
            </label>
            <input type="text" 
                   name="customer[first_name]" 
                   id="RegisterFirstName" 
                   {% if form.first_name %}value="{{ form.first_name }}"{% endif %} 
                   autocomplete="given-name" 
                   class="customer-register__form-input">
          </div>

          <div class="customer-register__form-field">
            <label for="RegisterLastName" class="customer-register__form-label">
              {{ 'customer.register.last_name' | t }}
            </label>
            <input type="text" 
                   name="customer[last_name]" 
                   id="RegisterLastName" 
                   {% if form.last_name %}value="{{ form.last_name }}"{% endif %} 
                   autocomplete="family-name" 
                   class="customer-register__form-input">
          </div>

          <div class="customer-register__form-field">
            <label for="RegisterEmail" class="customer-register__form-label">
              {{ 'customer.register.email' | t }}
            </label>
            <input type="email" 
                   name="customer[email]" 
                   id="RegisterEmail" 
                   {% if form.email %}value="{{ form.email }}"{% endif %} 
                   autocomplete="email" 
                   autocorrect="off" 
                   autocapitalize="off" 
                   class="customer-register__form-input">
          </div>

          <div class="customer-register__form-field">
            <label for="RegisterPassword" class="customer-register__form-label">
              {{ 'customer.register.password' | t }}
            </label>
            <input type="password" 
                   name="customer[password]" 
                   id="RegisterPassword" 
                   autocomplete="new-password" 
                   class="customer-register__form-input">
          </div>

          <div class="customer-register__form-actions">
            <button type="submit" class="customer-register__submit-button">
              {{ 'customer.register.submit' | t }}
            </button>
            
            <a href="{{ routes.account_login_url }}" class="customer-register__login-link">
              {{ 'customer.log_in' | t }}
            </a>
          </div>
        </div>
      {% endform %}
    </div>
  </div>
</div>

<style>
  .customer-register {
    padding: var(--spacing-large) 0;
  }

  .customer-register__header {
    margin-bottom: var(--spacing-large);
    text-align: center;
  }

  .customer-register__title {
    margin: 0;
    font-size: var(--font-size-heading-large);
  }

  .customer-register__content {
    max-width: 500px;
    margin: 0 auto;
  }

  .customer-register__error {
    margin-bottom: var(--spacing-base);
    padding: var(--spacing-base);
    background-color: rgba(var(--color-error-rgb), 0.1);
    border: 1px solid var(--color-error);
    border-radius: var(--border-radius);
  }

  .customer-register__error-title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-base);
    color: var(--color-error);
  }

  .customer-register__error-list {
    margin: 0;
    padding-left: var(--spacing-base);
    color: var(--color-error);
  }

  .customer-register__form {
    margin-bottom: var(--spacing-large);
  }

  .customer-register__form-field {
    margin-bottom: var(--spacing-base);
  }

  .customer-register__form-label {
    display: block;
    margin-bottom: var(--spacing-extra-small);
    font-weight: var(--font-weight-medium);
  }

  .customer-register__form-input {
    width: 100%;
    padding: var(--spacing-small);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
  }

  .customer-register__form-input:focus {
    border-color: var(--color-primary);
    outline: none;
  }

  .customer-register__form-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-base);
  }

  .customer-register__submit-button {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-register__submit-button:hover {
    background-color: var(--color-secondary);
  }

  .customer-register__login-link {
    display: inline-block;
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-background-light);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    text-align: center;
    text-decoration: none;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-register__login-link:hover {
    background-color: var(--color-border-light);
  }

  @media screen and (min-width: 750px) {
    .customer-register__form-actions {
      flex-direction: row;
      justify-content: space-between;
    }

    .customer-register__submit-button,
    .customer-register__login-link {
      flex: 1;
    }
  }
</style>
