<div class="customer-account">
  <div class="page-width">
    <div class="customer-account__header">
      <h1 class="customer-account__title">{{ 'customer.account.title' | t }}</h1>
      <a href="{{ routes.account_logout_url }}" class="customer-account__logout-link">{{ 'customer.log_out' | t }}</a>
    </div>

    <div class="customer-account__content">
      <div class="customer-account__orders">
        <h2 class="customer-account__section-title">{{ 'customer.orders.title' | t }}</h2>

        {% paginate customer.orders by 5 %}
          {% if customer.orders.size > 0 %}
            <table class="customer-account__orders-table">
              <thead>
                <tr>
                  <th>{{ 'customer.orders.order_number' | t }}</th>
                  <th>{{ 'customer.orders.date' | t }}</th>
                  <th>{{ 'customer.orders.payment_status' | t }}</th>
                  <th>{{ 'customer.orders.fulfillment_status' | t }}</th>
                  <th>{{ 'customer.orders.total' | t }}</th>
                </tr>
              </thead>
              <tbody>
                {% for order in customer.orders %}
                  <tr>
                    <td>
                      <a href="{{ order.customer_url }}" class="customer-account__order-link">
                        {{ 'customer.orders.order_number_link' | t: number: order.name }}
                      </a>
                    </td>
                    <td>{{ order.created_at | time_tag: format: 'date' }}</td>
                    <td>{{ order.financial_status_label }}</td>
                    <td>{{ order.fulfillment_status_label }}</td>
                    <td>{{ order.total_price | money }}</td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>

            {% if paginate.pages > 1 %}
              <div class="customer-account__pagination">
                {% render 'pagination', paginate: paginate %}
              </div>
            {% endif %}
          {% else %}
            <p class="customer-account__no-orders">{{ 'customer.orders.none' | t }}</p>
          {% endif %}
        {% endpaginate %}
      </div>

      <div class="customer-account__details">
        <div class="customer-account__details-header">
          <h2 class="customer-account__section-title">{{ 'customer.account.details' | t }}</h2>
          <a href="{{ routes.account_addresses_url }}" class="customer-account__addresses-link">
            {{ 'customer.account.view_addresses' | t }} ({{ customer.addresses.size }})
          </a>
        </div>

        <div class="customer-account__details-content">
          <h3 class="customer-account__name">{{ customer.name }}</h3>
          
          {% if customer.email %}
            <p class="customer-account__email">{{ customer.email }}</p>
          {% endif %}
          
          {% if customer.default_address %}
            <h3 class="customer-account__address-title">{{ 'customer.addresses.default' | t }}</h3>
            <p class="customer-account__address">
              {{ customer.default_address.first_name }} {{ customer.default_address.last_name }}<br>
              {% if customer.default_address.company != blank %}
                {{ customer.default_address.company }}<br>
              {% endif %}
              {{ customer.default_address.street }}<br>
              {{ customer.default_address.city }}, {{ customer.default_address.province_code }} {{ customer.default_address.zip }}<br>
              {{ customer.default_address.country }}<br>
              {% if customer.default_address.phone != blank %}
                {{ customer.default_address.phone }}
              {% endif %}
            </p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .customer-account {
    padding: var(--spacing-large) 0;
  }

  .customer-account__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-large);
    padding-bottom: var(--spacing-base);
    border-bottom: 1px solid var(--color-border);
  }

  .customer-account__title {
    margin: 0;
    font-size: var(--font-size-heading-large);
  }

  .customer-account__logout-link {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-background-light);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--color-text);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-account__logout-link:hover {
    background-color: var(--color-border-light);
  }

  .customer-account__content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-large);
  }

  .customer-account__section-title {
    margin: 0 0 var(--spacing-base);
    font-size: var(--font-size-heading-base);
  }

  .customer-account__orders-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-base);
  }

  .customer-account__orders-table th,
  .customer-account__orders-table td {
    padding: var(--spacing-small);
    text-align: left;
    border-bottom: 1px solid var(--color-border-light);
  }

  .customer-account__orders-table th {
    font-weight: var(--font-weight-medium);
    border-bottom: 1px solid var(--color-border);
  }

  .customer-account__order-link {
    color: var(--color-accent);
    text-decoration: none;
  }

  .customer-account__order-link:hover {
    text-decoration: underline;
  }

  .customer-account__no-orders {
    padding: var(--spacing-base);
    background-color: var(--color-background-light);
    border-radius: var(--border-radius);
  }

  .customer-account__details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-base);
  }

  .customer-account__addresses-link {
    color: var(--color-accent);
    text-decoration: none;
  }

  .customer-account__addresses-link:hover {
    text-decoration: underline;
  }

  .customer-account__details-content {
    padding: var(--spacing-base);
    background-color: var(--color-background-light);
    border-radius: var(--border-radius);
  }

  .customer-account__name {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-large);
  }

  .customer-account__email {
    margin: 0 0 var(--spacing-base);
    color: var(--color-text-light);
  }

  .customer-account__address-title {
    margin: var(--spacing-base) 0 var(--spacing-small);
    font-size: var(--font-size-base);
  }

  .customer-account__address {
    margin: 0;
    line-height: 1.5;
  }

  .customer-account__pagination {
    margin-top: var(--spacing-base);
  }

  @media screen and (min-width: 750px) {
    .customer-account__content {
      grid-template-columns: 2fr 1fr;
    }
  }

  @media screen and (max-width: 749px) {
    .customer-account__orders-table {
      display: block;
    }

    .customer-account__orders-table thead {
      display: none;
    }

    .customer-account__orders-table tbody,
    .customer-account__orders-table tr,
    .customer-account__orders-table td {
      display: block;
      width: 100%;
    }

    .customer-account__orders-table tr {
      margin-bottom: var(--spacing-base);
      border: 1px solid var(--color-border);
      border-radius: var(--border-radius);
      padding: var(--spacing-small);
    }

    .customer-account__orders-table td {
      display: flex;
      justify-content: space-between;
      padding: var(--spacing-extra-small) var(--spacing-small);
      border-bottom: none;
    }

    .customer-account__orders-table td::before {
      content: attr(data-label);
      font-weight: var(--font-weight-medium);
      margin-right: var(--spacing-small);
    }

    .customer-account__orders-table td:first-child::before {
      content: "{{ 'customer.orders.order_number' | t }}";
    }

    .customer-account__orders-table td:nth-child(2)::before {
      content: "{{ 'customer.orders.date' | t }}";
    }

    .customer-account__orders-table td:nth-child(3)::before {
      content: "{{ 'customer.orders.payment_status' | t }}";
    }

    .customer-account__orders-table td:nth-child(4)::before {
      content: "{{ 'customer.orders.fulfillment_status' | t }}";
    }

    .customer-account__orders-table td:nth-child(5)::before {
      content: "{{ 'customer.orders.total' | t }}";
    }
  }
</style>
