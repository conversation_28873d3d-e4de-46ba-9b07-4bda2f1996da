<div class="customer-login">
  <div class="page-width">
    <div class="customer-login__header">
      <h1 class="customer-login__title">{{ 'customer.login_page.title' | t }}</h1>
    </div>

    <div class="customer-login__content">
      <div class="customer-login__login">
        {% form 'customer_login' %}
          {%- if form.errors -%}
            <div class="customer-login__error" role="alert">
              <h2 class="customer-login__error-title">{{ 'general.errors.login_error' | t }}</h2>
              <ul class="customer-login__error-list">
                {%- for error in form.errors -%}
                  <li>
                    {%- if error == 'form' -%}
                      {{ form.errors.messages[error] }}
                    {%- else -%}
                      {{ 'general.errors.field_error' | t: field: error, error: form.errors.messages[error] }}
                    {%- endif -%}
                  </li>
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}

          <div class="customer-login__form">
            <div class="customer-login__form-field">
              <label for="CustomerEmail" class="customer-login__form-label">
                {{ 'customer.login_page.email' | t }}
              </label>
              <input type="email" 
                     name="customer[email]" 
                     id="CustomerEmail" 
                     autocomplete="email" 
                     autocorrect="off" 
                     autocapitalize="off" 
                     {% if form.errors contains 'form' %} 
                       aria-invalid="true" 
                     {% endif %} 
                     class="customer-login__form-input">
            </div>

            <div class="customer-login__form-field">
              <label for="CustomerPassword" class="customer-login__form-label">
                {{ 'customer.login_page.password' | t }}
              </label>
              <input type="password" 
                     name="customer[password]" 
                     id="CustomerPassword" 
                     autocomplete="current-password" 
                     {% if form.errors contains 'form' %} 
                       aria-invalid="true" 
                     {% endif %} 
                     class="customer-login__form-input">
            </div>

            <div class="customer-login__forgot-password">
              <a href="#recover" class="customer-login__forgot-link" data-toggle-recover-form>
                {{ 'customer.login_page.forgot_password' | t }}
              </a>
            </div>

            <div class="customer-login__form-actions">
              <button type="submit" class="customer-login__submit-button">
                {{ 'customer.login_page.sign_in' | t }}
              </button>
              
              <a href="{{ routes.account_register_url }}" class="customer-login__register-link">
                {{ 'customer.sign_up' | t }}
              </a>
            </div>
          </div>
        {% endform %}
      </div>

      <div id="RecoverPasswordForm" class="customer-login__recover" hidden>
        <div class="customer-login__recover-header">
          <h2 class="customer-login__recover-title">{{ 'customer.recover_password.title' | t }}</h2>
          <p class="customer-login__recover-text">{{ 'customer.recover_password.subtext' | t }}</p>
        </div>

        {% form 'recover_customer_password' %}
          {%- if form.posted_successfully? -%}
            <div class="customer-login__success" role="status">
              <h3 class="customer-login__success-title">{{ 'customer.recover_password.success' | t }}</h3>
            </div>
          {%- elsif form.errors -%}
            <div class="customer-login__error" role="alert">
              <h2 class="customer-login__error-title">{{ 'general.errors.form_error' | t }}</h2>
              <ul class="customer-login__error-list">
                {%- for error in form.errors -%}
                  <li>
                    {%- if error == 'form' -%}
                      {{ form.errors.messages[error] }}
                    {%- else -%}
                      {{ 'general.errors.field_error' | t: field: error, error: form.errors.messages[error] }}
                    {%- endif -%}
                  </li>
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}

          <div class="customer-login__form">
            <div class="customer-login__form-field">
              <label for="RecoverEmail" class="customer-login__form-label">
                {{ 'customer.recover_password.email' | t }}
              </label>
              <input type="email" 
                     name="email" 
                     id="RecoverEmail" 
                     autocomplete="email" 
                     autocorrect="off" 
                     autocapitalize="off" 
                     class="customer-login__form-input">
            </div>

            <div class="customer-login__form-actions">
              <button type="submit" class="customer-login__submit-button">
                {{ 'customer.recover_password.submit' | t }}
              </button>
              
              <button type="button" class="customer-login__cancel-button" data-toggle-recover-form>
                {{ 'customer.recover_password.cancel' | t }}
              </button>
            </div>
          </div>
        {% endform %}
      </div>

      {% if shop.checkout.guest_login %}
        <div class="customer-login__guest">
          <h2 class="customer-login__guest-title">{{ 'customer.login_page.guest_title' | t }}</h2>
          
          <a href="{{ routes.root_url }}?checkout_url={{ routes.root_url }}cart" class="customer-login__guest-button">
            {{ 'customer.login_page.guest_continue' | t }}
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .customer-login {
    padding: var(--spacing-large) 0;
  }

  .customer-login__header {
    margin-bottom: var(--spacing-large);
    text-align: center;
  }

  .customer-login__title {
    margin: 0;
    font-size: var(--font-size-heading-large);
  }

  .customer-login__content {
    max-width: 500px;
    margin: 0 auto;
  }

  .customer-login__error {
    margin-bottom: var(--spacing-base);
    padding: var(--spacing-base);
    background-color: rgba(var(--color-error-rgb), 0.1);
    border: 1px solid var(--color-error);
    border-radius: var(--border-radius);
  }

  .customer-login__error-title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-base);
    color: var(--color-error);
  }

  .customer-login__error-list {
    margin: 0;
    padding-left: var(--spacing-base);
    color: var(--color-error);
  }

  .customer-login__success {
    margin-bottom: var(--spacing-base);
    padding: var(--spacing-base);
    background-color: rgba(var(--color-success-rgb), 0.1);
    border: 1px solid var(--color-success);
    border-radius: var(--border-radius);
  }

  .customer-login__success-title {
    margin: 0;
    font-size: var(--font-size-base);
    color: var(--color-success);
  }

  .customer-login__form {
    margin-bottom: var(--spacing-large);
  }

  .customer-login__form-field {
    margin-bottom: var(--spacing-base);
  }

  .customer-login__form-label {
    display: block;
    margin-bottom: var(--spacing-extra-small);
    font-weight: var(--font-weight-medium);
  }

  .customer-login__form-input {
    width: 100%;
    padding: var(--spacing-small);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
  }

  .customer-login__form-input:focus {
    border-color: var(--color-primary);
    outline: none;
  }

  .customer-login__forgot-password {
    margin-bottom: var(--spacing-base);
    text-align: right;
  }

  .customer-login__forgot-link {
    color: var(--color-accent);
    text-decoration: none;
    font-size: var(--font-size-small);
  }

  .customer-login__forgot-link:hover {
    text-decoration: underline;
  }

  .customer-login__form-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-base);
  }

  .customer-login__submit-button {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-login__submit-button:hover {
    background-color: var(--color-secondary);
  }

  .customer-login__register-link {
    display: inline-block;
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-background-light);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    text-align: center;
    text-decoration: none;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-login__register-link:hover {
    background-color: var(--color-border-light);
  }

  .customer-login__recover {
    margin-top: var(--spacing-large);
    padding-top: var(--spacing-large);
    border-top: 1px solid var(--color-border);
  }

  .customer-login__recover-header {
    margin-bottom: var(--spacing-base);
  }

  .customer-login__recover-title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-heading-small);
  }

  .customer-login__recover-text {
    margin: 0;
    color: var(--color-text-light);
  }

  .customer-login__cancel-button {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-background-light);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-login__cancel-button:hover {
    background-color: var(--color-border-light);
  }

  .customer-login__guest {
    margin-top: var(--spacing-large);
    padding-top: var(--spacing-large);
    border-top: 1px solid var(--color-border);
    text-align: center;
  }

  .customer-login__guest-title {
    margin: 0 0 var(--spacing-base);
    font-size: var(--font-size-heading-small);
  }

  .customer-login__guest-button {
    display: inline-block;
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-background-light);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-login__guest-button:hover {
    background-color: var(--color-border-light);
  }

  @media screen and (min-width: 750px) {
    .customer-login__form-actions {
      flex-direction: row;
      justify-content: space-between;
    }

    .customer-login__submit-button,
    .customer-login__register-link {
      flex: 1;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const recoverForm = document.getElementById('RecoverPasswordForm');
    const toggleButtons = document.querySelectorAll('[data-toggle-recover-form]');
    
    // Check if URL contains the recover parameter
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('recover') && recoverForm) {
      recoverForm.removeAttribute('hidden');
    }
    
    // Toggle between login and recover password forms
    toggleButtons.forEach(button => {
      button.addEventListener('click', function(event) {
        event.preventDefault();
        
        if (recoverForm) {
          const isHidden = recoverForm.hasAttribute('hidden');
          
          if (isHidden) {
            recoverForm.removeAttribute('hidden');
            // Scroll to the recover form
            recoverForm.scrollIntoView({ behavior: 'smooth' });
          } else {
            recoverForm.setAttribute('hidden', '');
          }
        }
      });
    });
  });
</script>
