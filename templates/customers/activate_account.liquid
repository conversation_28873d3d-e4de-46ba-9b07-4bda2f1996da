<div class="customer-activate">
  <div class="page-width">
    <div class="customer-activate__header">
      <h1 class="customer-activate__title">{{ 'customer.activate_account.title' | t }}</h1>
      <p class="customer-activate__subtitle">{{ 'customer.activate_account.subtext' | t }}</p>
    </div>

    <div class="customer-activate__content">
      {% form 'activate_customer_password' %}
        {%- if form.errors -%}
          <div class="customer-activate__error" role="alert">
            <h2 class="customer-activate__error-title">{{ 'general.errors.form_error' | t }}</h2>
            <ul class="customer-activate__error-list">
              {%- for error in form.errors -%}
                <li>
                  {%- if error == 'form' -%}
                    {{ form.errors.messages[error] }}
                  {%- else -%}
                    {{ 'general.errors.field_error' | t: field: error, error: form.errors.messages[error] }}
                  {%- endif -%}
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}

        <div class="customer-activate__form">
          <div class="customer-activate__form-field">
            <label for="CustomerPassword" class="customer-activate__form-label">
              {{ 'customer.activate_account.password' | t }}
            </label>
            <input type="password" 
                   name="customer[password]" 
                   id="CustomerPassword" 
                   autocomplete="new-password" 
                   class="customer-activate__form-input">
          </div>

          <div class="customer-activate__form-field">
            <label for="CustomerPasswordConfirmation" class="customer-activate__form-label">
              {{ 'customer.activate_account.password_confirm' | t }}
            </label>
            <input type="password" 
                   name="customer[password_confirmation]" 
                   id="CustomerPasswordConfirmation" 
                   autocomplete="new-password" 
                   class="customer-activate__form-input">
          </div>

          <div class="customer-activate__form-actions">
            <button type="submit" class="customer-activate__submit-button">
              {{ 'customer.activate_account.submit' | t }}
            </button>
            
            <button type="submit" name="decline" class="customer-activate__decline-button">
              {{ 'customer.activate_account.cancel' | t }}
            </button>
          </div>
        </div>
      {% endform %}
    </div>
  </div>
</div>

<style>
  .customer-activate {
    padding: var(--spacing-large) 0;
  }

  .customer-activate__header {
    margin-bottom: var(--spacing-large);
    text-align: center;
  }

  .customer-activate__title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-heading-large);
  }

  .customer-activate__subtitle {
    margin: 0;
    color: var(--color-text-light);
  }

  .customer-activate__content {
    max-width: 500px;
    margin: 0 auto;
  }

  .customer-activate__error {
    margin-bottom: var(--spacing-base);
    padding: var(--spacing-base);
    background-color: rgba(var(--color-error-rgb), 0.1);
    border: 1px solid var(--color-error);
    border-radius: var(--border-radius);
  }

  .customer-activate__error-title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-base);
    color: var(--color-error);
  }

  .customer-activate__error-list {
    margin: 0;
    padding-left: var(--spacing-base);
    color: var(--color-error);
  }

  .customer-activate__form {
    margin-bottom: var(--spacing-large);
  }

  .customer-activate__form-field {
    margin-bottom: var(--spacing-base);
  }

  .customer-activate__form-label {
    display: block;
    margin-bottom: var(--spacing-extra-small);
    font-weight: var(--font-weight-medium);
  }

  .customer-activate__form-input {
    width: 100%;
    padding: var(--spacing-small);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
  }

  .customer-activate__form-input:focus {
    border-color: var(--color-primary);
    outline: none;
  }

  .customer-activate__form-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-base);
    margin-top: var(--spacing-base);
  }

  .customer-activate__submit-button {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-primary);
    color: var(--color-primary-contrast);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-activate__submit-button:hover {
    background-color: var(--color-secondary);
  }

  .customer-activate__decline-button {
    padding: var(--spacing-small) var(--spacing-base);
    background-color: var(--color-background-light);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: background-color var(--transition-duration-fast) var(--transition-timing-function);
  }

  .customer-activate__decline-button:hover {
    background-color: var(--color-border-light);
  }

  @media screen and (min-width: 750px) {
    .customer-activate__form-actions {
      flex-direction: row;
      justify-content: space-between;
    }

    .customer-activate__submit-button,
    .customer-activate__decline-button {
      flex: 1;
    }
  }
</style>
