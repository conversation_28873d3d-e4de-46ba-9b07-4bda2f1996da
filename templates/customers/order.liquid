<div class="customer-order">
  <div class="page-width">
    <div class="customer-order__header">
      <h1 class="customer-order__title">{{ 'customer.order.title' | t: name: order.name }}</h1>
      <a href="{{ routes.account_url }}" class="customer-order__return-link">
        {{ 'customer.account.return' | t }}
      </a>
    </div>

    <div class="customer-order__content">
      <div class="customer-order__meta">
        <p class="customer-order__date">{{ 'customer.order.date_html' | t: date: order.created_at | time_tag: format: 'date' }}</p>
        
        {% if order.cancelled %}
          <p class="customer-order__cancelled">
            {{ 'customer.order.cancelled_html' | t: date: order.cancelled_at | time_tag: format: 'date' }}
          </p>
          <p class="customer-order__cancelled-reason">
            {{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason }}
          </p>
        {% endif %}
      </div>

      <div class="customer-order__details">
        <div class="customer-order__details-row">
          <div class="customer-order__details-column">
            <h2 class="customer-order__details-title">{{ 'customer.order.billing_address' | t }}</h2>
            <p class="customer-order__address">
              {{ order.billing_address.name }}<br>
              {% if order.billing_address.company != blank %}
                {{ order.billing_address.company }}<br>
              {% endif %}
              {{ order.billing_address.street }}<br>
              {{ order.billing_address.city }}, {{ order.billing_address.province_code }} {{ order.billing_address.zip }}<br>
              {{ order.billing_address.country }}<br>
              {% if order.billing_address.phone != blank %}
                {{ order.billing_address.phone }}
              {% endif %}
            </p>
          </div>

          <div class="customer-order__details-column">
            <h2 class="customer-order__details-title">{{ 'customer.order.payment_status' | t }}</h2>
            <p class="customer-order__status">{{ order.financial_status_label }}</p>
          </div>
        </div>

        <div class="customer-order__details-row">
          <div class="customer-order__details-column">
            <h2 class="customer-order__details-title">{{ 'customer.order.shipping_address' | t }}</h2>
            <p class="customer-order__address">
              {{ order.shipping_address.name }}<br>
              {% if order.shipping_address.company != blank %}
                {{ order.shipping_address.company }}<br>
              {% endif %}
              {{ order.shipping_address.street }}<br>
              {{ order.shipping_address.city }}, {{ order.shipping_address.province_code }} {{ order.shipping_address.zip }}<br>
              {{ order.shipping_address.country }}<br>
              {% if order.shipping_address.phone != blank %}
                {{ order.shipping_address.phone }}
              {% endif %}
            </p>
          </div>

          <div class="customer-order__details-column">
            <h2 class="customer-order__details-title">{{ 'customer.order.fulfillment_status' | t }}</h2>
            <p class="customer-order__status">{{ order.fulfillment_status_label }}</p>
          </div>
        </div>
      </div>

      <div class="customer-order__items">
        <h2 class="customer-order__section-title">{{ 'customer.order.items' | t }}</h2>

        <table class="customer-order__table">
          <thead>
            <tr>
              <th class="customer-order__table-heading customer-order__table-heading--product">{{ 'customer.order.product' | t }}</th>
              <th class="customer-order__table-heading customer-order__table-heading--sku">{{ 'customer.order.sku' | t }}</th>
              <th class="customer-order__table-heading customer-order__table-heading--price">{{ 'customer.order.price' | t }}</th>
              <th class="customer-order__table-heading customer-order__table-heading--quantity">{{ 'customer.order.quantity' | t }}</th>
              <th class="customer-order__table-heading customer-order__table-heading--total">{{ 'customer.order.total' | t }}</th>
            </tr>
          </thead>
          <tbody>
            {% for line_item in order.line_items %}
              <tr class="customer-order__table-row">
                <td class="customer-order__table-cell customer-order__table-cell--product">
                  <div class="customer-order__product">
                    <div class="customer-order__product-image">
                      {% if line_item.image %}
                        <img src="{{ line_item.image | img_url: '120x120', crop: 'center' }}" 
                             alt="{{ line_item.title | escape }}" 
                             loading="lazy" 
                             width="60" 
                             height="60">
                      {% else %}
                        {{ 'product-1' | placeholder_svg_tag: 'customer-order__product-image-placeholder' }}
                      {% endif %}
                    </div>
                    <div class="customer-order__product-details">
                      <a href="{{ line_item.url }}" class="customer-order__product-title">
                        {{ line_item.title }}
                      </a>
                      
                      {% if line_item.fulfillment %}
                        <div class="customer-order__fulfillment">
                          {%- assign fulfillment_created_at = line_item.fulfillment.created_at | date: format: 'date' -%}
                          <p>{{ 'customer.order.fulfilled_at_html' | t: date: fulfillment_created_at }}</p>
                          
                          {% if line_item.fulfillment.tracking_url %}
                            <a href="{{ line_item.fulfillment.tracking_url }}" class="customer-order__tracking-link">
                              {{ 'customer.order.track_shipment' | t }}
                            </a>
                          {% endif %}
                          
                          <div class="customer-order__tracking-info">
                            {% if line_item.fulfillment.tracking_company %}
                              <span class="customer-order__tracking-company">
                                {{ 'customer.order.tracking_company' | t }}: {{ line_item.fulfillment.tracking_company }}
                              </span>
                            {% endif %}
                            
                            {% if line_item.fulfillment.tracking_number %}
                              <span class="customer-order__tracking-number">
                                {{ 'customer.order.tracking_number' | t }}: {{ line_item.fulfillment.tracking_number }}
                              </span>
                            {% endif %}
                          </div>
                        </div>
                      {% endif %}
                      
                      {% if line_item.variant.title != 'Default Title' %}
                        <p class="customer-order__variant">{{ line_item.variant.title }}</p>
                      {% endif %}
                    </div>
                  </div>
                </td>
                <td class="customer-order__table-cell customer-order__table-cell--sku">
                  {{ line_item.sku }}
                </td>
                <td class="customer-order__table-cell customer-order__table-cell--price">
                  {{ line_item.original_price | money }}
                </td>
                <td class="customer-order__table-cell customer-order__table-cell--quantity">
                  {{ line_item.quantity }}
                </td>
                <td class="customer-order__table-cell customer-order__table-cell--total">
                  {{ line_item.final_line_price | money }}
                </td>
              </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr class="customer-order__table-row">
              <td colspan="4" class="customer-order__table-cell customer-order__table-cell--subtotal-label">
                {{ 'customer.order.subtotal' | t }}
              </td>
              <td class="customer-order__table-cell customer-order__table-cell--subtotal">
                {{ order.line_items_subtotal_price | money }}
              </td>
            </tr>
            
            {% for discount in order.discounts %}
              <tr class="customer-order__table-row">
                <td colspan="4" class="customer-order__table-cell customer-order__table-cell--discount-label">
                  {{ 'customer.order.discount' | t }}: {{ discount.code }}
                </td>
                <td class="customer-order__table-cell customer-order__table-cell--discount">
                  -{{ discount.amount | money }}
                </td>
              </tr>
            {% endfor %}
            
            {% for shipping_method in order.shipping_methods %}
              <tr class="customer-order__table-row">
                <td colspan="4" class="customer-order__table-cell customer-order__table-cell--shipping-label">
                  {{ 'customer.order.shipping' | t }}: {{ shipping_method.title }}
                </td>
                <td class="customer-order__table-cell customer-order__table-cell--shipping">
                  {{ shipping_method.price | money }}
                </td>
              </tr>
            {% endfor %}
            
            {% for tax_line in order.tax_lines %}
              <tr class="customer-order__table-row">
                <td colspan="4" class="customer-order__table-cell customer-order__table-cell--tax-label">
                  {{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%)
                </td>
                <td class="customer-order__table-cell customer-order__table-cell--tax">
                  {{ tax_line.price | money }}
                </td>
              </tr>
            {% endfor %}
            
            <tr class="customer-order__table-row customer-order__table-row--total">
              <td colspan="4" class="customer-order__table-cell customer-order__table-cell--total-label">
                {{ 'customer.order.total' | t }}
              </td>
              <td class="customer-order__table-cell customer-order__table-cell--total-price">
                {{ order.total_price | money }} {{ order.currency }}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  </div>
</div>

<style>
  .customer-order {
    padding: var(--spacing-large) 0;
  }

  .customer-order__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-large);
    padding-bottom: var(--spacing-base);
    border-bottom: 1px solid var(--color-border);
  }

  .customer-order__title {
    margin: 0;
    font-size: var(--font-size-heading-large);
  }

  .customer-order__return-link {
    color: var(--color-accent);
    text-decoration: none;
  }

  .customer-order__return-link:hover {
    text-decoration: underline;
  }

  .customer-order__meta {
    margin-bottom: var(--spacing-large);
  }

  .customer-order__date {
    margin: 0 0 var(--spacing-small);
  }

  .customer-order__cancelled {
    margin: 0 0 var(--spacing-extra-small);
    color: var(--color-error);
  }

  .customer-order__cancelled-reason {
    margin: 0;
    color: var(--color-text-light);
  }

  .customer-order__details {
    margin-bottom: var(--spacing-large);
  }

  .customer-order__details-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: var(--spacing-base);
  }

  .customer-order__details-column {
    flex: 1;
    min-width: 250px;
    margin-bottom: var(--spacing-base);
  }

  .customer-order__details-title {
    margin: 0 0 var(--spacing-small);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
  }

  .customer-order__address {
    margin: 0;
    line-height: 1.5;
  }

  .customer-order__status {
    margin: 0;
  }

  .customer-order__section-title {
    margin: 0 0 var(--spacing-base);
    font-size: var(--font-size-heading-base);
  }

  .customer-order__table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-large);
  }

  .customer-order__table-head {
    border-bottom: 1px solid var(--color-border);
  }

  .customer-order__table-heading {
    padding: var(--spacing-small);
    text-align: left;
    font-weight: var(--font-weight-medium);
  }

  .customer-order__table-row {
    border-bottom: 1px solid var(--color-border-light);
  }

  .customer-order__table-row--total {
    border-top: 1px solid var(--color-border);
    border-bottom: none;
    font-weight: var(--font-weight-bold);
  }

  .customer-order__table-cell {
    padding: var(--spacing-small);
    vertical-align: top;
  }

  .customer-order__product {
    display: flex;
    align-items: flex-start;
  }

  .customer-order__product-image {
    flex-shrink: 0;
    margin-right: var(--spacing-small);
    width: 60px;
    height: 60px;
  }

  .customer-order__product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .customer-order__product-title {
    display: block;
    margin-bottom: var(--spacing-extra-small);
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
    text-decoration: none;
  }

  .customer-order__product-title:hover {
    color: var(--color-accent);
  }

  .customer-order__variant {
    margin: 0;
    font-size: var(--font-size-small);
    color: var(--color-text-light);
  }

  .customer-order__fulfillment {
    margin-top: var(--spacing-small);
    font-size: var(--font-size-small);
  }

  .customer-order__tracking-link {
    display: inline-block;
    margin: var(--spacing-extra-small) 0;
    color: var(--color-accent);
    text-decoration: none;
  }

  .customer-order__tracking-link:hover {
    text-decoration: underline;
  }

  .customer-order__tracking-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-extra-small);
    color: var(--color-text-light);
  }

  .customer-order__table-cell--subtotal-label,
  .customer-order__table-cell--discount-label,
  .customer-order__table-cell--shipping-label,
  .customer-order__table-cell--tax-label,
  .customer-order__table-cell--total-label {
    text-align: right;
  }

  .customer-order__table-cell--subtotal,
  .customer-order__table-cell--discount,
  .customer-order__table-cell--shipping,
  .customer-order__table-cell--tax,
  .customer-order__table-cell--total-price {
    text-align: right;
    font-weight: var(--font-weight-medium);
  }

  .customer-order__table-cell--discount {
    color: var(--color-error);
  }

  .customer-order__table-cell--total-price {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-large);
  }

  @media screen and (max-width: 749px) {
    .customer-order__table {
      display: block;
    }

    .customer-order__table thead {
      display: none;
    }

    .customer-order__table tbody,
    .customer-order__table tr,
    .customer-order__table td {
      display: block;
      width: 100%;
    }

    .customer-order__table-row {
      margin-bottom: var(--spacing-base);
      padding-bottom: var(--spacing-base);
    }

    .customer-order__table-cell {
      padding: var(--spacing-extra-small) 0;
      text-align: right;
    }

    .customer-order__table-cell::before {
      content: attr(data-label);
      float: left;
      font-weight: var(--font-weight-medium);
    }

    .customer-order__table-cell--product {
      text-align: left;
    }

    .customer-order__table-cell--product::before {
      content: none;
    }

    .customer-order__table-cell--sku::before {
      content: "{{ 'customer.order.sku' | t }}";
    }

    .customer-order__table-cell--price::before {
      content: "{{ 'customer.order.price' | t }}";
    }

    .customer-order__table-cell--quantity::before {
      content: "{{ 'customer.order.quantity' | t }}";
    }

    .customer-order__table-cell--total::before {
      content: "{{ 'customer.order.total' | t }}";
    }

    .customer-order__table tfoot tr {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .customer-order__table tfoot td {
      width: auto;
      padding: var(--spacing-extra-small) 0;
    }

    .customer-order__table-cell--subtotal-label,
    .customer-order__table-cell--discount-label,
    .customer-order__table-cell--shipping-label,
    .customer-order__table-cell--tax-label,
    .customer-order__table-cell--total-label {
      text-align: left;
    }

    .customer-order__table-cell--subtotal-label::before,
    .customer-order__table-cell--discount-label::before,
    .customer-order__table-cell--shipping-label::before,
    .customer-order__table-cell--tax-label::before,
    .customer-order__table-cell--total-label::before {
      content: none;
    }
  }
</style>
