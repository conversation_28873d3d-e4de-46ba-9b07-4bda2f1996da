{% layout none %}
<!DOCTYPE html>
<html lang="{{ request.locale.iso_code }}">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.color_primary }}">
  <link rel="canonical" href="{{ canonical_url }}">

  <title>{{ shop.name }} | {{ 'gift_cards.issued.title' | t: value: formatted_initial_value, shop: shop.name }}</title>

  <meta name="description" content="{{ 'gift_cards.issued.subtext' | t }}">

  {{ content_for_header }}

  <style>
    :root {
      --color-primary: {{ settings.color_primary }};
      --color-secondary: {{ settings.color_secondary }};
      --color-text: {{ settings.color_text }};
      --color-background: {{ settings.color_background }};
      --font-heading: 'Inter', sans-serif;
      --font-body: 'Inter', sans-serif;
    }
    
    *, *::before, *::after {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: var(--font-body);
      font-size: 16px;
      line-height: 1.5;
      color: var(--color-text);
      background-color: var(--color-background);
      text-align: center;
    }
    
    .gift-card {
      max-width: 600px;
      margin: 60px auto;
      padding: 20px;
    }
    
    .gift-card__header {
      margin-bottom: 40px;
    }
    
    .gift-card__title {
      font-family: var(--font-heading);
      font-size: 24px;
      margin-bottom: 10px;
    }
    
    .gift-card__shop-name {
      font-size: 18px;
      margin-bottom: 20px;
    }
    
    .gift-card__image {
      margin-bottom: 40px;
    }
    
    .gift-card__image img {
      max-width: 100%;
      height: auto;
    }
    
    .gift-card__amount {
      font-size: 36px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    
    .gift-card__code-wrapper {
      margin-bottom: 30px;
    }
    
    .gift-card__code {
      display: inline-block;
      padding: 10px 20px;
      background-color: #f7f7f7;
      border: 1px dashed #ccc;
      font-family: monospace;
      font-size: 18px;
      letter-spacing: 1px;
    }
    
    .gift-card__qr {
      margin-bottom: 30px;
    }
    
    .gift-card__actions {
      margin-bottom: 40px;
    }
    
    .gift-card__action-button {
      display: inline-block;
      padding: 12px 24px;
      background-color: var(--color-primary);
      color: white;
      text-decoration: none;
      margin: 0 10px 10px;
      border-radius: 4px;
    }
    
    .gift-card__footer {
      margin-top: 40px;
      font-size: 14px;
      color: #777;
    }
  </style>
</head>

<body>
  <div class="gift-card">
    <header class="gift-card__header">
      <h1 class="gift-card__title">{{ 'gift_cards.issued.subtext' | t }}</h1>
      <div class="gift-card__shop-name">{{ shop.name }}</div>
    </header>

    <div class="gift-card__image">
      {% if gift_card.image %}
        <img src="{{ gift_card.image.src | img_url: 'medium' }}" alt="{{ 'gift_cards.issued.gift_card_image' | t }}">
      {% else %}
        <img src="{{ 'gift-card.png' | asset_url }}" alt="{{ 'gift_cards.issued.gift_card_image' | t }}">
      {% endif %}
    </div>

    <div class="gift-card__amount">
      {% if gift_card.enabled %}
        {{ gift_card.initial_value | money }}
      {% else %}
        {{ gift_card.initial_value | money }} <span class="gift-card__expired">{{ 'gift_cards.issued.expired' | t }}</span>
      {% endif %}
    </div>

    {% if gift_card.enabled %}
      {% if gift_card.balance != gift_card.initial_value %}
        <div class="gift-card__balance">
          {{ 'gift_cards.issued.remaining_html' | t: balance: gift_card.balance | money }}
        </div>
      {% endif %}
    {% endif %}

    <div class="gift-card__code-wrapper">
      <div class="gift-card__label">{{ 'gift_cards.issued.gift_card_code' | t }}</div>
      <div class="gift-card__code">{{ gift_card.code | format_code }}</div>
    </div>

    {% if gift_card.enabled %}
      <div class="gift-card__qr">
        <img src="{{ gift_card | qr_image_url: size: 200 }}" alt="{{ 'gift_cards.issued.qr_image_alt' | t }}" width="200" height="200">
      </div>

      <div class="gift-card__actions">
        <a href="{{ shop.url }}" class="gift-card__action-button">{{ 'gift_cards.issued.shop_link' | t }}</a>
        <a href="#" class="gift-card__action-button gift-card__print" onclick="window.print();">{{ 'gift_cards.issued.print_gift_card' | t }}</a>
      </div>
    {% endif %}

    <div class="gift-card__footer">
      {% if gift_card.pass_url %}
        <a href="{{ gift_card.pass_url }}">
          <img src="{{ 'gift-card/add-to-apple-wallet.svg' | shopify_asset_url }}" width="120" height="40" alt="{{ 'gift_cards.issued.add_to_apple_wallet' | t }}">
        </a>
      {% endif %}
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      var giftCardCode = document.querySelector('.gift-card__code');
      
      if (giftCardCode) {
        var copyButton = document.createElement('button');
        copyButton.className = 'gift-card__copy';
        copyButton.textContent = '{{ "gift_cards.issued.copy_code" | t }}';
        copyButton.style.marginLeft = '10px';
        copyButton.style.padding = '5px 10px';
        copyButton.style.background = '#f0f0f0';
        copyButton.style.border = '1px solid #ddd';
        copyButton.style.borderRadius = '3px';
        copyButton.style.cursor = 'pointer';
        
        copyButton.addEventListener('click', function() {
          var tempInput = document.createElement('input');
          tempInput.value = '{{ gift_card.code }}';
          document.body.appendChild(tempInput);
          tempInput.select();
          document.execCommand('copy');
          document.body.removeChild(tempInput);
          
          copyButton.textContent = '{{ "gift_cards.issued.copy_code_success" | t }}';
          setTimeout(function() {
            copyButton.textContent = '{{ "gift_cards.issued.copy_code" | t }}';
          }, 2000);
        });
        
        giftCardCode.parentNode.appendChild(copyButton);
      }
    });
  </script>
</body>
</html>
