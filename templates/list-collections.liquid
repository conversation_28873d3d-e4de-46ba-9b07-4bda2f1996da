{% render 'breadcrumbs' %}

<div class="collection-banner" data-section-type="collection-banner">
  <div class="collection-banner__wrapper">
    {% if section.settings.banner_image != blank %}
      <div class="collection-banner__image-container">
        <img class="collection-banner__image"
             src="{{ section.settings.banner_image | img_url: '2000x' }}"
             alt="{{ section.settings.banner_image.alt | escape }}"
             width="{{ section.settings.banner_image.width }}"
             height="{{ section.settings.banner_image.height }}">
      </div>
    {% else %}
      <div class="collection-banner__placeholder">
        {{ 'collection-1' | placeholder_svg_tag: 'collection-banner__placeholder-svg' }}
      </div>
    {% endif %}

    <div class="collection-banner__content-wrapper">
      <div class="page-width">
        <div class="collection-banner__content">
          <div class="collection-banner__tagline">
            Explore our complete range of products
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="collection-title">
  <div class="page-width">
    <div class="collection-title__wrapper">
      <h1 class="collection-title__heading">All Collections</h1>
      <div class="collection-title__description rte">
        <p>Browse all of our collections and find the perfect products for you.</p>
      </div>
    </div>
  </div>
</div>

<div class="page-width">
  <div class="collections-list">
    {% for collection in collections %}
      {% unless collection.handle == 'frontpage' or collection.handle == 'all' %}
        <div class="collections-list__item">
          <a href="{{ collection.url }}" class="collections-list__link">
            <div class="collections-list__image-wrapper">
              {% if collection.image %}
                <img class="collections-list__image"
                     src="{{ collection.image | img_url: '500x500', crop: 'center' }}"
                     alt="{{ collection.title | escape }}"
                     width="500"
                     height="500"
                     loading="lazy">
              {% else %}
                {{ 'collection-' | append: forloop.index | placeholder_svg_tag: 'collections-list__placeholder' }}
              {% endif %}
            </div>
            <h2 class="collections-list__title">{{ collection.title }}</h2>
            <p class="collections-list__count">{{ collection.products_count }} products</p>
          </a>
        </div>
      {% endunless %}
    {% endfor %}
  </div>
</div>

<style>
  .collection-banner {
    margin-bottom: 40px;
    position: relative;
  }

  .collection-banner__wrapper {
    position: relative;
    overflow: hidden;
  }

  .collection-banner__image-container {
    position: relative;
    height: 300px;
    width: 100%;
    overflow: hidden;
  }

  .collection-banner__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .collection-banner__placeholder {
    height: 300px;
    width: 100%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .collection-banner__placeholder-svg {
    width: 100%;
    height: 100%;
    max-width: 500px;
    opacity: 0.2;
  }

  .collection-banner__content-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
  }

  .collection-banner__content {
    text-align: center;
    padding: 20px;
  }

  /* Title is now in a separate section */

  .collection-banner__tagline {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto;
  }

  .collections-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 60px;
  }

  .collections-list__item {
    text-align: center;
  }

  .collections-list__link {
    display: block;
    text-decoration: none;
    color: var(--color-text-primary);
  }

  .collections-list__image-wrapper {
    position: relative;
    margin-bottom: 15px;
    overflow: hidden;
    border-radius: 8px;
  }

  .collections-list__image {
    display: block;
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
  }

  .collections-list__placeholder {
    display: block;
    width: 100%;
    height: auto;
    background-color: #f5f5f5;
    padding: 30px;
  }

  .collections-list__link:hover .collections-list__image {
    transform: scale(1.05);
  }

  .collections-list__title {
    margin: 0 0 5px;
    font-size: 18px;
  }

  .collections-list__count {
    margin: 0;
    font-size: 14px;
    color: var(--color-text-secondary);
  }

  @media screen and (max-width: 989px) {
    .collections-list {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media screen and (max-width: 749px) {
    .collection-banner__image-container {
      height: 200px;
    }

    /* Title is now in a separate section */

    .collection-banner__tagline {
      font-size: 16px;
    }

    .collections-list {
      grid-template-columns: 1fr;
    }
  }
</style>
