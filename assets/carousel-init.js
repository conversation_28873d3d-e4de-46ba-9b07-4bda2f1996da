// Carousel initialization script
document.addEventListener('DOMContentLoaded', function() {
  // Initialize all carousels
  initCarousels();

  // Re-initialize carousels when the Shopify section is reloaded
  if (Shopify && Shopify.designMode) {
    document.addEventListener('shopify:section:load', function(event) {
      initCarousels(event.detail.sectionId);
    });
  }

  // Handle bfcache restoration
  window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
      // Page was restored from bfcache
      console.log('Carousels: Page restored from bfcache');

      // Destroy existing sliders first to prevent duplicates
      document.querySelectorAll('[data-section-type="testimonial-carousel"], [data-section-type="collection-carousel"], [data-section-type="logo-carousel"], [data-section-type="hero-carousel"]').forEach(container => {
        if (container.keenSlider) {
          container.keenSlider.destroy();
          container.keenSlider = null;
        }
      });

      // Re-initialize all carousels
      initCarousels();
    }
  });

  // Listen for bfcache restoration events from other scripts
  document.addEventListener('bfcache:restored', function() {
    console.log('Carousels: Received bfcache:restored event');
    initCarousels();
  });

  // Initialize all carousels or a specific one by section ID
  // Expose this function globally so it can be called from other scripts
  window.initCarousels = function initCarousels(sectionId) {
    // Find all carousel containers
    const carouselContainers = sectionId
      ? [document.querySelector(`[data-section-id="${sectionId}"]`)]
      : document.querySelectorAll('[data-section-type="testimonial-carousel"], [data-section-type="collection-carousel"], [data-section-type="logo-carousel"], [data-section-type="hero-carousel"], [data-section-type="marketplace-logos"]');

    carouselContainers.forEach(container => {
      if (!container) return;

      // Initialize based on section type
      if (container.getAttribute('data-section-type') === 'testimonial-carousel') {
        initTestimonialCarousel(container);
      } else if (container.getAttribute('data-section-type') === 'collection-carousel') {
        initCollectionCarousel(container);
      } else if (container.getAttribute('data-section-type') === 'logo-carousel') {
        initLogoCarousel(container);
      } else if (container.getAttribute('data-section-type') === 'hero-carousel') {
        initHeroCarousel(container);
      } else if (container.getAttribute('data-section-type') === 'marketplace-logos') {
        initMarketplaceLogosCarousel(container);
      }
    });
  }

  // Initialize testimonial carousel
  function initTestimonialCarousel(container) {
    const sliderId = container.querySelector('.keen-slider')?.id;
    if (!sliderId) return;

    // Get settings from data attributes
    const slidesPerView = parseInt(container.getAttribute('data-slides-per-view') || 3, 10);
    const slidesPerViewTablet = parseInt(container.getAttribute('data-slides-per-view-tablet') || 2, 10);
    const slidesPerViewMobile = parseInt(container.getAttribute('data-slides-per-view-mobile') || 1, 10);
    const autoplay = container.getAttribute('data-autoplay') === 'true';
    const autoplaySpeed = parseInt(container.getAttribute('data-autoplay-speed') || 5000, 10);
    const showDots = container.querySelector('[data-carousel-dots]') !== null;

    // Create responsive breakpoints
    const breakpoints = {
      '(min-width: 990px)': {
        slides: { perView: slidesPerView, spacing: 20 }
      },
      '(min-width: 750px) and (max-width: 989px)': {
        slides: { perView: slidesPerViewTablet, spacing: 15 }
      },
      '(max-width: 749px)': {
        slides: { perView: slidesPerViewMobile, spacing: 10 }
      }
    };

    // Force initial slides per view based on screen width
    let initialSlidesPerView = slidesPerView;
    const windowWidth = window.innerWidth;
    if (windowWidth < 750) {
      initialSlidesPerView = slidesPerViewMobile;
    } else if (windowWidth < 990) {
      initialSlidesPerView = slidesPerViewTablet;
    }

    console.log('Initializing testimonial carousel with settings:', {
      slidesPerView,
      slidesPerViewTablet,
      slidesPerViewMobile,
      initialSlidesPerView,
      windowWidth,
      breakpoints
    });

    // First, directly set the slide widths before initialization
    updateSlideWidths(container, initialSlidesPerView);

    // Initialize Keen Slider with simpler configuration
    const slider = new KeenSlider(`#${sliderId}`, {
      loop: true,
      mode: 'free-snap',
      rtl: document.dir === 'rtl',
      // Disable the built-in perView to use our custom width implementation
      created: function(instance) {
        // Setup navigation arrows
        setupArrows(container, instance);

        // Setup dots if enabled
        if (showDots) {
          setupDots(container, instance);
        }

        // Setup autoplay if enabled
        if (autoplay) {
          setupAutoplay(instance, autoplaySpeed);
        }

        // Force the slide widths again after initialization
        setTimeout(() => {
          updateSlideWidths(container, initialSlidesPerView);
        }, 0);
      }
    });

    // Add a resize observer to handle responsive behavior more reliably
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        const newWindowWidth = window.innerWidth;
        let currentSlidesPerView = slidesPerView;

        if (newWindowWidth < 750) {
          currentSlidesPerView = slidesPerViewMobile;
        } else if (newWindowWidth < 990) {
          currentSlidesPerView = slidesPerViewTablet;
        }

        updateSlideWidths(container, currentSlidesPerView);
      });

      resizeObserver.observe(container);
    }

    // Store the slider instance on the container for future reference
    container.keenSlider = slider;

    // Add resize listener to update slide widths on window resize (fallback for browsers without ResizeObserver)
    if (typeof ResizeObserver === 'undefined') {
      window.addEventListener('resize', function() {
        const newWindowWidth = window.innerWidth;
        let currentSlidesPerView = slidesPerView;

        if (newWindowWidth < 750) {
          currentSlidesPerView = slidesPerViewMobile;
        } else if (newWindowWidth < 990) {
          currentSlidesPerView = slidesPerViewTablet;
        }

        updateSlideWidths(container, currentSlidesPerView);
      });
    }

    // Force update on load to ensure correct initial state
    setTimeout(() => {
      updateSlideWidths(container, initialSlidesPerView);
    }, 100);
  }

  // Helper function to update slide widths based on slides per view
  function updateSlideWidths(container, slidesPerView) {
    const slides = container.querySelectorAll('.keen-slider__slide');
    const slideWidth = 100 / slidesPerView;

    slides.forEach(slide => {
      // Force the slide width with !important to override any CSS
      slide.style.cssText = `min-width: ${slideWidth}% !important; max-width: ${slideWidth}% !important; width: ${slideWidth}% !important;`;
    });

    // Also update the container to ensure proper layout
    const sliderContainer = container.querySelector('.keen-slider');
    if (sliderContainer) {
      sliderContainer.style.cssText = 'display: flex; flex-wrap: nowrap; width: 100%;';
    }
  }

  // Initialize collection carousel
  function initCollectionCarousel(container) {
    const sliderId = container.querySelector('.keen-slider')?.id;
    if (!sliderId) return;

    // Get settings from data attributes
    const slidesPerView = parseInt(container.getAttribute('data-slides-per-view') || 3, 10);
    const slidesPerViewTablet = parseInt(container.getAttribute('data-slides-per-view-tablet') || 2, 10);
    const slidesPerViewMobile = parseInt(container.getAttribute('data-slides-per-view-mobile') || 1, 10);
    const autoplay = container.getAttribute('data-autoplay') === 'true';
    const autoplaySpeed = parseInt(container.getAttribute('data-autoplay-speed') || 5000, 10);
    const showDots = container.querySelector('[data-carousel-dots]') !== null;

    // Create responsive breakpoints
    const breakpoints = {
      '(min-width: 990px)': {
        slides: { perView: slidesPerView, spacing: 20 }
      },
      '(min-width: 750px) and (max-width: 989px)': {
        slides: { perView: slidesPerViewTablet, spacing: 15 }
      },
      '(max-width: 749px)': {
        slides: { perView: slidesPerViewMobile, spacing: 10 }
      }
    };

    // Force initial slides per view based on screen width
    let initialSlidesPerView = slidesPerView;
    const windowWidth = window.innerWidth;
    if (windowWidth < 750) {
      initialSlidesPerView = slidesPerViewMobile;
    } else if (windowWidth < 990) {
      initialSlidesPerView = slidesPerViewTablet;
    }

    console.log('Initializing collection carousel with settings:', {
      slidesPerView,
      slidesPerViewTablet,
      slidesPerViewMobile,
      initialSlidesPerView,
      windowWidth,
      breakpoints
    });

    // First, directly set the slide widths before initialization
    updateSlideWidths(container, initialSlidesPerView);

    // Initialize Keen Slider with simpler configuration
    const slider = new KeenSlider(`#${sliderId}`, {
      loop: true,
      mode: 'free-snap',
      rtl: document.dir === 'rtl',
      // Disable the built-in perView to use our custom width implementation
      created: function(instance) {
        // Setup navigation arrows
        setupArrows(container, instance);

        // Setup dots if enabled
        if (showDots) {
          setupDots(container, instance);
        }

        // Setup autoplay if enabled
        if (autoplay) {
          setupAutoplay(instance, autoplaySpeed);
        }

        // Force the slide widths again after initialization
        setTimeout(() => {
          updateSlideWidths(container, initialSlidesPerView);
        }, 0);
      }
    });

    // Store the slider instance on the container for future reference
    container.keenSlider = slider;

    // Add a resize observer to handle responsive behavior more reliably
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        const newWindowWidth = window.innerWidth;
        let currentSlidesPerView = slidesPerView;

        if (newWindowWidth < 750) {
          currentSlidesPerView = slidesPerViewMobile;
        } else if (newWindowWidth < 990) {
          currentSlidesPerView = slidesPerViewTablet;
        }

        updateSlideWidths(container, currentSlidesPerView);
      });

      resizeObserver.observe(container);
    }

    // Add resize listener to update slide widths on window resize (fallback for browsers without ResizeObserver)
    if (typeof ResizeObserver === 'undefined') {
      window.addEventListener('resize', function() {
        const newWindowWidth = window.innerWidth;
        let currentSlidesPerView = slidesPerView;

        if (newWindowWidth < 750) {
          currentSlidesPerView = slidesPerViewMobile;
        } else if (newWindowWidth < 990) {
          currentSlidesPerView = slidesPerViewTablet;
        }

        updateSlideWidths(container, currentSlidesPerView);
      });
    }

    // Force update on load to ensure correct initial state
    setTimeout(() => {
      updateSlideWidths(container, initialSlidesPerView);
    }, 100);
  }

  // Initialize marketplace logos carousel
  function initMarketplaceLogosCarousel(container) {
    const sliderId = container.querySelector('.keen-slider')?.id;
    if (!sliderId) return;

    // Get settings from data attributes
    const slidesPerView = parseInt(container.getAttribute('data-slides-per-view') || 5, 10);
    const slidesPerViewTablet = parseInt(container.getAttribute('data-slides-per-view-tablet') || 3, 10);
    const slidesPerViewMobile = parseInt(container.getAttribute('data-slides-per-view-mobile') || 2, 10);
    const autoplay = container.getAttribute('data-autoplay') === 'true';
    const autoplaySpeed = parseInt(container.getAttribute('data-autoplay-speed') || 3000, 10);
    const showDots = container.querySelector('[data-carousel-dots]') !== null;

    // Force initial slides per view based on screen width
    let initialSlidesPerView = slidesPerView;
    const windowWidth = window.innerWidth;
    if (windowWidth < 750) {
      initialSlidesPerView = slidesPerViewMobile;
    } else if (windowWidth < 990) {
      initialSlidesPerView = slidesPerViewTablet;
    }

    console.log('Initializing marketplace logos carousel with settings:', {
      slidesPerView,
      slidesPerViewTablet,
      slidesPerViewMobile,
      initialSlidesPerView,
      windowWidth
    });

    // First, directly set the slide widths before initialization
    updateSlideWidths(container, initialSlidesPerView);

    // Initialize Keen Slider with simpler configuration
    const slider = new KeenSlider(`#${sliderId}`, {
      loop: true,
      mode: 'free-snap',
      rtl: document.dir === 'rtl',
      // Disable the built-in perView to use our custom width implementation
      created: function(instance) {
        // Setup navigation arrows
        setupArrows(container, instance);

        // Setup dots if enabled
        if (showDots) {
          setupDots(container, instance);
        }

        // Setup autoplay if enabled
        if (autoplay) {
          setupAutoplay(instance, autoplaySpeed);
        }

        // Force the slide widths again after initialization
        setTimeout(() => {
          updateSlideWidths(container, initialSlidesPerView);
        }, 0);
      }
    });

    // Store the slider instance on the container for future reference
    container.keenSlider = slider;

    // Add a resize observer to handle responsive behavior more reliably
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        const newWindowWidth = window.innerWidth;
        let currentSlidesPerView = slidesPerView;

        if (newWindowWidth < 750) {
          currentSlidesPerView = slidesPerViewMobile;
        } else if (newWindowWidth < 990) {
          currentSlidesPerView = slidesPerViewTablet;
        }

        updateSlideWidths(container, currentSlidesPerView);
      });

      resizeObserver.observe(container);
    }

    // Add resize listener to update slide widths on window resize (fallback for browsers without ResizeObserver)
    if (typeof ResizeObserver === 'undefined') {
      window.addEventListener('resize', function() {
        const newWindowWidth = window.innerWidth;
        let currentSlidesPerView = slidesPerView;

        if (newWindowWidth < 750) {
          currentSlidesPerView = slidesPerViewMobile;
        } else if (newWindowWidth < 990) {
          currentSlidesPerView = slidesPerViewTablet;
        }

        updateSlideWidths(container, currentSlidesPerView);
      });
    }

    // Force update on load to ensure correct initial state
    setTimeout(() => {
      updateSlideWidths(container, initialSlidesPerView);
    }, 100);
  }

  // Initialize logo carousel
  function initLogoCarousel(container) {
    const sliderId = container.querySelector('.keen-slider')?.id;
    if (!sliderId) return;

    // Get settings from data attributes
    const slidesPerView = parseInt(container.getAttribute('data-slides-per-view') || 5, 10);
    const slidesPerViewTablet = parseInt(container.getAttribute('data-slides-per-view-tablet') || 3, 10);
    const slidesPerViewMobile = parseInt(container.getAttribute('data-slides-per-view-mobile') || 2, 10);
    const autoplay = container.getAttribute('data-autoplay') !== 'false';
    const autoplaySpeed = parseInt(container.getAttribute('data-autoplay-speed') || 3000, 10);
    const showDots = container.querySelector('[data-carousel-dots]') !== null;

    // Force initial slides per view based on screen width
    let initialSlidesPerView = slidesPerView;
    const windowWidth = window.innerWidth;
    if (windowWidth < 750) {
      initialSlidesPerView = slidesPerViewMobile;
    } else if (windowWidth < 990) {
      initialSlidesPerView = slidesPerViewTablet;
    }

    console.log('Initializing logo carousel with settings:', {
      slidesPerView,
      slidesPerViewTablet,
      slidesPerViewMobile,
      initialSlidesPerView,
      windowWidth
    });

    // First, directly set the slide widths before initialization
    updateSlideWidths(container, initialSlidesPerView);

    // Initialize Keen Slider with simpler configuration
    const slider = new KeenSlider(`#${sliderId}`, {
      loop: true,
      mode: 'free-snap',
      rtl: document.dir === 'rtl',
      // Disable the built-in perView to use our custom width implementation
      created: function(instance) {
        // Setup navigation arrows
        setupArrows(container, instance);

        // Setup dots if enabled
        if (showDots) {
          setupDots(container, instance);
        }

        // Setup autoplay if enabled
        if (autoplay) {
          setupAutoplay(instance, autoplaySpeed);
        }

        // Force the slide widths again after initialization
        setTimeout(() => {
          updateSlideWidths(container, initialSlidesPerView);
        }, 0);
      }
    });

    // Store the slider instance on the container for future reference
    container.keenSlider = slider;

    // Add a resize observer to handle responsive behavior more reliably
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        const newWindowWidth = window.innerWidth;
        let currentSlidesPerView = slidesPerView;

        if (newWindowWidth < 750) {
          currentSlidesPerView = slidesPerViewMobile;
        } else if (newWindowWidth < 990) {
          currentSlidesPerView = slidesPerViewTablet;
        }

        updateSlideWidths(container, currentSlidesPerView);
      });

      resizeObserver.observe(container);
    }

    // Add resize listener to update slide widths on window resize (fallback for browsers without ResizeObserver)
    if (typeof ResizeObserver === 'undefined') {
      window.addEventListener('resize', function() {
        const newWindowWidth = window.innerWidth;
        let currentSlidesPerView = slidesPerView;

        if (newWindowWidth < 750) {
          currentSlidesPerView = slidesPerViewMobile;
        } else if (newWindowWidth < 990) {
          currentSlidesPerView = slidesPerViewTablet;
        }

        updateSlideWidths(container, currentSlidesPerView);
      });
    }

    // Force update on load to ensure correct initial state
    setTimeout(() => {
      updateSlideWidths(container, initialSlidesPerView);
    }, 100);
  }

  // Initialize hero carousel (simplified for now)
  function initHeroCarousel(container) {
    // Similar implementation to testimonial carousel
  }

  // Setup navigation arrows
  function setupArrows(container, instance) {
    const prevButton = container.querySelector('[data-carousel-prev]');
    const nextButton = container.querySelector('[data-carousel-next]');

    if (prevButton) {
      prevButton.addEventListener('click', () => instance.prev());
    }

    if (nextButton) {
      nextButton.addEventListener('click', () => instance.next());
    }
  }

  // Setup pagination dots
  function setupDots(container, instance) {
    const dotsContainer = container.querySelector('[data-carousel-dots]');
    if (!dotsContainer) return;

    // Create dots
    const slides = instance.details().size;
    let dots = [];

    for (let i = 0; i < slides; i++) {
      const dot = document.createElement('button');
      dot.classList.add('testimonial-carousel__dot');
      dot.setAttribute('aria-label', `Go to slide ${i + 1}`);
      dot.addEventListener('click', () => instance.moveToIdx(i));
      dotsContainer.appendChild(dot);
      dots.push(dot);
    }

    // Update active dot on slide change
    instance.on('slideChanged', () => {
      const currentSlide = instance.details().relativeSlide;
      dots.forEach((dot, index) => {
        if (index === currentSlide) {
          dot.classList.add('testimonial-carousel__dot--active');
        } else {
          dot.classList.remove('testimonial-carousel__dot--active');
        }
      });
    });

    // Set initial active dot
    if (dots.length > 0) {
      dots[0].classList.add('testimonial-carousel__dot--active');
    }
  }

  // Setup autoplay
  function setupAutoplay(instance, speed) {
    let interval;

    const start = () => {
      interval = setInterval(() => {
        instance.next();
      }, speed);
    };

    const stop = () => {
      if (interval) {
        clearInterval(interval);
      }
    };

    // Start autoplay
    start();

    // Pause on hover
    instance.on('mouseenter', () => stop());
    instance.on('mouseleave', () => start());

    // Cleanup on destroy
    instance.on('destroyed', () => stop());
  }
});
