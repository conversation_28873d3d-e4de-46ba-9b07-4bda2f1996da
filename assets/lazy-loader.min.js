(()=>{let t={rootMargin:"200px 0px",threshold:0,selectors:{images:".lazy-image",backgrounds:".lazy-background"},classes:{loading:"lazy-loading",loaded:"lazy-loaded",error:"lazy-error"}},a=null,o=null;function loadImage(e){e.classList.add(t.classes.loading);let a=e.dataset.src,o=e.dataset.srcset,s=e.dataset.sizes;var r;a?((r=new Image).onload=function(){o&&(e.srcset=o),s&&(e.sizes=s),e.src=a,e.classList.remove(t.classes.loading),e.classList.add(t.classes.loaded),e.classList.add("is-loaded"),e.removeAttribute("data-src"),e.removeAttribute("data-srcset"),e.removeAttribute("data-sizes")},r.onerror=function(){console.error("Failed to load image:",a),e.classList.remove(t.classes.loading),e.classList.add(t.classes.error)},r.src=a,o&&(r.srcset=o)):(console.warn("Lazy loading failed: No data-src attribute found",e),e.classList.add(t.classes.error))}function loadBackgroundImage(e){e.classList.add(t.classes.loading);let a=e.dataset.background;var o;a?((o=new Image).onload=function(){e.style.backgroundImage=`url(${a})`,e.classList.remove(t.classes.loading),e.classList.add(t.classes.loaded),e.removeAttribute("data-background")},o.onerror=function(){console.error("Failed to load background image:",a),e.classList.remove(t.classes.loading),e.classList.add(t.classes.error)},o.src=a):(console.warn("Lazy loading failed: No data-background attribute found",e),e.classList.add(t.classes.error))}function initImageObserver(){"IntersectionObserver"in window?(a=new IntersectionObserver(function(e,a){e.forEach(function(e){e.isIntersecting&&(loadImage(e.target),a.unobserve(e.target))})},{rootMargin:t.rootMargin,threshold:t.threshold}),document.querySelectorAll(t.selectors.images).forEach(function(e){a.observe(e)})):document.querySelectorAll(t.selectors.images).forEach(loadImage)}function initBackgroundObserver(){"IntersectionObserver"in window?(o=new IntersectionObserver(function(e,a){e.forEach(function(e){e.isIntersecting&&(loadBackgroundImage(e.target),a.unobserve(e.target))})},{rootMargin:t.rootMargin,threshold:t.threshold}),document.querySelectorAll(t.selectors.backgrounds).forEach(function(e){o.observe(e)})):document.querySelectorAll(t.selectors.backgrounds).forEach(loadBackgroundImage)}function initLazyLoading(){initImageObserver(),initBackgroundObserver()}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",initLazyLoading):initLazyLoading(),window.addEventListener("load",initLazyLoading),window.lazyLoader={refresh:initLazyLoading,loadImage:loadImage,loadBackgroundImage:loadBackgroundImage}})();