// Lazy Loader - Advanced image lazy loading with Intersection Observer
// This script provides better lazy loading than the native 'loading="lazy"' attribute

(function() {
  'use strict';

  // Configuration
  const config = {
    // Root margin defines how far from the viewport the element should be when loading starts
    // Positive values will start loading before the element is visible
    rootMargin: '200px 0px',
    // <PERSON>hreshold defines how much of the element needs to be visible to trigger loading
    // 0 means as soon as even 1px is visible, loading will start
    threshold: 0,
    // Selectors for elements to lazy load
    selectors: {
      images: '.lazy-image',
      backgrounds: '.lazy-background'
    },
    // Animation classes
    classes: {
      loading: 'lazy-loading',
      loaded: 'lazy-loaded',
      error: 'lazy-error'
    }
  };

  // Store references to observers
  let imageObserver = null;
  let backgroundObserver = null;

  // Function to check if Intersection Observer is supported
  function isIntersectionObserverSupported() {
    return 'IntersectionObserver' in window;
  }

  // Function to load an image
  function loadImage(element) {
    // Add loading class
    element.classList.add(config.classes.loading);

    // Get the source from data attribute
    const src = element.dataset.src;
    const srcset = element.dataset.srcset;
    const sizes = element.dataset.sizes;

    if (!src) {
      console.warn('Lazy loading failed: No data-src attribute found', element);
      element.classList.add(config.classes.error);
      return;
    }

    // Create a new image to preload
    const img = new Image();

    // Set up event handlers
    img.onload = function() {
      // Once loaded, update the original element
      if (srcset) element.srcset = srcset;
      if (sizes) element.sizes = sizes;
      element.src = src;

      // Remove loading class and add loaded class
      element.classList.remove(config.classes.loading);
      element.classList.add(config.classes.loaded);
      element.classList.add('is-loaded'); // Add is-loaded class for compatibility

      // Remove data attributes to prevent double loading
      element.removeAttribute('data-src');
      element.removeAttribute('data-srcset');
      element.removeAttribute('data-sizes');
    };

    img.onerror = function() {
      console.error('Failed to load image:', src);
      element.classList.remove(config.classes.loading);
      element.classList.add(config.classes.error);
    };

    // Start loading
    img.src = src;
    if (srcset) img.srcset = srcset;
  }

  // Function to load a background image
  function loadBackgroundImage(element) {
    // Add loading class
    element.classList.add(config.classes.loading);

    // Get the source from data attribute
    const src = element.dataset.background;

    if (!src) {
      console.warn('Lazy loading failed: No data-background attribute found', element);
      element.classList.add(config.classes.error);
      return;
    }

    // Create a new image to preload
    const img = new Image();

    // Set up event handlers
    img.onload = function() {
      // Once loaded, update the original element's background
      element.style.backgroundImage = `url(${src})`;

      // Remove loading class and add loaded class
      element.classList.remove(config.classes.loading);
      element.classList.add(config.classes.loaded);

      // Remove data attribute to prevent double loading
      element.removeAttribute('data-background');
    };

    img.onerror = function() {
      console.error('Failed to load background image:', src);
      element.classList.remove(config.classes.loading);
      element.classList.add(config.classes.error);
    };

    // Start loading
    img.src = src;
  }

  // Function to initialize Intersection Observer for images
  function initImageObserver() {
    if (!isIntersectionObserverSupported()) {
      // Fallback for browsers that don't support Intersection Observer
      loadAllImages();
      return;
    }

    // Create a new Intersection Observer
    imageObserver = new IntersectionObserver(function(entries, observer) {
      entries.forEach(function(entry) {
        // If the element is in the viewport (or within the rootMargin)
        if (entry.isIntersecting) {
          // Load the image
          loadImage(entry.target);
          // Stop observing this element
          observer.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: config.rootMargin,
      threshold: config.threshold
    });

    // Start observing all lazy images
    document.querySelectorAll(config.selectors.images).forEach(function(image) {
      imageObserver.observe(image);
    });
  }

  // Function to initialize Intersection Observer for background images
  function initBackgroundObserver() {
    if (!isIntersectionObserverSupported()) {
      // Fallback for browsers that don't support Intersection Observer
      loadAllBackgroundImages();
      return;
    }

    // Create a new Intersection Observer
    backgroundObserver = new IntersectionObserver(function(entries, observer) {
      entries.forEach(function(entry) {
        // If the element is in the viewport (or within the rootMargin)
        if (entry.isIntersecting) {
          // Load the background image
          loadBackgroundImage(entry.target);
          // Stop observing this element
          observer.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: config.rootMargin,
      threshold: config.threshold
    });

    // Start observing all lazy background elements
    document.querySelectorAll(config.selectors.backgrounds).forEach(function(element) {
      backgroundObserver.observe(element);
    });
  }

  // Fallback function to load all images immediately
  function loadAllImages() {
    document.querySelectorAll(config.selectors.images).forEach(loadImage);
  }

  // Fallback function to load all background images immediately
  function loadAllBackgroundImages() {
    document.querySelectorAll(config.selectors.backgrounds).forEach(loadBackgroundImage);
  }

  // Function to initialize lazy loading
  function initLazyLoading() {
    initImageObserver();
    initBackgroundObserver();
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initLazyLoading);
  } else {
    initLazyLoading();
  }

  // Re-initialize when the page is fully loaded to catch any dynamically added elements
  window.addEventListener('load', initLazyLoading);

  // Expose the API to the global scope
  window.lazyLoader = {
    refresh: initLazyLoading,
    loadImage: loadImage,
    loadBackgroundImage: loadBackgroundImage
  };
})();
