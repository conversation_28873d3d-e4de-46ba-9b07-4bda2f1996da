((n,t)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(n="undefined"!=typeof globalThis?globalThis:n||self).KeenSlider=t()})(this,function(){var P=function(){return(P=Object.assign||function(n){for(var t,e=1,i=arguments.length;e<i;e++)for(var o in t=arguments[e])Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o]);return n}).apply(this,arguments)};function t(n,t,e){if(e||2===arguments.length)for(var i,o=0,a=t.length;o<a;o++)!i&&o in t||((i=i||Array.prototype.slice.call(t,0,o))[o]=t[o]);return n.concat(i||Array.prototype.slice.call(t))}function i(n){return Array.prototype.slice.call(n)}function e(n,t){var e=Math.floor(n);return e===t||e+1===t?n:t}function r(){return Date.now()}function a(n,t,e){if(t="data-keen-slider-"+t,null===e)return n.removeAttribute(t);n.setAttribute(t,e||"")}function o(n,t){return t=t||document,"function"==typeof n&&(n=n(t)),Array.isArray(n)?n:"string"==typeof n?i(t.querySelectorAll(n)):n instanceof HTMLElement?[n]:n instanceof NodeList?i(n):[]}function u(n){(n=n.raw?n.raw:n).cancelable&&!n.defaultPrevented&&n.preventDefault()}function s(n){(n=n.raw?n.raw:n).stopPropagation&&n.stopPropagation()}function c(){var o=[];return{add:function(n,t,e,i){n.addListener?n.addListener(e):n.addEventListener(t,e,i),o.push([n,t,e,i])},input:function(n,t,e,i){var o;this.add(n,t,(o=e,function(n){var t=(n=n.nativeEvent?n.nativeEvent:n).changedTouches||[],e=n.targetTouches||[],i=n.detail&&n.detail.x?n.detail:null;return o({id:i?i.identifier||"i":e[0]?e[0]?e[0].identifier:"e":"d",idChanged:i?i.identifier||"i":t[0]?t[0]?t[0].identifier:"e":"d",raw:n,x:i&&i.x?i.x:e[0]?e[0].screenX:i?i.x:n.pageX,y:i&&i.y?i.y:e[0]?e[0].screenY:i?i.y:n.pageY})}),i)},purge:function(){o.forEach(function(n){n[0].removeListener?n[0].removeListener(n[2]):n[0].removeEventListener(n[1],n[2],n[3])}),o=[]}}}function d(n,t,e){return Math.min(Math.max(n,t),e)}function l(n){return(0<n?1:0)-(n<0?1:0)||+n}function f(n){var t=n.getBoundingClientRect();return{height:e(t.height,n.offsetHeight),width:e(t.width,n.offsetWidth)}}function p(n,t,e,i){n=n&&n[t];return null==n?e:i&&"function"==typeof n?n():n}function v(n){return Math.round(1e6*n)/1e6}function m(a){var e,c,f,m,h,g,u,s,b,y,x,k,w,M,o=[],i=null,T=0;function C(n){_(T+n)}function E(n){n=z(T+n).abs;return D(n)?n:null}function z(n){var e=Math.floor(Math.abs(v(n/c))),i=v((n%c+c)%c),n=(i===c&&(i=0),l(n)),o=u.indexOf(t([],u,!0).reduce(function(n,t){return Math.abs(t-i)<Math.abs(n-i)?t:n})),a=o;return n<0&&e++,o===g&&(e+=(a=0)<n?1:-1),{abs:a+e*g*n,origin:o,rel:a}}function I(n,t,e){return t||!S()?A(n,e):D(n)?(t=A(n=(t=z(null!=e?e:T)).abs+(e=n-t.rel)),v(t=null!==(n=A(n-g*l(e)))&&Math.abs(n)<Math.abs(t)||null===t?n:t)):null}function A(n,t){if(null==t&&(t=v(T)),!D(n)||null===n)return null;n=Math.round(n);var e=z(t),i=e.abs,o=e.origin,a=O(n),t=(t%c+c)%c,r=u[o],n=Math.floor((n-(i-e.rel))/g)*c;return v(r-t-r+u[a]+n+(o===g?c:0))}function D(n){return L(n)===n}function L(n){return d(n,b,y)}function S(){return m.loop}function O(n){return(n%g+g)%g}function _(n){var t=n-T,t=(o.push({distance:t,timestamp:r()}),6<o.length&&(o=o.slice(-6)),T=v(n),H().abs);t!==i&&(n=null!==i,i=t,n)&&a.emit("slideChanged")}function H(n){n=n?null:(()=>{var o,n,a,r,u,t,s,d;if(g)return n=(o=S())?(T%c+c)%c:T,t=(o?T%c:T)-h[0][2],a=0-(t<0&&o?c-Math.abs(t):t),r=0,t=z(T),s=t.abs,u=h[d=t.rel][2],t=h.map(function(n,t){var e=a+r,t=((e<0-n[0]||1<e)&&(e+=(Math.abs(e)>c-1&&o?c:0)*l(-e)),t-d),i=l(t),t=t+s,i=(o&&(-1===i&&u<e&&(t+=g),1===i&&e<u&&(t-=g),null!==x&&t<x&&(e+=c),null!==k)&&k<t&&(e-=c),e+n[0]+n[1]),i=Math.max(0<=e&&i<=1?1:i<0||1<e?0:e<0?Math.min(1,(n[0]+e)/n[0]):(1-e)/n[0],0);return r+=n[0]+n[1],{abs:t,distance:m.rtl?-1*e+1-n[0]:e,portion:i,size:n[0]}}),d=O(s=L(s)),{abs:L(s),length:f,max:M,maxIdx:y,min:w,minIdx:b,position:T,progress:o?n/c:T/f,rel:d,slides:t,slidesLength:c}})();return e.details=n,a.emit("detailsChanged"),n}return e={absToRel:O,add:C,details:null,distToIdx:E,idxToDist:I,init:function(n){if(m=a.options,h=(m.trackConfig||[]).map(function(n){return[p(n,"size",1),p(n,"spacing",0),p(n,"origin",0)]}),(g=h.length)&&(c=v(h.reduce(function(n,t){return n+t[0]+t[1]},0)),t=g-1,f=v(c+h[0][2]-h[t][0]-h[t][2]-h[t][1]),u=h.reduce(function(n,t){var e;return n?(e=h[n.length-1],e=n[n.length-1]+(e[0]+e[2])+e[1],e=v(e=(e-=t[2])<n[n.length-1]?n[n.length-1]:e),n.push(e),(!i||i<e)&&(s=n.length-1),i=e,n):[0]},null),0===f&&(s=0),u.push(v(c))),!g)return H(!0);var i,t,e,o;t=a.options.range,e=a.options.loop,x=b=e?p(e,"min",-1/0):0,k=y=e?p(e,"max",1/0):s,e=p(t,"min",null),null!==(o=p(t,"max",null))&&(y=o),w=(b=null!==e?e:b)===-1/0?b:a.track.idxToDist(b||0,!0,0),M=y===1/0?y:I(y,!0,0),null===o&&(k=y),p(t,"align",!1)&&y!==1/0&&0===h[O(y)][2]&&(M-=1-h[O(y)][0],y=E(M-T)),w=v(w),M=v(M),e=n,Number(e)===e?C(A(L(n))):H()},to:_,velocity:function(){var i=r(),n=o.reduce(function(n,t){var e=t.distance,t=t.timestamp;return 200<i-t||((n=l(e)!==l(n.distance)&&n.distance?{distance:0,lastTimestamp:0,time:0}:n).time&&(n.distance+=e),n.lastTimestamp&&(n.time+=t-n.lastTimestamp),n.lastTimestamp=t),n},{distance:0,lastTimestamp:0,time:0});return n.distance/n.time||0}}}function g(c){var h,y,n,x,k,w,t,e;function f(n){return d(n,t,e)}function p(n){return 1-Math.pow(1-n,3)}function v(){return n?c.track.velocity():0}function m(n,t){void 0===t&&(t=1e3);t=147e-9+(n=Math.abs(n))/t;return{dist:Math.pow(n,2)/t,dur:n/t}}function g(){var n=c.track.details;n&&(k=n.min,w=n.max,t=n.minIdx,e=n.maxIdx)}function b(){c.animator.stop()}c.on("updated",g),c.on("optionsChanged",g),c.on("created",g),c.on("dragStarted",function(){n=!1,b(),h=y=c.track.details.abs}),c.on("dragChecked",function(){n=!0}),c.on("dragEnded",function(){var n,t,e,i,o,a,r,u,s=c.options.mode;"snap"===s&&(o=c.track,a=c.track.details,r=a.position,u=l(v()),r=h+(u=w<r||r<k?0:u),0===a.slides[o.absToRel(r)].portion&&(r-=u),h!==y&&(r=y),l(o.idxToDist(r,!0))!==u&&(r+=u),r=f(r),a=o.idxToDist(r,!0),c.animator.start([{distance:a,duration:500,easing:function(n){return 1+--n*n*n*n*n}}])),"free"!==s&&"free-snap"!==s||(b(),u="free-snap"===c.options.mode,o=c.track,r=v(),x=l(r),a=c.track.details,s=[],r||!u?(i=(e=m(r)).dist,e=e.dur,e*=2,i*=x,u&&(u=o.idxToDist(o.distToIdx(i),!0))&&(i=u),s.push({distance:i,duration:e,easing:p}),((u=(o=a.position)+i)<k||w<u)&&(n=0,t=r,l(u=u<k?k-o:w-o)===x?(o=Math.min(Math.abs(u)/Math.abs(i),1),i=(1-Math.pow(1-o,1/3))*e,s[0].earlyExit=i,t=r*(1-o)):(s[0].earlyExit=0,n+=u),i=(e=m(t,100)).dist*x,c.options.rubberband)&&(s.push({distance:i,duration:2*e.dur,easing:p}),s.push({distance:-i+n,duration:500,easing:p})),c.animator.start(s)):c.moveToIdx(f(a.abs),!0,{duration:500,easing:function(n){return 1+--n*n*n*n*n}}))}),c.on("dragged",function(){y=c.track.details.abs})}function b(r){var e,f,p,m,h,g,v,b,y,x,k,w,i,a,M,T,C,H,P=c();function E(n){if(g&&b===n.id){var t=D(n);if(y){if(!A(n))return I(n);x=t,y=!1,r.emit("dragChecked")}if(T)return x=t;u(n);e=v(x-t)/m*p;o=C===-1/0&&H===1/0?e:(a=(o=r.track.details).length,i=d(e,C-(o=o.position),H-o),0===a?0:r.options.rubberband?o<=H&&C<=o||o<C&&0<f||H<o&&f<0?e:(o=(o<C?o-C:o-H)/a,a=m*a,o=Math.abs(o*a),(a=Math.max(0,1-o/h*2))*a*e):i),a=(f=l(o),r.track.details.position);(C<a&&a<H||a===C&&0<f||a===H&&f<0)&&s(n),k+=o,!w&&5<Math.abs(k*m)&&(w=!0),r.track.add(o),x=t,r.emit("dragged")}var e,i,o,a}function z(n){!g&&r.track.details&&r.track.details.length&&(y=!(w=!(g=!(k=0))),b=n.id,A(n),x=D(n),r.emit("dragStarted"))}function I(n){g&&b===n.idChanged&&(g=!1,r.emit("dragEnded"))}function A(n){var t=L(),e=t?n.y:n.x,t=t?n.x:n.y,n=void 0!==i&&void 0!==a&&Math.abs(a-t)<=Math.abs(i-e);return i=e,a=t,n}function D(n){return L()?n.y:n.x}function L(){return r.options.vertical}function S(){m=r.size,h=L()?window.innerHeight:window.innerWidth;var n=r.track.details;n&&(C=n.min,H=n.max)}function O(n){w&&(s(n),u(n))}function _(){var t,n;P.purge(),r.options.drag&&!r.options.disabled&&(t=r.options.dragSpeed||1,v="function"==typeof t?t:function(n){return n*t},p=r.options.rtl?-1:1,S(),e=r.container,o("[".concat(n="data-keen-slider-clickable","]:not([").concat(n,"=false])"),e).map(function(n){P.add(n,"dragstart",s),P.add(n,"mousedown",s),P.add(n,"touchstart",s)}),P.add(e,"dragstart",function(n){u(n)}),P.add(e,"click",O,{capture:!0}),P.input(e,"ksDragStart",z),P.input(e,"ksDrag",E),P.input(e,"ksDragEnd",I),P.input(e,"mousedown",z),P.input(e,"mousemove",E),P.input(e,"mouseleave",I),P.input(e,"mouseup",I),P.input(e,"touchstart",z,{passive:!0}),P.input(e,"touchmove",E,{passive:!1}),P.input(e,"touchend",I),P.input(e,"touchcancel",I),P.add(window,"wheel",function(n){g&&u(n)}),o("[".concat(n="data-keen-slider-scrollable","]:not([").concat(n,"=false])"),r.container).map(function(n){return a=n,P.input(a,"touchstart",function(n){r=D(n),M=T=!0},{passive:!0}),P.input(a,"touchmove",function(n){var t=L(),e=t?a.scrollHeight-a.clientHeight:a.scrollWidth-a.clientWidth,i=r-D(n),o=t?a.scrollTop:a.scrollLeft,t=t&&"scroll"===a.style.overflowY||!t&&"scroll"===a.style.overflowX;if(r=D(n),(i<0&&0<o||0<i&&o<e)&&M&&t)return T=!0;M=!1,u(n),T=!1}),void P.input(a,"touchend",function(){T=!1});var a,r}))}r.on("updated",S),r.on("optionsChanged",_),r.on("created",_),r.on("destroyed",P.purge)}function x(m){var t,n,h=null;function r(n,t,e){m.animator.active?o(n,t,e):requestAnimationFrame(function(){return o(n,t,e)})}function a(){r(!1,!1,n)}function o(i,o,a){var r,u=0,d=m.size,n=m.track.details;n&&t&&(r=n.slides,t.forEach(function(n,t){var e;i?(!h&&o&&s(n,null,a),c(n,null,a)):r[t]&&(e=r[t].size*d,!h&&o&&s(n,e,a),c(n,r[t].distance*d-u,a),u+=e)}))}function u(n){return"performance"===m.options.renderMode?Math.round(n):n}function s(n,t,e){e=e?"height":"width";null!==t&&(t=u(t)+"px"),n.style["min-"+e]=t,n.style["max-"+e]=t}function c(n,t,e){var i;null!==t&&(t=u(t),i=e?t:0,t="translate3d(".concat(e?0:t,"px, ").concat(i,"px, 0)")),n.style.transform=t,n.style["-webkit-transform"]=t}function d(){t&&(o(!0,!0,n),t=null),m.on("detailsChanged",a,!0)}function l(){r(!1,!0,n)}function f(){d(),n=m.options.vertical,m.options.disabled||"custom"===m.options.renderMode||(h="auto"===p(m.options.slides,"perView",null),m.on("detailsChanged",a),(t=m.slides).length&&l())}m.on("created",f),m.on("optionsChanged",f),m.on("beforeOptionsChanged",function(){d()}),m.on("updated",l),m.on("destroyed",d)}function y(t,l){return function(h){var i,r,u,s,n,d=c();function m(n){a(h.container,"reverse","rtl"!==window.getComputedStyle(h.container,null).getPropertyValue("direction")||n?null:""),a(h.container,"v",h.options.vertical&&!n?"":null),a(h.container,"disabled",h.options.disabled&&!n?"":null)}function g(){b()&&M()}function b(){var n,t=null;if(s.forEach(function(n){n.matches&&(t=n.__media)}),t!==i)return i||h.emit("beforeOptionsChanged"),n=(i=t)?u.breakpoints[t]:u,h.options=P(P({},u),n),m(),I(),A(),C(),1}function y(){return h.options.trackConfig.length}function k(n){for(var t in i=!1,u=P(P({},l),n),d.purge(),r=h.size,s=[],u.breakpoints||[]){var e=window.matchMedia(t);e.__media=t,s.push(e),d.add(e,"change",g)}d.add(window,"orientationchange",z),d.add(window,"resize",E),b()}function w(n){h.animator.stop();var t=h.track.details;h.track.init(null!=n?n:t?t.abs:0)}function M(n){w(n),h.emit("optionsChanged")}function T(n,t){n?(k(n),M(t)):(I(),A(),n=y(),C(),(y()!==n?M:w)(t),h.emit("updated"))}function C(){var n=h.options.slides;if("function"==typeof n)return h.options.trackConfig=n(h.size,h.slides);for(var e,t=h.slides,i=t.length,o="number"==typeof n?n:p(n,"number",i,!0),a=[],r=p(n,"perView",1,!0),u=p(n,"spacing",0,!0)/h.size||0,s="auto"===r?u:u/r,d=p(n,"origin","auto"),c=0,l=0;l<o;l++){var m="auto"===r?(m=f(m=t[l]),(h.options.vertical?m.height:m.width)/h.size||1):1/r-u+s;a.push({origin:"center"===d?.5-m/2:"auto"===d?0:d,size:m,spacing:u}),c+=m}c+=u*(o-1),"auto"!==d||h.options.loop||1===r||(e=0,a.map(function(n){var t=c-e;return e+=n.size+u,1<=t||(n.origin=1-t-(1<c?0:1-c)),n})),h.options.trackConfig=a}function E(){I();var n=h.size;h.options.disabled||n===r||(r=n,T())}function z(){E(),setTimeout(E,500),setTimeout(E,2e3)}function I(){var n=f(h.container);h.size=(h.options.vertical?n.height:n.width)||1}function A(){h.slides=o(h.options.selector,h.container)}h.container=(n=o(t,document)).length?n[0]:null,h.destroy=function(){d.purge(),h.emit("destroyed"),m(!0)},h.prev=function(){h.moveToIdx(h.track.details.abs-1,!0)},h.next=function(){h.moveToIdx(h.track.details.abs+1,!0)},h.update=T,k(h.options)}}return function(n,e,i){try{var o,a,f,h,v,r,k,w=e,M=t([y(n,{drag:!0,mode:"snap",renderMode:"precision",rubberband:!0,selector:".keen-slider__slide"}),x,b,g],i||[],!0),T={},E={emit:function(n){T[n]&&T[n].forEach(function(n){n(E)});n=E.options&&E.options[n];n&&n(E)},moveToIdx:function(n,t,e){n=E.track.idxToDist(n,t);n&&(t=E.options.defaultAnimation,E.animator.start([{distance:n,duration:p(e||t,"duration",500),easing:p(e||t,"easing",function(n){return 1+--n*n*n*n*n})}]))},on:function(n,t,e){void 0===e&&(e=!1),T[n]||(T[n]=[]);var i=T[n].indexOf(t);-1<i?e&&delete T[n][i]:e||T[n].push(t)},options:w};if(E.track=m(E),E.animator=(o=E,a={active:!1,start:function(n){var r,u;l(),o.track.details&&(r=0,u=o.track.details.position,h=f=0,v=n.map(function(n){var t=Number(u),e=null!=(e=n.earlyExit)?e:n.duration,i=n.easing,o=n.distance*i(e/n.duration)||0,a=(u+=o,h);return h+=e,r+=o,[t,n.distance,a,h,n.duration,i]}),c(o.track.distToIdx(r)),d(),o.emit("animationStarted"))},stop:l,targetIdx:null}),M)for(var I=0,C=M;I<C.length;I++)(0,C[I])(E);function u(n){k=k||n,s(!0);var t,e=n-k,i=(h<e&&(e=h),v[f]);return i[3]<e?(f++,u(n)):(n=i[0],(i=i[1]*(0,i[5])(0===(t=i[4])?1:(e-i[2])/t))&&o.track.to(n+i),e<h?d():(k=null,s(!1),c(null),void o.emit("animationEnded")))}function s(n){a.active=n}function c(n){a.targetIdx=n}function d(){r=window.requestAnimationFrame(u)}function l(){var n=r;window.cancelAnimationFrame(n),s(!1),c(null),k&&o.emit("animationStopped"),k=null}return E.track.init(E.options.initial||0),E.emit("created"),E}catch(n){console.error(n)}}});