(()=>{function t(){"function"==typeof updateCartDrawer?updateCartDrawer():window.updateCartDrawer&&window.updateCartDrawer(),document.dispatchEvent(new CustomEvent("bfcache:restored"))}function a(){0<document.querySelectorAll(".keen-slider").length&&"function"==typeof window.initCarousels&&window.initCarousels()}"onpageshow"in window&&window.addEventListener("pageshow",function(e){e.persisted&&(console.log("Page restored from bfcache"),t(),a())}),window.bfcacheFix={refreshDynamicContent:t,reinitializeComponents:a}})(),(()=>{class e{constructor(){this.elements=new Map,this.focusableSelector='a, button, input, select, textarea, [tabindex], audio[controls], video[controls], [contenteditable]:not([contenteditable="false"])',this.init()}init(){document.querySelectorAll('[aria-hidden="true"]').forEach(e=>{this.processAriaHiddenElement(e)}),this.setupMutationObserver()}processAriaHiddenElement(a){a.querySelectorAll(this.focusableSelector).forEach(e=>{var t;"-1"!==e.getAttribute("tabindex")&&(t=e.getAttribute("tabindex"),this.elements.has(e)||this.elements.set(e,{originalTabIndex:t,container:a}),e.setAttribute("tabindex","-1"),e.setAttribute("data-aria-hidden-tabindex","true"))})}restoreFocusableElements(a){this.elements.forEach((e,t)=>{e.container===a&&(null===e.originalTabIndex?t.removeAttribute("tabindex"):t.setAttribute("tabindex",e.originalTabIndex),t.removeAttribute("data-aria-hidden-tabindex"),this.elements.delete(t))})}setupMutationObserver(){new MutationObserver(e=>{e.forEach(e=>{"attributes"===e.type&&"aria-hidden"===e.attributeName&&("true"===(e=e.target).getAttribute("aria-hidden")?this.processAriaHiddenElement(e):this.restoreFocusableElements(e))})}).observe(document.body,{attributes:!0,attributeFilter:["aria-hidden"],subtree:!0})}}document.addEventListener("DOMContentLoaded",()=>{window.ariaHiddenManager=new e}),window.accessibilityUtils={createFocusTrap:function(e){var t=e.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');let a=t[0],n=t[t.length-1],r=!1;function o(e){r&&"Tab"===e.key&&(e.shiftKey&&document.activeElement===a?(e.preventDefault(),n.focus()):e.shiftKey||document.activeElement!==n||(e.preventDefault(),a.focus()))}return{activate(){r=!0,e.addEventListener("keydown",o),a&&setTimeout(()=>{a.focus()},100)},deactivate(){r=!1,e.removeEventListener("keydown",o)}}}}})(),(()=>{let o={rootMargin:"200px 0px",threshold:0,selectors:{images:".lazy-image",backgrounds:".lazy-background"},classes:{loading:"lazy-loading",loaded:"lazy-loaded",error:"lazy-error"}},t=null,a=null;function e(){return"IntersectionObserver"in window}function n(e){e.classList.add(o.classes.loading);let t=e.dataset.src,a=e.dataset.srcset,n=e.dataset.sizes;var r;t?((r=new Image).onload=function(){a&&(e.srcset=a),n&&(e.sizes=n),e.src=t,e.classList.remove(o.classes.loading),e.classList.add(o.classes.loaded),e.classList.add("is-loaded"),e.removeAttribute("data-src"),e.removeAttribute("data-srcset"),e.removeAttribute("data-sizes")},r.onerror=function(){console.error("Failed to load image:",t),e.classList.remove(o.classes.loading),e.classList.add(o.classes.error)},r.src=t,a&&(r.srcset=a)):(console.warn("Lazy loading failed: No data-src attribute found",e),e.classList.add(o.classes.error))}function r(e){e.classList.add(o.classes.loading);let t=e.dataset.background;var a;t?((a=new Image).onload=function(){e.style.backgroundImage=`url(${t})`,e.classList.remove(o.classes.loading),e.classList.add(o.classes.loaded),e.removeAttribute("data-background")},a.onerror=function(){console.error("Failed to load background image:",t),e.classList.remove(o.classes.loading),e.classList.add(o.classes.error)},a.src=t):(console.warn("Lazy loading failed: No data-background attribute found",e),e.classList.add(o.classes.error))}function i(){e()?(t=new IntersectionObserver(function(e,t){e.forEach(function(e){e.isIntersecting&&(n(e.target),t.unobserve(e.target))})},{rootMargin:o.rootMargin,threshold:o.threshold}),document.querySelectorAll(o.selectors.images).forEach(function(e){t.observe(e)})):document.querySelectorAll(o.selectors.images).forEach(n)}function s(){e()?(a=new IntersectionObserver(function(e,t){e.forEach(function(e){e.isIntersecting&&(r(e.target),t.unobserve(e.target))})},{rootMargin:o.rootMargin,threshold:o.threshold}),document.querySelectorAll(o.selectors.backgrounds).forEach(function(e){a.observe(e)})):document.querySelectorAll(o.selectors.backgrounds).forEach(r)}function c(){i(),s()}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",c):c(),window.addEventListener("load",c),window.lazyLoader={refresh:c,loadImage:n,loadBackgroundImage:r}})(),(()=>{let r={highPriority:["theme-variables.css","base.css"],lowPriority:["theme.css","keen-slider.css","collection-filters.css","section-headings.css","button-styles.css","collection-page.css"],conditional:{collection:["collection-page.css"],product:["product-page.css"]}};function o(e,t="low"){var a=document.createElement("link");a.rel="stylesheet",a.href=e,"high"===t?(a.setAttribute("fetchpriority","high"),a.rel="preload",a.as="style",a.onload=function(){this.onload=null,this.rel="stylesheet"}):(a.media="print",a.onload=function(){this.onload=null,this.media="all"}),document.head.appendChild(a)}function t(n){r.highPriority.forEach(e=>{o(n+e,"high")}),setTimeout(()=>{r.lowPriority.forEach(e=>{let t=!1;for(var a in r.conditional)if(r.conditional[a].includes(e)){t=!0;break}t||o(n+e,"low")})},100);var e,t=document.body.className;for(e in r.conditional)t.includes("template-"+e)&&r.conditional[e].forEach(e=>{o(n+e,"low")})}var e;"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){var e=document.getElementById("asset-url");e&&t(e.getAttribute("content"))}):(e=document.getElementById("asset-url"))&&t(e.getAttribute("content"))})(),(()=>{function e(){document.querySelectorAll('link[rel="stylesheet"]').forEach(function(e){var t;e.hasAttribute("data-optimized")||(e.setAttribute("data-optimized","true"),(t=e.getAttribute("href")).includes("theme-variables"))||t.includes("base.css")||t.includes("theme.css")||"print"!==(t=e.getAttribute("media")||"all")&&(e.setAttribute("data-original-media",t),e.setAttribute("media","print"),e.onload=function(){this.setAttribute("media",this.getAttribute("data-original-media"))})})}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",e):e(),window.addEventListener("load",e)})(),document.addEventListener("DOMContentLoaded",function(){let t=document.querySelector(".collection-filters-mobile__toggle"),a=document.getElementById("collection-filters");t&&a&&t.addEventListener("click",function(){var e="true"===t.getAttribute("aria-expanded");t.setAttribute("aria-expanded",!e),e?a.classList.remove("is-open"):a.classList.add("is-open")});var e,n=document.getElementById("SortBy");n&&((e=new URLSearchParams(window.location.search).get("sort_by"))&&n.querySelector(`option[value="${e}"]`)&&(n.value=e),n.addEventListener("change",function(){var e=new URL(window.location.href);e.searchParams.set("sort_by",this.value),window.Shopify&&window.Shopify.designMode?console.log("Sort change prevented in Theme Editor. Would navigate to:",e.href):window.location.href=e.href})),document.querySelectorAll("[data-filter-update]").forEach(function(t){t.addEventListener("change",function(){var e=t.closest("form");e&&(window.Shopify&&window.Shopify.designMode?console.log("Filter update prevented in Theme Editor"):e.submit())})}),document.querySelectorAll(".collection-filters__price-slider").forEach(function(n){let r=n.querySelector(".collection-filters__price-handle--min"),o=n.querySelector(".collection-filters__price-handle--max"),a=n.querySelector(".collection-filters__price-progress"),i=n.closest(".collection-filters__price-range").querySelector('[data-input="min"]'),s=n.closest(".collection-filters__price-range").querySelector('[data-input="max"]'),e=n.closest(".collection-filters__price-range").querySelector(".collection-filters__price-display-min"),t=n.closest(".collection-filters__price-range").querySelector(".collection-filters__price-display-max"),c=0,d=parseInt(n.getAttribute("data-max"))||100,l=n.getAttribute("data-currency")||"$";var u=parseFloat(n.getAttribute("data-current-min"))||0,m=parseFloat(n.getAttribute("data-current-max"))||d;let h=parseFloat(i.value),f=parseFloat(s.value),v=(isNaN(h)&&(h=u,i.value=u),isNaN(f)&&(f=m,s.value=m),f>d&&(f=d,s.value=d),E(),w(),!1),y=null;function g(e){e.preventDefault(),v=!0,(y=e.target).classList.add("dragging");e=e.clientX||e.touches&&e.touches[0].clientX;y.setAttribute("data-start-x",e),document.body.style.userSelect="none"}function p(e){var t,a;v&&y&&(e.preventDefault(),t=(a=n.getBoundingClientRect()).width,e=e.clientX||e.touches&&e.touches[0].clientX,e=Math.max(0,Math.min(100,(e-a.left)/t*100)),a=c+(d-c)*e/100,y===r?(h=Math.min(f,Math.max(c,a)),i.value=Math.round(h)):y===o&&(f=Math.max(h,Math.min(d,a)),s.value=Math.round(f)),w(),E())}function b(){v&&(v=!1,y&&(y.classList.remove("dragging"),y=null,i.value&&i.dispatchEvent(new Event("change",{bubbles:!0})),s.value)&&s.dispatchEvent(new Event("change",{bubbles:!0})),document.body.style.userSelect="")}function w(){var e=d-c,t=0<e?(h-c)/e*100:0,e=0<e?(f-c)/e*100:100,t=Math.max(0,Math.min(100,t)),e=Math.max(0,Math.min(100,e));r.style.left=t+"%",o.style.left=e+"%",a.style.left=t+"%",a.style.width=e-t+"%"}function E(){e.textContent=L(h,l),t.textContent=L(f,l)}function L(e,t){return t+e.toFixed(2)}[r,o].forEach(e=>{e.addEventListener("mousedown",g),e.addEventListener("touchstart",g,{passive:!1})}),document.addEventListener("mousemove",p),document.addEventListener("touchmove",p,{passive:!1}),document.addEventListener("mouseup",b),document.addEventListener("touchend",b),i.addEventListener("change",function(){h=Math.max(c,Math.min(f,parseFloat(this.value)||c)),this.value=h,w(),E()}),s.addEventListener("change",function(){f=Math.max(h,Math.min(d,parseFloat(this.value)||d)),this.value=f,w(),E()})})}),(()=>{function t(){let r=document.getElementById("cart-drawer"),e=document.getElementById("cart-drawer-overlay");var t=document.querySelector(".cart-drawer__close"),a=document.querySelectorAll(".cart-icon");if(!r)return;function n(){requestAnimationFrame(()=>{r.classList.add("active"),e.classList.add("active"),document.body.style.overflow="hidden",u()})}function o(){requestAnimationFrame(()=>{r.classList.remove("active"),e.classList.remove("active"),document.body.style.overflow=""})}t&&t.addEventListener("click",o),e&&e.addEventListener("click",o),a.forEach(e=>{e.addEventListener("click",function(e){e.preventDefault(),n()})});let i=document.querySelector(".cart-drawer__items"),s=document.querySelector(".cart-drawer__empty-state"),c=document.querySelector(".cart-drawer__subtotal-price"),d=document.querySelectorAll("[data-cart-count]"),l=((t,a)=>{let n;return function(...e){clearTimeout(n),n=setTimeout(()=>{clearTimeout(n),t(...e)},a)}})(function(){r.classList.contains("active")&&r.classList.add("loading"),fetch("/cart.js").then(e=>{if(e.ok)return e.json();throw new Error("Network response was not ok")}).then(e=>{r.classList.remove("loading");let t=e.item_count;if(requestAnimationFrame(()=>{d.forEach(e=>{e.textContent=t,e.style.display=0<t?"flex":"none"})}),0===t)requestAnimationFrame(()=>{i&&(i.style.display="none"),s&&(s.style.display="block"),c&&(c.textContent=f(0))});else if(i){let o=document.createDocumentFragment();e.items.forEach(e=>{var t=document.createElement("div"),a=(t.className="cart-drawer__item",e.featured_image?e.featured_image.url:""),n=e.featured_image?e.featured_image.alt:e.title,r=e.variant_title?`<div class="cart-drawer__item-variant">${e.variant_title}</div>`:"";t.innerHTML=`
                <img src="${a}" alt="${n}" loading="lazy" class="cart-drawer__item-image">
                <div class="cart-drawer__item-details">
                  <h3 class="cart-drawer__item-title">${e.title}</h3>
                  ${r}
                  <div class="cart-drawer__item-price">${f(e.price)}</div>
                  <div class="cart-drawer__item-quantity">
                    <button type="button" class="cart-drawer__item-quantity-button" data-cart-update="${e.key}" data-cart-quantity="${e.quantity-1}">-</button>
                    <input type="number" class="cart-drawer__item-quantity-input" value="${e.quantity}" min="1" data-cart-quantity-input="${e.key}">
                    <button type="button" class="cart-drawer__item-quantity-button" data-cart-update="${e.key}" data-cart-quantity="${e.quantity+1}">+</button>
                  </div>
                  <button type="button" class="cart-drawer__item-remove" data-cart-remove="${e.key}">Remove</button>
                </div>
              `,o.appendChild(t)}),requestAnimationFrame(()=>{i.style.display="block",i.innerHTML="",i.appendChild(o),s&&(s.style.display="none"),c&&(c.textContent=f(e.total_price)),document.querySelectorAll("[data-cart-quantity-input]").forEach(e=>{e.addEventListener("change",function(){var e=this.getAttribute("data-cart-quantity-input"),t=parseInt(this.value,10);0<=t&&m(e,t)})}),document.querySelectorAll("[data-cart-update]").forEach(e=>{e.addEventListener("click",function(){var e=this.getAttribute("data-cart-update"),t=parseInt(this.getAttribute("data-cart-quantity"),10);0<=t&&m(e,t)})}),document.querySelectorAll("[data-cart-remove]").forEach(e=>{e.addEventListener("click",function(){m(this.getAttribute("data-cart-remove"),0)})})})}}).catch(e=>{console.error("Error fetching cart:",e),r.classList.remove("loading")})},300);let u=window.updateCartDrawer=function(){"function"==typeof l&&l()};function m(e,t){r.classList.contains("active")&&r.classList.add("loading");let a=new AbortController,n=setTimeout(()=>a.abort(),5e3);fetch("/cart/change.js",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({id:e,quantity:t}),signal:a.signal}).then(e=>{if(clearTimeout(n),e.ok)return e.json();throw new Error("Network response was not ok")}).then(e=>{l()}).catch(e=>{console.error("Error updating cart:",e),r.classList.remove("loading");let t=document.createElement("div");t.className="cart-drawer__error",t.textContent="There was an error updating your cart. Please try again.",setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},3e3),r.appendChild(t)})}let h=new Map;function f(e){if(h.has(e))return h.get(e);var t=window.Shopify&&window.Shopify.currency&&window.Shopify.currency.active?window.Shopify.currency.active:window.theme&&window.theme.currency?window.theme.currency:"USD";let a;return a="undefined"!=typeof Shopify&&Shopify.formatMoney?Shopify.formatMoney(e):({USD:"$",CAD:"CA$",EUR:"€",GBP:"£",AUD:"A$",JPY:"¥",INR:"₹"}[t]||t)+(e/100).toFixed(2),h.set(e,a),a}0<a.length&&(t=document.querySelector("[data-cart-count]"))&&t.textContent&&0<parseInt(t.textContent,10)&&l(),document.addEventListener("product:added",function(){l(),n()}),document.addEventListener("bfcache:restored",function(){l()});let v=()=>{document.querySelectorAll("[data-ajax-cart-form]").forEach(o=>{o&&!o.hasAttribute("data-ajax-cart-initialized")&&(o.setAttribute("data-ajax-cart-initialized","true"),o.addEventListener("submit",function(n){n.preventDefault();let r=o.querySelector('button[type="submit"]');if(r){let a=r.innerHTML;r.innerHTML="Adding...",r.disabled=!0;n=new FormData(o);let e=new AbortController,t=setTimeout(()=>e.abort(),5e3);fetch("/cart/add.js",{method:"POST",body:n,signal:e.signal}).then(e=>{if(clearTimeout(t),e.ok)return e.json();throw new Error("Network response was not ok")}).then(e=>{r.innerHTML=a,r.disabled=!1,e.status&&422===e.status?console.error("Error adding to cart:",e.description):document.dispatchEvent(new CustomEvent("product:added"))}).catch(e=>{console.error("Error adding to cart:",e),r.innerHTML=a,r.disabled=!1;let t=document.createElement("div");t.className="cart-error-message",t.textContent="There was an error adding this item to your cart. Please try again.",t.style.color="red",t.style.marginTop="10px",r.parentNode.appendChild(t),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},3e3)})}}))})};v(),new MutationObserver(e=>{for(var t of e)if("childList"===t.type&&t.addedNodes.length)for(var a of t.addedNodes)if(a.nodeType===Node.ELEMENT_NODE&&(a.matches("[data-ajax-cart-form]")||a.querySelector("[data-ajax-cart-form]"))){v();break}}).observe(document.body,{childList:!0,subtree:!0})}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",t):t(),window.addEventListener("pageshow",function(e){e.persisted&&(console.log("Cart drawer: Page restored from bfcache"),t())})})();