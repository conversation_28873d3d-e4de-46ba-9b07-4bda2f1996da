.collection-template__active-filter-item,.collection-template__active-filter-link,.collection-template__active-filter-link--clear,.collection-template__active-filter-list,.collection-template__active-filters,.collection-template__filter,.collection-template__filter-content,.collection-template__filter-item,.collection-template__filter-label,.collection-template__filter-list,.collection-template__filter-toggle,.collection-template__price-range,.collection-template__price-range-field{display:none!important}
.collection-filters{padding:var(--spacing-base);background-color:var(--color-background-light);border-radius:var(--border-radius);display:block!important}
.collection-filters__price-range{margin-top:var(--spacing-medium);padding:0 10px}
.collection-filters__price-slider{position:relative;height:6px;background-color:var(--color-border);border-radius:3px;margin:30px 10px 40px;width:calc(100% - 20px)}
.collection-filters__price-progress{position:absolute;height:100%;background-color:var(--color-text);border-radius:3px;left:0;width:100%}
.collection-filters__price-handle{width:20px;height:20px;border-radius:50%;background-color:var(--color-text);position:absolute;top:50%;transform:translate(-50%,-50%);cursor:pointer;z-index:2;box-shadow:0 1px 3px rgba(0,0,0,.2);transition:background-color .2s}
.collection-filters__price-handle.dragging,.collection-filters__price-handle:hover{background-color:var(--color-accent)}
.collection-filters__price-handle--min{left:0}
.collection-filters__price-handle--max{right:0;transform:translate(50%,-50%)}
.collection-filters__price-inputs{display:flex;justify-content:space-between;margin-top:var(--spacing-small)}
.collection-filters__price-field{width:45%}
.collection-filters__price-field label{display:block;font-size:var(--font-size-small);margin-bottom:5px;color:var(--color-text-light)}
.collection-filters__price-field input{width:100%;padding:8px;border:1px solid var(--color-border);border-radius:var(--border-radius);font-size:var(--font-size-base);text-align:center}
.collection-filters__price-display{text-align:center;margin-top:var(--spacing-small);font-size:var(--font-size-base);color:var(--color-text)}
.collection-filters__group{margin-bottom:var(--spacing-large)}
.collection-filters__group:last-child{margin-bottom:0}
.collection-filters__heading{font-size:var(--font-size-large);font-weight:var(--font-weight-bold);margin-bottom:var(--spacing-base);text-transform:none;letter-spacing:normal}
.collection-filters__checkbox-list{list-style:none;padding:0;margin:0}
.collection-filters__checkbox-item{margin-bottom:var(--spacing-small)}
.collection-filters__checkbox-label{display:flex;align-items:center;cursor:pointer;font-size:var(--font-size-base);color:var(--color-text)}
.collection-filters__checkbox-input{position:absolute;opacity:0;width:0;height:0}
.collection-filters__checkbox-custom{display:inline-block;width:20px;height:20px;margin-right:var(--spacing-small);border:1px solid var(--color-border-dark);background-color:var(--color-background);position:relative;flex-shrink:0}
.collection-filters__checkbox-input:checked+.collection-filters__checkbox-custom::after{content:'';position:absolute;top:4px;left:4px;width:10px;height:10px;background-color:var(--color-text)}
.collection-filters__checkbox-count{margin-left:auto;color:var(--color-text-light);font-size:var(--font-size-small)}
.collection-filters__color-list{display:flex;flex-wrap:wrap;gap:var(--spacing-small);list-style:none;padding:0;margin:0}
.collection-filters__color-item{margin-bottom:var(--spacing-small)}
.collection-filters__color-label{display:block;cursor:pointer}
.collection-filters__color-input{position:absolute;opacity:0;width:0;height:0}
.collection-filters__color-swatch{display:block;width:36px;height:36px;border-radius:50%;border:1px solid var(--color-border);position:relative;transition:transform var(--transition-duration-fast) var(--transition-timing-function)}
.collection-filters__color-input:checked+.collection-filters__color-swatch{border:2px solid var(--color-text);transform:scale(1.1)}
.collection-filters__size-list{display:flex;flex-wrap:wrap;gap:var(--spacing-small);list-style:none;padding:0;margin:0}
.collection-filters__size-item{margin-bottom:var(--spacing-small)}
.collection-filters__size-label{display:block;cursor:pointer}
.collection-filters__size-input{position:absolute;opacity:0;width:0;height:0}
.collection-filters__size-button{display:flex;align-items:center;justify-content:center;width:60px;height:40px;border:1px solid var(--color-border-dark);background-color:var(--color-background);font-size:var(--font-size-base);transition:all var(--transition-duration-fast) var(--transition-timing-function)}
.collection-filters__size-input:checked+.collection-filters__size-button{border-color:var(--color-text);background-color:var(--color-text);color:var(--color-background)}
.collection-filters__active{margin-top:var(--spacing-base)}
.collection-filters__active-list{display:flex;flex-wrap:wrap;gap:var(--spacing-small);list-style:none;padding:0;margin:0}
.collection-filters__active-item{display:inline-flex;align-items:center;background-color:var(--color-background);border:1px solid var(--color-border);border-radius:var(--border-radius);padding:var(--spacing-extra-small) var(--spacing-small);font-size:var(--font-size-small)}
.collection-filters__active-remove{margin-left:var(--spacing-small);cursor:pointer;color:var(--color-text-light)}
.collection-filters__active-remove:hover{color:var(--color-text)}
.collection-filters__clear-all{margin-top:var(--spacing-small);font-size:var(--font-size-small);text-decoration:underline;cursor:pointer}
.collection-filters-mobile__toggle{display:none;width:100%;padding:var(--spacing-small);background-color:var(--color-background);border:1px solid var(--color-border);text-align:left;font-size:var(--font-size-base);font-weight:var(--font-weight-medium);cursor:pointer;margin-bottom:var(--spacing-base)}
.collection-filters-mobile__toggle-icon{float:right;transition:transform var(--transition-duration-fast) var(--transition-timing-function)}
.collection-filters-mobile__toggle[aria-expanded=true] .collection-filters-mobile__toggle-icon{transform:rotate(180deg)}
@media screen and (max-width:749px){
.collection-filters-mobile__toggle{display:block}
.collection-filters{display:none}
.collection-filters.is-open{display:block}
}