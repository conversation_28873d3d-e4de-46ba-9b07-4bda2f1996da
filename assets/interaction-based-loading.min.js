(function(){"use strict";let e=!1;const t=[];function n(e,t){const n=document.createElement("script");return n.src=e,t&&(n.onload=t),document.head.appendChild(n),n}function i(){if(0===t.length)return;t.sort((e,t)=>"high"===e.priority&&"low"===t.priority?-1:"low"===e.priority&&"high"===t.priority?1:0);let e=0;t.forEach(t=>{setTimeout(()=>{n(t.url)},e),e+=100}),t.length=0}function r(){e||(e=!0,i(),document.removeEventListener("click",r),document.removeEventListener("scroll",r),document.removeEventListener("mousemove",r),document.removeEventListener("touchstart",r))}window.queueScriptLoad=function(i,r="low"){e?n(i):t.push({url:i,priority:r})},document.addEventListener("click",r),document.addEventListener("scroll",r),document.addEventListener("mousemove",r),document.addEventListener("touchstart",r),setTimeout(function(){e||(e=!0,i())},5e3)})();
