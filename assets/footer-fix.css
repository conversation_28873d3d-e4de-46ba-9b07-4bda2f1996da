/* Footer CSS fixes to prevent layout shifts */

.site-footer {
  padding: 60px 0 30px;
  background-color: var(--color-background);
  border-top: 1px solid var(--color-border);
  min-height: 300px; /* Minimum height to prevent layout shifts */
}

.footer-wrapper {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  min-height: 200px; /* Minimum height to prevent layout shifts */
}

.footer-block {
  display: flex;
  flex-direction: column;
  min-height: 150px; /* Minimum height to prevent layout shifts */
}

.footer-block__title {
  margin-bottom: 20px;
  font-size: 14px;
  text-transform: uppercase;
  min-height: 20px; /* Minimum height to prevent layout shifts */
}

.footer-nav {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 100px; /* Minimum height to prevent layout shifts */
}

.footer-nav li {
  margin-bottom: 10px;
  min-height: 20px; /* Minimum height to prevent layout shifts */
}

.footer-newsletter {
  margin-top: 15px;
  min-height: 80px; /* Minimum height to prevent layout shifts */
}

.newsletter-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: 50px; /* Minimum height to prevent layout shifts */
}

.field {
  position: relative;
  min-height: 40px; /* Minimum height to prevent layout shifts */
}

.field__input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  min-height: 40px; /* Minimum height to prevent layout shifts */
}

.footer-social-wrapper {
  margin-top: 30px;
  min-height: 50px; /* Minimum height to prevent layout shifts */
}

.footer-social {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 30px; /* Minimum height to prevent layout shifts */
}

.footer-social li {
  margin-right: 10px;
  min-width: 24px; /* Fixed width to prevent layout shifts */
  min-height: 24px; /* Fixed height to prevent layout shifts */
}

.footer-social svg {
  width: 24px;
  height: 24px;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border);
  min-height: 50px; /* Minimum height to prevent layout shifts */
}

.copyright {
  font-size: 12px;
  min-height: 20px; /* Minimum height to prevent layout shifts */
}

.payment-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  min-height: 30px; /* Minimum height to prevent layout shifts */
}

.icon--payment {
  width: 38px;
  height: 24px;
}

/* Responsive styles */
@media screen and (max-width: 989px) {
  .footer-wrapper {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media screen and (max-width: 749px) {
  .site-footer {
    padding: 40px 0 20px;
    min-height: 250px; /* Adjusted minimum height for mobile */
  }

  .footer-wrapper {
    grid-template-columns: 1fr;
    gap: 30px;
    min-height: 150px; /* Adjusted minimum height for mobile */
  }

  .footer-block {
    min-height: 100px; /* Adjusted minimum height for mobile */
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    min-height: 80px; /* Adjusted minimum height for mobile */
  }
}
