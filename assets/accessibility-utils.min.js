(()=>{class AriaHiddenManager{constructor(){this.elements=new Map,this.focusableSelector='a, button, input, select, textarea, [tabindex], audio[controls], video[controls], [contenteditable]:not([contenteditable="false"])',this.init()}init(){document.querySelectorAll('[aria-hidden="true"]').forEach(e=>{this.processAriaHiddenElement(e)}),this.setupMutationObserver()}processAriaHiddenElement(n){n.querySelectorAll(this.focusableSelector).forEach(e=>{var t;"-1"!==e.getAttribute("tabindex")&&(t=e.getAttribute("tabindex"),this.elements.has(e)||this.elements.set(e,{originalTabIndex:t,container:n}),e.setAttribute("tabindex","-1"),e.setAttribute("data-aria-hidden-tabindex","true"))})}restoreFocusableElements(n){this.elements.forEach((e,t)=>{e.container===n&&(null===e.originalTabIndex?t.removeAttribute("tabindex"):t.setAttribute("tabindex",e.originalTabIndex),t.removeAttribute("data-aria-hidden-tabindex"),this.elements.delete(t))})}setupMutationObserver(){new MutationObserver(e=>{e.forEach(e=>{"attributes"===e.type&&"aria-hidden"===e.attributeName&&("true"===(e=e.target).getAttribute("aria-hidden")?this.processAriaHiddenElement(e):this.restoreFocusableElements(e))})}).observe(document.body,{attributes:!0,attributeFilter:["aria-hidden"],subtree:!0})}}document.addEventListener("DOMContentLoaded",()=>{window.ariaHiddenManager=new AriaHiddenManager}),window.accessibilityUtils={createFocusTrap:function(e){var t=e.querySelectorAll('a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');let n=t[0],i=t[t.length-1],a=!1;function handleKeyDown(e){a&&"Tab"===e.key&&(e.shiftKey&&document.activeElement===n?(e.preventDefault(),i.focus()):e.shiftKey||document.activeElement!==i||(e.preventDefault(),n.focus()))}return{activate(){a=!0,e.addEventListener("keydown",handleKeyDown),n&&setTimeout(()=>{n.focus()},100)},deactivate(){a=!1,e.removeEventListener("keydown",handleKeyDown)}}}}})();