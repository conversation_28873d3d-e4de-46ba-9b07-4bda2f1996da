// BFCache (Back/Forward Cache) Fix
// This script helps ensure the page works properly with browser back/forward cache

(function() {
  'use strict';

  // Check if the browser supports the Page Lifecycle API
  const supportsPageLifecycle = 'onpageshow' in window;

  // Function to handle page restoration from bfcache
  function handlePageShow(event) {
    // If this is a back/forward navigation (persisted is true)
    if (event.persisted) {
      console.log('Page restored from bfcache');
      
      // Refresh dynamic content that might be stale
      refreshDynamicContent();
      
      // Re-initialize any components that need it
      reinitializeComponents();
    }
  }

  // Function to refresh dynamic content like cart counts, prices, etc.
  function refreshDynamicContent() {
    // Refresh cart data
    if (typeof updateCartDrawer === 'function') {
      updateCartDrawer();
    } else if (window.updateCartDrawer) {
      window.updateCartDrawer();
    }
    
    // Refresh any other dynamic content as needed
    // For example, product availability, prices, etc.
    
    // Dispatch a custom event that other scripts can listen for
    document.dispatchEvent(new CustomEvent('bfcache:restored'));
  }

  // Function to reinitialize components that might need it
  function reinitializeComponents() {
    // Reinitialize sliders if needed
    const sliders = document.querySelectorAll('.keen-slider');
    if (sliders.length > 0 && typeof window.initCarousels === 'function') {
      window.initCarousels();
    }
    
    // Reinitialize any other components as needed
  }

  // Register event listeners
  if (supportsPageLifecycle) {
    // Use pageshow event to detect bfcache restoration
    window.addEventListener('pageshow', handlePageShow);
  }

  // Expose functions to global scope if needed
  window.bfcacheFix = {
    refreshDynamicContent: refreshDynamicContent,
    reinitializeComponents: reinitializeComponents
  };
})();
