document.addEventListener("DOMContentLoaded",function(){let t=document.querySelector(".collection-filters-mobile__toggle"),a=document.getElementById("collection-filters");t&&a&&t.addEventListener("click",function(){var e="true"===t.getAttribute("aria-expanded");t.setAttribute("aria-expanded",!e),e?a.classList.remove("is-open"):a.classList.add("is-open")});var e,n=document.getElementById("SortBy");n&&((e=new URLSearchParams(window.location.search).get("sort_by"))&&n.querySelector(`option[value="${e}"]`)&&(n.value=e),n.addEventListener("change",function(){var e=new URL(window.location.href);e.searchParams.set("sort_by",this.value),window.Shopify&&window.Shopify.designMode?console.log("Sort change prevented in Theme Editor. Would navigate to:",e.href):window.location.href=e.href})),document.querySelectorAll("[data-filter-update]").forEach(function(t){t.addEventListener("change",function(){var e=t.closest("form");e&&(window.Shopify&&window.Shopify.designMode?console.log("Filter update prevented in Theme Editor"):e.submit())})}),document.querySelectorAll(".collection-filters__price-slider").forEach(function(n){let i=n.querySelector(".collection-filters__price-handle--min"),o=n.querySelector(".collection-filters__price-handle--max"),a=n.querySelector(".collection-filters__price-progress"),r=n.closest(".collection-filters__price-range").querySelector('[data-input="min"]'),l=n.closest(".collection-filters__price-range").querySelector('[data-input="max"]'),e=n.closest(".collection-filters__price-range").querySelector(".collection-filters__price-display-min"),t=n.closest(".collection-filters__price-range").querySelector(".collection-filters__price-display-max"),s=0,c=parseInt(n.getAttribute("data-max"))||100,d=n.getAttribute("data-currency")||"$";var u=parseFloat(n.getAttribute("data-current-min"))||0,h=parseFloat(n.getAttribute("data-current-max"))||c;let p=parseFloat(r.value),m=parseFloat(l.value),v=(isNaN(p)&&(p=u,r.value=u),isNaN(m)&&(m=h,l.value=h),m>c&&(m=c,l.value=c),updateDisplayValues(),updateSliderPositions(),!1),f=null;function startDrag(e){e.preventDefault(),v=!0,(f=e.target).classList.add("dragging");e=e.clientX||e.touches&&e.touches[0].clientX;f.setAttribute("data-start-x",e),document.body.style.userSelect="none"}function drag(e){var t,a;v&&f&&(e.preventDefault(),t=(a=n.getBoundingClientRect()).width,e=e.clientX||e.touches&&e.touches[0].clientX,e=Math.max(0,Math.min(100,(e-a.left)/t*100)),a=s+(c-s)*e/100,f===i?(p=Math.min(m,Math.max(s,a)),r.value=Math.round(p)):f===o&&(m=Math.max(p,Math.min(c,a)),l.value=Math.round(m)),updateSliderPositions(),updateDisplayValues())}function endDrag(){v&&(v=!1,f&&(f.classList.remove("dragging"),f=null,r.value&&r.dispatchEvent(new Event("change",{bubbles:!0})),l.value)&&l.dispatchEvent(new Event("change",{bubbles:!0})),document.body.style.userSelect="")}function updateSliderPositions(){var e=c-s,t=0<e?(p-s)/e*100:0,e=0<e?(m-s)/e*100:100,t=Math.max(0,Math.min(100,t)),e=Math.max(0,Math.min(100,e));i.style.left=t+"%",o.style.left=e+"%",a.style.left=t+"%",a.style.width=e-t+"%"}function updateDisplayValues(){e.textContent=formatMoney(p,d),t.textContent=formatMoney(m,d)}function formatMoney(e,t){return t+e.toFixed(2)}[i,o].forEach(e=>{e.addEventListener("mousedown",startDrag),e.addEventListener("touchstart",startDrag,{passive:!1})}),document.addEventListener("mousemove",drag),document.addEventListener("touchmove",drag,{passive:!1}),document.addEventListener("mouseup",endDrag),document.addEventListener("touchend",endDrag),r.addEventListener("change",function(){p=Math.max(s,Math.min(m,parseFloat(this.value)||s)),this.value=p,updateSliderPositions(),updateDisplayValues()}),l.addEventListener("change",function(){m=Math.max(p,Math.min(c,parseFloat(this.value)||c)),this.value=m,updateSliderPositions(),updateDisplayValues()})})});