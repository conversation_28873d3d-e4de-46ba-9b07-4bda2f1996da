// Cart Drawer JavaScript - Optimized for performance
(() => {
  // Wait for DOM to be fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initCartDrawer);
  } else {
    initCartDrawer();
  }

  // Handle bfcache restoration
  window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
      // Page was restored from bfcache
      console.log('Cart drawer: Page restored from bfcache');
      initCartDrawer();
    }
  });

  function initCartDrawer() {
    // Cart drawer elements - cache DOM queries
    const cartDrawer = document.getElementById('cart-drawer');
    const cartDrawerOverlay = document.getElementById('cart-drawer-overlay');
    const cartDrawerClose = document.querySelector('.cart-drawer__close');
    const cartIcons = document.querySelectorAll('.cart-icon');

    // Exit early if cart drawer doesn't exist
    if (!cartDrawer) return;

    // Cart drawer functions
    function openCartDrawer() {
      // Use requestAnimationFrame for smoother animation
      requestAnimationFrame(() => {
        cartDrawer.classList.add('active');
        cartDrawerOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
        updateCartDrawer();
      });
    }

    function closeCartDrawer() {
      requestAnimationFrame(() => {
        cartDrawer.classList.remove('active');
        cartDrawerOverlay.classList.remove('active');
        document.body.style.overflow = '';
      });
    }

  // Add event listeners
  if (cartDrawerClose) {
    cartDrawerClose.addEventListener('click', closeCartDrawer);
  }

  if (cartDrawerOverlay) {
    cartDrawerOverlay.addEventListener('click', closeCartDrawer);
  }

  // Add click event to cart icons
  cartIcons.forEach(icon => {
    icon.addEventListener('click', function(event) {
      event.preventDefault();
      openCartDrawer();
    });
  });

    // Cache DOM elements for better performance
    const cartItemsContainer = document.querySelector('.cart-drawer__items');
    const cartEmptyState = document.querySelector('.cart-drawer__empty-state');
    const cartSubtotalPrice = document.querySelector('.cart-drawer__subtotal-price');
    const cartCountElements = document.querySelectorAll('[data-cart-count]');

    // Use a debounced version of updateCartDrawer to prevent multiple rapid updates
    const updateCartDrawerInternal = debounce(function() {
      // Show loading state
      if (cartDrawer.classList.contains('active')) {
        cartDrawer.classList.add('loading');
      }

      fetch('/cart.js')
        .then(response => {
          if (!response.ok) throw new Error('Network response was not ok');
          return response.json();
        })
        .then(cart => {
          // Remove loading state
          cartDrawer.classList.remove('loading');

          // Update cart count with batch DOM operations
          const itemCount = cart.item_count;
          requestAnimationFrame(() => {
            cartCountElements.forEach(element => {
              element.textContent = itemCount;
              element.style.display = itemCount > 0 ? 'flex' : 'none';
            });
          });

          if (itemCount === 0) {
            // Cart is empty - batch DOM updates
            requestAnimationFrame(() => {
              if (cartItemsContainer) cartItemsContainer.style.display = 'none';
              if (cartEmptyState) cartEmptyState.style.display = 'block';
              if (cartSubtotalPrice) cartSubtotalPrice.textContent = formatMoney(0);
            });
            return;
          }

          // Cart has items
          if (cartItemsContainer) {
            // Create document fragment for better performance
            const fragment = document.createDocumentFragment();

            // Prepare HTML for all items first
            cart.items.forEach(item => {
              const itemElement = document.createElement('div');
              itemElement.className = 'cart-drawer__item';

              const itemImage = item.featured_image ? item.featured_image.url : '';
              const itemImageAlt = item.featured_image ? item.featured_image.alt : item.title;

              // Use template literals only once for better performance
              let variantInfo = item.variant_title ?
                `<div class="cart-drawer__item-variant">${item.variant_title}</div>` : '';

              itemElement.innerHTML = `
                <img src="${itemImage}" alt="${itemImageAlt}" loading="lazy" class="cart-drawer__item-image">
                <div class="cart-drawer__item-details">
                  <h3 class="cart-drawer__item-title">${item.title}</h3>
                  ${variantInfo}
                  <div class="cart-drawer__item-price">${formatMoney(item.price)}</div>
                  <div class="cart-drawer__item-quantity">
                    <button type="button" class="cart-drawer__item-quantity-button" data-cart-update="${item.key}" data-cart-quantity="${item.quantity - 1}">-</button>
                    <input type="number" class="cart-drawer__item-quantity-input" value="${item.quantity}" min="1" data-cart-quantity-input="${item.key}">
                    <button type="button" class="cart-drawer__item-quantity-button" data-cart-update="${item.key}" data-cart-quantity="${item.quantity + 1}">+</button>
                  </div>
                  <button type="button" class="cart-drawer__item-remove" data-cart-remove="${item.key}">Remove</button>
                </div>
              `;

              fragment.appendChild(itemElement);
            });

            // Batch DOM updates with requestAnimationFrame
            requestAnimationFrame(() => {
              cartItemsContainer.style.display = 'block';
              cartItemsContainer.innerHTML = '';
              cartItemsContainer.appendChild(fragment);

              if (cartEmptyState) cartEmptyState.style.display = 'none';
              if (cartSubtotalPrice) cartSubtotalPrice.textContent = formatMoney(cart.total_price);

              // Add event listeners after DOM is updated
              setupCartItemEvents();
            });
          }
        })
        .catch(error => {
          console.error('Error fetching cart:', error);
          cartDrawer.classList.remove('loading');
        });
    }, 300); // 300ms debounce time

    // Debounce helper function
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

  // Setup cart item events
  function setupCartItemEvents() {
    // Quantity input changes
    document.querySelectorAll('[data-cart-quantity-input]').forEach(input => {
      input.addEventListener('change', function() {
        const key = this.getAttribute('data-cart-quantity-input');
        const quantity = parseInt(this.value, 10);

        if (quantity >= 0) {
          updateCartItem(key, quantity);
        }
      });
    });

    // Quantity buttons
    document.querySelectorAll('[data-cart-update]').forEach(button => {
      button.addEventListener('click', function() {
        const key = this.getAttribute('data-cart-update');
        const quantity = parseInt(this.getAttribute('data-cart-quantity'), 10);

        if (quantity >= 0) {
          updateCartItem(key, quantity);
        }
      });
    });

    // Remove buttons
    document.querySelectorAll('[data-cart-remove]').forEach(button => {
      button.addEventListener('click', function() {
        const key = this.getAttribute('data-cart-remove');
        updateCartItem(key, 0);
      });
    });
  }

    // Create a public updateCartDrawer function that can be called from outside
    const updateCartDrawer = window.updateCartDrawer = function() {
      if (typeof updateCartDrawerInternal === 'function') {
        updateCartDrawerInternal();
      }
    };

    // Update cart item with optimized fetch
    function updateCartItem(key, quantity) {
      // Show loading state
      if (cartDrawer.classList.contains('active')) {
        cartDrawer.classList.add('loading');
      }

      // Use fetch with timeout for better error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      fetch('/cart/change.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          id: key,
          quantity: quantity
        }),
        signal: controller.signal
      })
      .then(response => {
        clearTimeout(timeoutId);
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
      })
      .then(cart => {
        updateCartDrawerInternal();
      })
      .catch(error => {
        console.error('Error updating cart:', error);
        cartDrawer.classList.remove('loading');

        // Show error message to user
        const errorMessage = document.createElement('div');
        errorMessage.className = 'cart-drawer__error';
        errorMessage.textContent = 'There was an error updating your cart. Please try again.';

        // Remove after 3 seconds
        setTimeout(() => {
          if (errorMessage.parentNode) {
            errorMessage.parentNode.removeChild(errorMessage);
          }
        }, 3000);

        cartDrawer.appendChild(errorMessage);
      });
    }

    // Format money helper function - memoized for performance
    const moneyCache = new Map();
    function formatMoney(cents) {
      if (moneyCache.has(cents)) {
        return moneyCache.get(cents);
      }

      // Get the currency symbol from the shop's money format
      const currencySymbol = window.Shopify && window.Shopify.currency && window.Shopify.currency.active
        ? window.Shopify.currency.active
        : (window.theme && window.theme.currency ? window.theme.currency : 'USD');

      // Format the price with the correct currency symbol
      let formatted;

      // Use Shopify's money formatter if available
      if (typeof Shopify !== 'undefined' && Shopify.formatMoney) {
        formatted = Shopify.formatMoney(cents);
      } else {
        // Fallback to basic formatting with the currency symbol
        const currencyMap = {
          'USD': '$',
          'CAD': 'CA$',
          'EUR': '€',
          'GBP': '£',
          'AUD': 'A$',
          'JPY': '¥',
          'INR': '₹',
          // Add more currencies as needed
        };

        const symbol = currencyMap[currencySymbol] || currencySymbol;
        formatted = symbol + (cents / 100).toFixed(2);
      }

      moneyCache.set(cents, formatted);
      return formatted;
    }

    // Initialize cart drawer - only if visible or needed
    if (cartIcons.length > 0) {
      // Lazy initialize - only fetch cart data when needed
      const cartCount = document.querySelector('[data-cart-count]');
      if (cartCount && cartCount.textContent && parseInt(cartCount.textContent, 10) > 0) {
        // Cart has items, initialize
        updateCartDrawerInternal();
      }
    }

    // Listen for add to cart events
    document.addEventListener('product:added', function() {
      updateCartDrawerInternal();
      openCartDrawer();
    });

    // Listen for bfcache restoration events
    document.addEventListener('bfcache:restored', function() {
      updateCartDrawerInternal();
    });

    // Initialize AJAX cart forms with optimized event delegation
    const initializeAjaxCartForms = () => {
      document.querySelectorAll('[data-ajax-cart-form]').forEach(form => {
        if (form && !form.hasAttribute('data-ajax-cart-initialized')) {
          form.setAttribute('data-ajax-cart-initialized', 'true');

          form.addEventListener('submit', function(event) {
            event.preventDefault();

            // Get submit button
            const submitButton = form.querySelector('button[type="submit"]');
            if (!submitButton) return;

            // Store original button text
            const originalButtonText = submitButton.innerHTML;

            // Change button text to loading state
            submitButton.innerHTML = 'Adding...';
            submitButton.disabled = true;

            // Use FormData for better performance
            const formData = new FormData(form);

            // Use fetch with timeout for better error handling
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            fetch('/cart/add.js', {
              method: 'POST',
              body: formData,
              signal: controller.signal
            })
            .then(response => {
              clearTimeout(timeoutId);
              if (!response.ok) throw new Error('Network response was not ok');
              return response.json();
            })
            .then(data => {
              // Reset button
              submitButton.innerHTML = originalButtonText;
              submitButton.disabled = false;

              if (data.status && data.status === 422) {
                // Handle error
                console.error('Error adding to cart:', data.description);
                return;
              }

              // Dispatch an event that the cart-drawer.js can listen for
              document.dispatchEvent(new CustomEvent('product:added'));
            })
            .catch(error => {
              console.error('Error adding to cart:', error);
              submitButton.innerHTML = originalButtonText;
              submitButton.disabled = false;

              // Show error message
              const errorMessage = document.createElement('div');
              errorMessage.className = 'cart-error-message';
              errorMessage.textContent = 'There was an error adding this item to your cart. Please try again.';
              errorMessage.style.color = 'red';
              errorMessage.style.marginTop = '10px';

              // Insert error message after the button
              submitButton.parentNode.appendChild(errorMessage);

              // Remove after 3 seconds
              setTimeout(() => {
                if (errorMessage.parentNode) {
                  errorMessage.parentNode.removeChild(errorMessage);
                }
              }, 3000);
            });
          });
        }
      });
    };

    // Initialize forms now
    initializeAjaxCartForms();

    // Also initialize any forms that might be added to the DOM later
    // Use a MutationObserver for better performance than polling
    const formObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length) {
          for (const node of mutation.addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check if the added node is a form or contains forms
              if (node.matches('[data-ajax-cart-form]') || node.querySelector('[data-ajax-cart-form]')) {
                initializeAjaxCartForms();
                break;
              }
            }
          }
        }
      }
    });

    // Start observing the document with the configured parameters
    formObserver.observe(document.body, { childList: true, subtree: true });
  }
})();
