.button,.shopify-payment-button .shopify-payment-button__button--unbranded{display:inline-flex;align-items:center;justify-content:center;gap:var(--spacing-small);padding:14px 28px;font-family:var(--font-body);font-weight:var(--font-weight-medium);font-size:var(--font-size-small);line-height:1;text-align:center;text-transform:uppercase;letter-spacing:.05em;background-color:var(--color-button);color:var(--color-button-text);border:none;border-radius:var(--border-radius-button);cursor:pointer;transition:background-color var(--transition-duration) var(--transition-timing),transform var(--transition-duration) var(--transition-timing);position:relative;min-width:160px}
.button:hover,.shopify-payment-button .shopify-payment-button__button--unbranded:hover{background-color:var(--color-button-hover);transform:translateY(-2px)}
.button:active,.shopify-payment-button .shopify-payment-button__button--unbranded:active{transform:translateY(0)}
.button--with-icon::before{content:'';display:inline-block;width:20px;height:20px;background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M5 12h14'%3E%3C/path%3E%3Cpath d='M12 5l7 7-7 7'%3E%3C/path%3E%3C/svg%3E");background-repeat:no-repeat;background-position:center;background-size:contain}
.button--secondary{background-color:transparent;color:var(--color-button);border:var(--border-width) solid var(--color-button)}
.button--secondary:hover{background-color:var(--color-button);color:var(--color-button-text)}
.button--small{padding:10px 20px;font-size:var(--font-size-smaller);min-width:120px}
.button--large{padding:16px 32px;font-size:var(--font-size-large);min-width:200px}
.button--full-width{width:100%;justify-content:center}
.button--disabled,.button--disabled:hover,.button[disabled],.button[disabled]:hover{background-color:var(--color-border-dark);color:var(--color-text-lighter);cursor:not-allowed;transform:none}
.tab-button,.tabs__button{border-radius:0;background-color:transparent;color:inherit;min-width:auto}
.tab-button:hover,.tabs__button:hover{transform:none}
@media screen and (max-width:var(--breakpoint-medium)){
.button,.shopify-payment-button .shopify-payment-button__button--unbranded{padding:12px 24px;font-size:calc(var(--font-size-small) - 1px);min-width:140px}
.button--small{padding:8px 16px;font-size:calc(var(--font-size-smaller) - 1px);min-width:100px}
.button--large{padding:14px 28px;font-size:calc(var(--font-size-base) - 1px);min-width:180px}
}