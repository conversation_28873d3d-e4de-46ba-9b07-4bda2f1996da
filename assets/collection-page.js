// Collection Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
  // Mobile filter toggle
  const filterToggle = document.querySelector('.collection-filters-mobile__toggle');
  const filterContainer = document.getElementById('collection-filters');

  if (filterToggle && filterContainer) {
    filterToggle.addEventListener('click', function() {
      const isExpanded = filterToggle.getAttribute('aria-expanded') === 'true';
      filterToggle.setAttribute('aria-expanded', !isExpanded);

      if (isExpanded) {
        filterContainer.classList.remove('is-open');
      } else {
        filterContainer.classList.add('is-open');
      }
    });
  }

  // Sort by functionality
  const sortSelect = document.getElementById('SortBy');
  if (sortSelect) {
    // Set the current sort value on page load
    const urlParams = new URLSearchParams(window.location.search);
    const currentSort = urlParams.get('sort_by');
    
    if (currentSort && sortSelect.querySelector(`option[value="${currentSort}"]`)) {
      sortSelect.value = currentSort;
    }

    sortSelect.addEventListener('change', function() {
      const url = new URL(window.location.href);
      url.searchParams.set('sort_by', this.value);

      // If we're in the Shopify Theme Editor, don't redirect
      if (window.Shopify && window.Shopify.designMode) {
        console.log('Sort change prevented in Theme Editor. Would navigate to:', url.href);
        return;
      }

      window.location.href = url.href;
    });
  }

  // Filter update functionality
  const filterInputs = document.querySelectorAll('[data-filter-update]');
  filterInputs.forEach(function(input) {
    input.addEventListener('change', function() {
      const form = input.closest('form');
      if (form) {
        // If we're in the Shopify Theme Editor, don't submit
        if (window.Shopify && window.Shopify.designMode) {
          console.log('Filter update prevented in Theme Editor');
          return;
        }

        form.submit();
      }
    });
  });

  // Price range slider functionality
  initPriceRangeSliders();

  function initPriceRangeSliders() {
    const priceSliders = document.querySelectorAll('.collection-filters__price-slider');

    priceSliders.forEach(function(slider) {
      const minHandle = slider.querySelector('.collection-filters__price-handle--min');
      const maxHandle = slider.querySelector('.collection-filters__price-handle--max');
      const progress = slider.querySelector('.collection-filters__price-progress');
      const minInput = slider.closest('.collection-filters__price-range').querySelector('[data-input="min"]');
      const maxInput = slider.closest('.collection-filters__price-range').querySelector('[data-input="max"]');
      const minDisplay = slider.closest('.collection-filters__price-range').querySelector('.collection-filters__price-display-min');
      const maxDisplay = slider.closest('.collection-filters__price-range').querySelector('.collection-filters__price-display-max');

      // Get min and max values from data attributes
      const minValue = 0;
      const maxValue = parseInt(slider.getAttribute('data-max')) || 100;
      const currency = slider.getAttribute('data-currency') || '$';

      // Get current values from data attributes
      const currentMinFromData = parseFloat(slider.getAttribute('data-current-min')) || 0;
      const currentMaxFromData = parseFloat(slider.getAttribute('data-current-max')) || maxValue;

      // Set initial values from inputs or data attributes
      let currentMinValue = parseFloat(minInput.value);
      let currentMaxValue = parseFloat(maxInput.value);

      // If no values are set in inputs, use the data attribute values
      if (isNaN(currentMinValue)) {
        currentMinValue = currentMinFromData;
        minInput.value = currentMinFromData;
      }

      if (isNaN(currentMaxValue)) {
        currentMaxValue = currentMaxFromData;
        maxInput.value = currentMaxFromData;
      }

      // Ensure max value is not greater than the highest price
      if (currentMaxValue > maxValue) {
        currentMaxValue = maxValue;
        maxInput.value = maxValue;
      }

      // Update display values immediately
      updateDisplayValues();

      // Initialize slider positions
      updateSliderPositions();

      // Handle dragging
      let isDragging = false;
      let activeHandle = null;

      // Mouse events for handles
      [minHandle, maxHandle].forEach(handle => {
        handle.addEventListener('mousedown', startDrag);
        handle.addEventListener('touchstart', startDrag, { passive: false });
      });

      document.addEventListener('mousemove', drag);
      document.addEventListener('touchmove', drag, { passive: false });
      document.addEventListener('mouseup', endDrag);
      document.addEventListener('touchend', endDrag);

      // Input change events
      minInput.addEventListener('change', function() {
        currentMinValue = Math.max(minValue, Math.min(currentMaxValue, parseFloat(this.value) || minValue));
        this.value = currentMinValue;
        updateSliderPositions();
        updateDisplayValues();
      });

      maxInput.addEventListener('change', function() {
        currentMaxValue = Math.max(currentMinValue, Math.min(maxValue, parseFloat(this.value) || maxValue));
        this.value = currentMaxValue;
        updateSliderPositions();
        updateDisplayValues();
      });

      function startDrag(e) {
        e.preventDefault();
        isDragging = true;
        activeHandle = e.target;

        // Add dragging class
        activeHandle.classList.add('dragging');

        // Capture initial position
        const initialX = e.clientX || (e.touches && e.touches[0].clientX);
        activeHandle.setAttribute('data-start-x', initialX);

        // Prevent text selection during drag
        document.body.style.userSelect = 'none';
      }

      function drag(e) {
        if (!isDragging || !activeHandle) return;

        e.preventDefault();

        const sliderRect = slider.getBoundingClientRect();
        const sliderWidth = sliderRect.width;
        const x = e.clientX || (e.touches && e.touches[0].clientX);

        // Calculate position as percentage of slider width
        let percentage = Math.max(0, Math.min(100, ((x - sliderRect.left) / sliderWidth) * 100));

        // Convert percentage to value
        const value = minValue + ((maxValue - minValue) * percentage / 100);

        // Update the appropriate value based on which handle is active
        if (activeHandle === minHandle) {
          // Ensure min value stays within bounds and doesn't exceed max value
          currentMinValue = Math.min(currentMaxValue, Math.max(minValue, value));
          minInput.value = Math.round(currentMinValue);
        } else if (activeHandle === maxHandle) {
          // Ensure max value stays within bounds and doesn't go below min value
          currentMaxValue = Math.max(currentMinValue, Math.min(maxValue, value));
          maxInput.value = Math.round(currentMaxValue);
        }

        updateSliderPositions();
        updateDisplayValues();
      }

      function endDrag() {
        if (!isDragging) return;

        isDragging = false;

        if (activeHandle) {
          activeHandle.classList.remove('dragging');
          activeHandle = null;

          // Trigger change event on inputs to update the filter
          if (minInput.value) {
            minInput.dispatchEvent(new Event('change', { bubbles: true }));
          }

          if (maxInput.value) {
            maxInput.dispatchEvent(new Event('change', { bubbles: true }));
          }
        }

        // Restore text selection
        document.body.style.userSelect = '';
      }

      function updateSliderPositions() {
        // Calculate percentages - ensure we don't divide by zero
        const range = maxValue - minValue;
        const minPercentage = range > 0 ? ((currentMinValue - minValue) / range) * 100 : 0;
        const maxPercentage = range > 0 ? ((currentMaxValue - minValue) / range) * 100 : 100;

        // Ensure percentages are within bounds
        const safeMinPercentage = Math.max(0, Math.min(100, minPercentage));
        const safeMaxPercentage = Math.max(0, Math.min(100, maxPercentage));

        // Update handle positions
        minHandle.style.left = safeMinPercentage + '%';
        maxHandle.style.left = safeMaxPercentage + '%';

        // Update progress bar
        progress.style.left = safeMinPercentage + '%';
        progress.style.width = (safeMaxPercentage - safeMinPercentage) + '%';
      }

      function updateDisplayValues() {
        // Format currency values
        minDisplay.textContent = formatMoney(currentMinValue, currency);
        maxDisplay.textContent = formatMoney(currentMaxValue, currency);
      }

      function formatMoney(amount, currency) {
        return currency + amount.toFixed(2);
      }
    });
  }
});
