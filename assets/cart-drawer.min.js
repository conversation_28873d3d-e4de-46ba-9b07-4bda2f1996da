(()=>{function initCartDrawer(){let n=document.getElementById("cart-drawer"),t=document.getElementById("cart-drawer-overlay");var e=document.querySelector(".cart-drawer__close"),a=document.querySelectorAll(".cart-icon");if(!n)return;function openCartDrawer(){requestAnimationFrame(()=>{n.classList.add("active"),t.classList.add("active"),document.body.style.overflow="hidden",s()})}function closeCartDrawer(){requestAnimationFrame(()=>{n.classList.remove("active"),t.classList.remove("active"),document.body.style.overflow=""})}e&&e.addEventListener("click",closeCartDrawer),t&&t.addEventListener("click",closeCartDrawer),a.forEach(t=>{t.addEventListener("click",function(t){t.preventDefault(),openCartDrawer()})});let r=document.querySelector(".cart-drawer__items"),i=document.querySelector(".cart-drawer__empty-state"),c=document.querySelector(".cart-drawer__subtotal-price"),o=document.querySelectorAll("[data-cart-count]"),d=((e,a)=>{let r;return function(...t){clearTimeout(r),r=setTimeout(()=>{clearTimeout(r),e(...t)},a)}})(function(){n.classList.contains("active")&&n.classList.add("loading"),fetch("/cart.js").then(t=>{if(t.ok)return t.json();throw new Error("Network response was not ok")}).then(t=>{n.classList.remove("loading");let e=t.item_count;if(requestAnimationFrame(()=>{o.forEach(t=>{t.textContent=e,t.style.display=0<e?"flex":"none"})}),0===e)requestAnimationFrame(()=>{r&&(r.style.display="none"),i&&(i.style.display="block"),c&&(c.textContent=formatMoney(0))});else if(r){let o=document.createDocumentFragment();t.items.forEach(t=>{var e=document.createElement("div"),a=(e.className="cart-drawer__item",t.featured_image?t.featured_image.url:""),r=t.featured_image?t.featured_image.alt:t.title,n=t.variant_title?`<div class="cart-drawer__item-variant">${t.variant_title}</div>`:"";e.innerHTML=`
                <img src="${a}" alt="${r}" loading="lazy" class="cart-drawer__item-image">
                <div class="cart-drawer__item-details">
                  <h3 class="cart-drawer__item-title">${t.title}</h3>
                  ${n}
                  <div class="cart-drawer__item-price">${formatMoney(t.price)}</div>
                  <div class="cart-drawer__item-quantity">
                    <button type="button" class="cart-drawer__item-quantity-button" data-cart-update="${t.key}" data-cart-quantity="${t.quantity-1}">-</button>
                    <input type="number" class="cart-drawer__item-quantity-input" value="${t.quantity}" min="1" data-cart-quantity-input="${t.key}">
                    <button type="button" class="cart-drawer__item-quantity-button" data-cart-update="${t.key}" data-cart-quantity="${t.quantity+1}">+</button>
                  </div>
                  <button type="button" class="cart-drawer__item-remove" data-cart-remove="${t.key}">Remove</button>
                </div>
              `,o.appendChild(e)}),requestAnimationFrame(()=>{r.style.display="block",r.innerHTML="",r.appendChild(o),i&&(i.style.display="none"),c&&(c.textContent=formatMoney(t.total_price)),document.querySelectorAll("[data-cart-quantity-input]").forEach(t=>{t.addEventListener("change",function(){var t=this.getAttribute("data-cart-quantity-input"),e=parseInt(this.value,10);0<=e&&updateCartItem(t,e)})}),document.querySelectorAll("[data-cart-update]").forEach(t=>{t.addEventListener("click",function(){var t=this.getAttribute("data-cart-update"),e=parseInt(this.getAttribute("data-cart-quantity"),10);0<=e&&updateCartItem(t,e)})}),document.querySelectorAll("[data-cart-remove]").forEach(t=>{t.addEventListener("click",function(){updateCartItem(this.getAttribute("data-cart-remove"),0)})})})}}).catch(t=>{console.error("Error fetching cart:",t),n.classList.remove("loading")})},300);let s=window.updateCartDrawer=function(){"function"==typeof d&&d()};function updateCartItem(t,e){n.classList.contains("active")&&n.classList.add("loading");let a=new AbortController,r=setTimeout(()=>a.abort(),5e3);fetch("/cart/change.js",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({id:t,quantity:e}),signal:a.signal}).then(t=>{if(clearTimeout(r),t.ok)return t.json();throw new Error("Network response was not ok")}).then(t=>{d()}).catch(t=>{console.error("Error updating cart:",t),n.classList.remove("loading");let e=document.createElement("div");e.className="cart-drawer__error",e.textContent="There was an error updating your cart. Please try again.",setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},3e3),n.appendChild(e)})}let l=new Map;function formatMoney(t){if(l.has(t))return l.get(t);var e=window.Shopify&&window.Shopify.currency&&window.Shopify.currency.active?window.Shopify.currency.active:window.theme&&window.theme.currency?window.theme.currency:"USD";let a;return a="undefined"!=typeof Shopify&&Shopify.formatMoney?Shopify.formatMoney(t):({USD:"$",CAD:"CA$",EUR:"€",GBP:"£",AUD:"A$",JPY:"¥",INR:"₹"}[e]||e)+(t/100).toFixed(2),l.set(t,a),a}0<a.length&&(e=document.querySelector("[data-cart-count]"))&&e.textContent&&0<parseInt(e.textContent,10)&&d(),document.addEventListener("product:added",function(){d(),openCartDrawer()}),document.addEventListener("bfcache:restored",function(){d()});let u=()=>{document.querySelectorAll("[data-ajax-cart-form]").forEach(o=>{o&&!o.hasAttribute("data-ajax-cart-initialized")&&(o.setAttribute("data-ajax-cart-initialized","true"),o.addEventListener("submit",function(r){r.preventDefault();let n=o.querySelector('button[type="submit"]');if(n){let a=n.innerHTML;n.innerHTML="Adding...",n.disabled=!0;r=new FormData(o);let t=new AbortController,e=setTimeout(()=>t.abort(),5e3);fetch("/cart/add.js",{method:"POST",body:r,signal:t.signal}).then(t=>{if(clearTimeout(e),t.ok)return t.json();throw new Error("Network response was not ok")}).then(t=>{n.innerHTML=a,n.disabled=!1,t.status&&422===t.status?console.error("Error adding to cart:",t.description):document.dispatchEvent(new CustomEvent("product:added"))}).catch(t=>{console.error("Error adding to cart:",t),n.innerHTML=a,n.disabled=!1;let e=document.createElement("div");e.className="cart-error-message",e.textContent="There was an error adding this item to your cart. Please try again.",e.style.color="red",e.style.marginTop="10px",n.parentNode.appendChild(e),setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},3e3)})}}))})};u(),new MutationObserver(t=>{for(var e of t)if("childList"===e.type&&e.addedNodes.length)for(var a of e.addedNodes)if(a.nodeType===Node.ELEMENT_NODE&&(a.matches("[data-ajax-cart-form]")||a.querySelector("[data-ajax-cart-form]"))){u();break}}).observe(document.body,{childList:!0,subtree:!0})}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",initCartDrawer):initCartDrawer(),window.addEventListener("pageshow",function(t){t.persisted&&(console.log("Cart drawer: Page restored from bfcache"),initCartDrawer())})})();