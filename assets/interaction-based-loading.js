// Interaction-Based Script Loading
// This script loads non-critical JavaScript only when the user interacts with the page

(function() {
  'use strict';
  
  // Track if user has interacted with the page
  let userHasInteracted = false;
  
  // Queue of scripts to load after interaction
  const scriptQueue = [];
  
  // Function to load a script
  function loadScript(url, callback) {
    const script = document.createElement('script');
    script.src = url;
    
    if (callback) {
      script.onload = callback;
    }
    
    document.head.appendChild(script);
    return script;
  }
  
  // Function to add a script to the queue
  window.queueScriptLoad = function(url, priority = 'low') {
    // If user has already interacted, load immediately
    if (userHasInteracted) {
      loadScript(url);
      return;
    }
    
    // Otherwise, add to queue
    scriptQueue.push({
      url: url,
      priority: priority
    });
  };
  
  // Function to process the script queue
  function processScriptQueue() {
    if (scriptQueue.length === 0) return;
    
    // Sort by priority (high first, then low)
    scriptQueue.sort((a, b) => {
      if (a.priority === 'high' && b.priority === 'low') return -1;
      if (a.priority === 'low' && b.priority === 'high') return 1;
      return 0;
    });
    
    // Load scripts with a small delay between each
    let delay = 0;
    scriptQueue.forEach(script => {
      setTimeout(() => {
        loadScript(script.url);
      }, delay);
      delay += 100; // 100ms delay between scripts
    });
    
    // Clear the queue
    scriptQueue.length = 0;
  }
  
  // Function to handle user interaction
  function handleUserInteraction() {
    if (userHasInteracted) return;
    
    userHasInteracted = true;
    
    // Process the script queue
    processScriptQueue();
    
    // Remove event listeners
    document.removeEventListener('click', handleUserInteraction);
    document.removeEventListener('scroll', handleUserInteraction);
    document.removeEventListener('mousemove', handleUserInteraction);
    document.removeEventListener('touchstart', handleUserInteraction);
  }
  
  // Add event listeners for user interaction
  document.addEventListener('click', handleUserInteraction);
  document.addEventListener('scroll', handleUserInteraction);
  document.addEventListener('mousemove', handleUserInteraction);
  document.addEventListener('touchstart', handleUserInteraction);
  
  // Also process queue after a timeout, even if no interaction
  setTimeout(function() {
    if (!userHasInteracted) {
      userHasInteracted = true;
      processScriptQueue();
    }
  }, 5000); // 5 second timeout
})();
