/**
 * Accessibility Utilities
 * 
 * This file contains utility functions to improve accessibility
 * across the theme, particularly for handling aria-hidden elements
 * and their focusable descendants.
 */

(function() {
  'use strict';

  /**
   * Manages focusable elements within aria-hidden containers
   * 
   * When an element is hidden with aria-hidden="true", any focusable
   * descendants should also be made unfocusable to prevent screen reader
   * users from accessing hidden content.
   */
  class AriaHiddenManager {
    constructor() {
      // Store references to elements and their original tabindex values
      this.elements = new Map();
      
      // Selector for all potentially focusable elements
      this.focusableSelector = 'a, button, input, select, textarea, [tabindex], audio[controls], video[controls], [contenteditable]:not([contenteditable="false"])';
      
      // Initialize
      this.init();
    }
    
    /**
     * Initialize the manager
     */
    init() {
      // Find all elements with aria-hidden="true"
      const ariaHiddenElements = document.querySelectorAll('[aria-hidden="true"]');
      
      // Process each aria-hidden element
      ariaHiddenElements.forEach(container => {
        this.processAriaHiddenElement(container);
      });
      
      // Set up mutation observer to watch for changes
      this.setupMutationObserver();
    }
    
    /**
     * Process an aria-hidden element to make its focusable descendants unfocusable
     * @param {HTMLElement} container - The container with aria-hidden="true"
     */
    processAriaHiddenElement(container) {
      // Find all focusable descendants
      const focusableElements = container.querySelectorAll(this.focusableSelector);
      
      // Make each focusable element unfocusable
      focusableElements.forEach(element => {
        // Skip elements that already have tabindex="-1"
        if (element.getAttribute('tabindex') === '-1') {
          return;
        }
        
        // Store the original tabindex
        const originalTabIndex = element.getAttribute('tabindex');
        
        // Add to our map for tracking
        if (!this.elements.has(element)) {
          this.elements.set(element, {
            originalTabIndex,
            container
          });
        }
        
        // Set tabindex to -1 to make it unfocusable
        element.setAttribute('tabindex', '-1');
        
        // Add a data attribute for debugging
        element.setAttribute('data-aria-hidden-tabindex', 'true');
      });
    }
    
    /**
     * Restore focusability to elements when aria-hidden is removed
     * @param {HTMLElement} container - The container that had aria-hidden removed
     */
    restoreFocusableElements(container) {
      // Find all elements we've modified that belong to this container
      this.elements.forEach((data, element) => {
        if (data.container === container) {
          // Restore original tabindex
          if (data.originalTabIndex === null) {
            element.removeAttribute('tabindex');
          } else {
            element.setAttribute('tabindex', data.originalTabIndex);
          }
          
          // Remove our data attribute
          element.removeAttribute('data-aria-hidden-tabindex');
          
          // Remove from our map
          this.elements.delete(element);
        }
      });
    }
    
    /**
     * Set up a mutation observer to watch for changes to aria-hidden attributes
     */
    setupMutationObserver() {
      // Create a new observer
      const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          // Check if the mutation is for an attribute
          if (mutation.type === 'attributes' && mutation.attributeName === 'aria-hidden') {
            const target = mutation.target;
            const isHidden = target.getAttribute('aria-hidden') === 'true';
            
            if (isHidden) {
              // Element was hidden, process it
              this.processAriaHiddenElement(target);
            } else {
              // Element was unhidden, restore focusability
              this.restoreFocusableElements(target);
            }
          }
        });
      });
      
      // Start observing the document
      observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['aria-hidden'],
        subtree: true
      });
    }
  }

  // Initialize the manager when the DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    window.ariaHiddenManager = new AriaHiddenManager();
  });

  /**
   * Trap focus within a modal or dialog
   * @param {HTMLElement} container - The container to trap focus within
   * @returns {Object} - Methods to activate and deactivate the focus trap
   */
  function createFocusTrap(container) {
    const focusableElements = container.querySelectorAll(
      'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    let active = false;
    
    function handleKeyDown(e) {
      if (!active || e.key !== 'Tab') return;
      
      // If shift + tab and on first element, move to last element
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
      // If tab and on last element, move to first element
      else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
    
    return {
      activate() {
        active = true;
        container.addEventListener('keydown', handleKeyDown);
        // Focus the first element when activated
        if (firstElement) {
          setTimeout(() => {
            firstElement.focus();
          }, 100);
        }
      },
      
      deactivate() {
        active = false;
        container.removeEventListener('keydown', handleKeyDown);
      }
    };
  }

  // Expose utilities to global scope
  window.accessibilityUtils = {
    createFocusTrap
  };
})();
