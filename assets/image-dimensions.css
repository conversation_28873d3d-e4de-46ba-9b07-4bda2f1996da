/* Image Dimensions CSS
 * This file contains styles to prevent layout shifts by setting proper dimensions for images
 */

/* Common image container class */
.image-container {
  position: relative;
  overflow: hidden;
  background-color: #f7f7f7; /* Light background as placeholder */
}

/* Square aspect ratio (1:1) */
.image-container--square {
  aspect-ratio: 1 / 1;
}

/* Portrait aspect ratio (2:3) */
.image-container--portrait {
  aspect-ratio: 2 / 3;
}

/* Landscape aspect ratio (16:9) */
.image-container--landscape {
  aspect-ratio: 16 / 9;
}

/* Product aspect ratio (typically 1:1.25) */
.image-container--product {
  aspect-ratio: 1 / 1.25;
}

/* Banner aspect ratio (typically 3:1) */
.image-container--banner {
  aspect-ratio: 3 / 1;
}

/* Logo aspect ratio (typically 3:2) */
.image-container--logo {
  aspect-ratio: 3 / 2;
}

/* Fallback for browsers that don't support aspect-ratio */
@supports not (aspect-ratio: 1 / 1) {
  .image-container--square {
    padding-top: 100%;
  }
  
  .image-container--portrait {
    padding-top: 150%;
  }
  
  .image-container--landscape {
    padding-top: 56.25%;
  }
  
  .image-container--product {
    padding-top: 125%;
  }
  
  .image-container--banner {
    padding-top: 33.33%;
  }
  
  .image-container--logo {
    padding-top: 66.67%;
  }
}

/* Common image styles */
.image-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* For images that should contain rather than cover */
.image-contain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* For images that should maintain their original dimensions */
.image-original {
  display: block;
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
}

/* Placeholder styles */
.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f7f7f7;
  z-index: 1;
}

/* Loading animation */
@keyframes imagePlaceholderShimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.image-loading {
  animation-duration: 1.25s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: imagePlaceholderShimmer;
  animation-timing-function: linear;
  background: linear-gradient(to right, #f7f7f7 8%, #eeeeee 18%, #f7f7f7 33%);
  background-size: 800px 104px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* Hide loading animation when image is loaded */
img.is-loaded + .image-loading {
  display: none;
}

/* Specific component overrides */

/* Product grid items */
.product-grid__item .image-container {
  width: 100%;
  margin-bottom: 15px;
}

/* Collection grid items */
.category-item__image-container {
  width: 100%;
}

/* Hero banner */
.hero-banner__image-container {
  width: 100%;
}

/* Logo items */
.logo-carousel__logo-wrapper {
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
}

/* Responsive adjustments */
@media screen and (max-width: 749px) {
  .image-container--banner {
    aspect-ratio: 2 / 1; /* Shorter banner on mobile */
  }
  
  @supports not (aspect-ratio: 1 / 1) {
    .image-container--banner {
      padding-top: 50%;
    }
  }
}
