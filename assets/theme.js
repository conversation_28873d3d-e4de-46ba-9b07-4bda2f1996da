// Levo Theme JavaScript
// Focus on performance and minimal size

(function() {
  'use strict';

  // Utility functions
  const utils = {
    // DOM ready function
    ready: function(callback) {
      if (document.readyState !== 'loading') {
        callback();
      } else {
        document.addEventListener('DOMContentLoaded', callback);
      }
    },

    // Event delegation
    on: function(element, event, selector, callback) {
      element.addEventListener(event, function(e) {
        const target = e.target.closest(selector);
        if (target) {
          callback(e, target);
        }
      });
    },

    // Get all elements matching selector
    all: function(selector, parent = document) {
      return Array.from(parent.querySelectorAll(selector));
    },

    // Get first element matching selector
    get: function(selector, parent = document) {
      return parent.querySelector(selector);
    }
  };

  // Theme modules
  const theme = {
    // Initialize all modules
    init: function() {
      this.productGallery.init();
      this.quantityAdjuster.init();
      this.variantSelector.init();
      this.mobileMenu.init();
      this.lazyLoad.init();
    },

    // Product image gallery
    productGallery: {
      init: function() {
        const galleryContainer = utils.get('.product-gallery');
        if (!galleryContainer) return;

        const thumbnails = utils.all('.product-thumbnail');
        if (thumbnails.length === 0) return;

        thumbnails.forEach(thumbnail => {
          thumbnail.addEventListener('click', () => {
            const mediaId = thumbnail.getAttribute('data-thumbnail-id');
            const mediaItem = utils.get(`.product-gallery__item[data-media-id="${mediaId}"]`);

            if (mediaItem) {
              // Hide all media items
              utils.all('.product-gallery__item').forEach(item => {
                item.style.display = 'none';
              });

              // Show selected media item
              mediaItem.style.display = 'block';

              // Update active thumbnail
              thumbnails.forEach(thumb => {
                thumb.classList.remove('active');
              });
              thumbnail.classList.add('active');
            }
          });
        });

        // Show first media item by default
        if (thumbnails.length > 0) {
          thumbnails[0].click();
        }
      }
    },

    // Quantity adjuster
    quantityAdjuster: {
      init: function() {
        const quantityInputs = utils.all('.quantity-input');

        quantityInputs.forEach(input => {
          // Ensure minimum value is respected
          input.addEventListener('change', () => {
            const min = parseInt(input.getAttribute('min'), 10) || 1;
            if (input.value < min) {
              input.value = min;
            }
          });
        });
      }
    },

    // Variant selector
    variantSelector: {
      init: function() {
        const productContainer = utils.get('[data-section-type="product"]');
        if (!productContainer) return;

        const productId = productContainer.getAttribute('data-product-id');
        if (!productId) return;

        const variantSelects = utils.all('.product-form__select', productContainer);
        if (variantSelects.length === 0) return;

        // Get product JSON
        fetch(`/products/${productId}.js`)
          .then(response => response.json())
          .then(product => {
            this.setupVariantSelectors(product, variantSelects);
          })
          .catch(error => console.error('Error loading product data:', error));
      },

      setupVariantSelectors: function(product, selects) {
        const variants = product.variants;

        // Update variant on select change
        selects.forEach(select => {
          select.addEventListener('change', () => {
            this.updateVariantSelection(product, selects);
          });
        });

        // Initial update
        this.updateVariantSelection(product, selects);
      },

      updateVariantSelection: function(product, selects) {
        // Get currently selected options
        const selectedOptions = selects.map(select => select.value);

        // Find the variant that matches the selected options
        const selectedVariant = product.variants.find(variant => {
          return variant.options.every((option, index) => {
            return option === selectedOptions[index];
          });
        });

        if (selectedVariant) {
          // Update variant ID in the form
          const idInput = utils.get('input[name="id"]');
          if (idInput) {
            idInput.value = selectedVariant.id;
          }

          // Update price
          const regularPrice = utils.get('[data-regular-price]');
          if (regularPrice) {
            regularPrice.textContent = this.formatMoney(selectedVariant.price);
          }

          // Update add to cart button
          const addToCartButton = utils.get('.product-form__submit');
          if (addToCartButton) {
            if (selectedVariant.available) {
              addToCartButton.disabled = false;
              addToCartButton.textContent = theme.strings.addToCart || 'Add to cart';
            } else {
              addToCartButton.disabled = true;
              addToCartButton.textContent = theme.strings.soldOut || 'Sold out';
            }
          }
        }
      },

      formatMoney: function(cents) {
        return '$' + (cents / 100).toFixed(2);
      }
    },

    // Mobile menu
    mobileMenu: {
      init: function() {
        // Implementation will depend on the mobile menu design
      }
    },

    // Lazy loading images
    lazyLoad: {
      init: function() {
        if ('loading' in HTMLImageElement.prototype) {
          // Browser supports native lazy loading
          const lazyImages = utils.all('img[loading="lazy"]');
          lazyImages.forEach(img => {
            if (img.dataset.src) {
              img.src = img.dataset.src;
            }
            if (img.dataset.srcset) {
              img.srcset = img.dataset.srcset;
            }
          });
        } else {
          // Fallback for browsers that don't support native lazy loading
          this.lazyLoadWithIntersectionObserver();
        }
      },

      lazyLoadWithIntersectionObserver: function() {
        if ('IntersectionObserver' in window) {
          const lazyImageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const lazyImage = entry.target;
                if (lazyImage.dataset.src) {
                  lazyImage.src = lazyImage.dataset.src;
                }
                if (lazyImage.dataset.srcset) {
                  lazyImage.srcset = lazyImage.dataset.srcset;
                }
                lazyImage.removeAttribute('data-src');
                lazyImage.removeAttribute('data-srcset');
                lazyImageObserver.unobserve(lazyImage);
              }
            });
          });

          const lazyImages = utils.all('img[data-src], img[data-srcset]');
          lazyImages.forEach(lazyImage => {
            lazyImageObserver.observe(lazyImage);
          });
        }
      }
    }
  };

  // Initialize theme when DOM is ready
  utils.ready(() => {
    theme.init();
  });

  // Handle bfcache restoration
  window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
      // Page was restored from bfcache
      console.log('Theme: Page restored from bfcache');
      theme.init();
    }
  });
})();
