/* Collection Page Styles */

/* Breadcrumbs */
.breadcrumbs {
  padding: var(--spacing-small) 0;
  font-size: var(--font-size-smaller);
  color: var(--color-text-light);
}

.breadcrumbs__list {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumbs__item {
  display: flex;
  align-items: center;
}

.breadcrumbs__link {
  color: var(--color-text-light);
  text-decoration: none;
}

.breadcrumbs__link:hover {
  text-decoration: underline;
}

.breadcrumbs__separator {
  margin: 0 var(--spacing-extra-small);
}

.breadcrumbs__current {
  font-weight: var(--font-weight-medium);
}

/* Collection Banner */
.collection-banner {
  margin-bottom: var(--spacing-large);
  position: relative;
}

.collection-banner__wrapper {
  position: relative;
  overflow: hidden;
}

.collection-banner__image-container {
  position: relative;
  height: 300px;
  width: 100%;
  overflow: hidden;
}

.collection-banner__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.collection-banner__placeholder {
  height: 300px;
  width: 100%;
  background-color: var(--color-background-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.collection-banner__placeholder-svg {
  width: 100%;
  height: 100%;
  max-width: 500px;
  opacity: 0.2;
}

.collection-banner__content-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
}

.collection-banner__content {
  text-align: center;
  padding: var(--spacing-base);
}

/* Collection title is now in a separate section */

.collection-banner__tagline {
  font-size: var(--font-size-large);
  max-width: 600px;
  margin: 0 auto;
}

/* Collection Title */
.collection-title {
  margin-bottom: var(--spacing-large);
  margin-top: var(--spacing-large);
}

.collection-title__wrapper {
  text-align: left;
}

.collection-title__heading {
  font-size: var(--font-size-h3);
  margin: 0 0 var(--spacing-base);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-family: var(--font-heading);
  font-weight: var(--font-weight-bold);
}

.collection-title__description {
  margin-top: var(--spacing-small);
  font-size: var(--font-size-base);
  line-height: var(--line-height);
}

/* Collection Template */
.collection-template__inner {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: var(--grid-gap);
}

/* Filters */
.collection-filters {
  background-color: var(--color-background-light);
  padding: var(--spacing-base);
  border-radius: var(--border-radius-large);
}

.collection-filters__group {
  margin-bottom: var(--spacing-base);
  padding-bottom: var(--spacing-base);
  border-bottom: var(--border-width) solid var(--color-border);
}

.collection-filters__group:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.collection-filters__heading {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-base);
  text-transform: uppercase;
}

.collection-filters__checkbox-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.collection-filters__checkbox-item {
  margin-bottom: var(--spacing-small);
}

.collection-filters__checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: var(--font-size-small);
}

.collection-filters__checkbox-input {
  position: absolute;
  opacity: 0;
}

.collection-filters__checkbox-custom {
  width: 16px;
  height: 16px;
  border: var(--border-width) solid var(--color-border-dark);
  border-radius: var(--border-radius-small);
  margin-right: var(--spacing-small);
  position: relative;
}

.collection-filters__checkbox-input:checked + .collection-filters__checkbox-custom::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 8px;
  height: 8px;
  background-color: var(--color-text);
  border-radius: 1px;
}

.collection-filters__checkbox-count {
  margin-left: var(--spacing-extra-small);
  color: var(--color-text-lighter);
}

/* Mobile filters */
.collection-filters-mobile__toggle {
  display: none;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: var(--spacing-small) var(--spacing-base);
  background-color: var(--color-background-light);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  margin-bottom: var(--spacing-base);
}

.collection-filters-mobile__toggle-icon {
  transition: transform var(--transition-duration) var(--transition-timing);
}

.collection-filters-mobile__toggle[aria-expanded="true"] .collection-filters-mobile__toggle-icon {
  transform: rotate(180deg);
}

/* Product Grid */
.collection-template__product-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--grid-gap);
}

.collection-template__product-count {
  margin-bottom: var(--spacing-base);
  font-size: var(--font-size-small);
  color: var(--color-text-light);
}

.collection-template__sort {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-base);
}

.collection-template__sort-label {
  font-size: var(--font-size-small);
  margin-right: var(--spacing-small);
}

.collection-template__sort-select {
  padding: 8px 30px 8px 10px;
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: var(--color-background);
  font-size: var(--font-size-small);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
}

/* Responsive */
@media screen and (max-width: var(--breakpoint-large)) {
  .collection-template__product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: var(--breakpoint-medium)) {
  .collection-banner__image-container {
    height: 200px;
  }

  .collection-banner__tagline {
    font-size: var(--font-size-base);
  }

  .collection-title__heading {
    font-size: var(--font-size-h4);
  }

  .collection-title__description {
    font-size: var(--font-size-small);
  }

  .collection-template__inner {
    grid-template-columns: 1fr;
  }

  .collection-filters-mobile__toggle {
    display: flex;
  }

  .collection-filters {
    display: none;
  }

  .collection-filters.is-open {
    display: block;
  }

  .collection-template__product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: calc(var(--grid-gap) * 0.75);
  }
}

@media screen and (max-width: var(--breakpoint-small)) {
  .collection-template__product-grid {
    grid-template-columns: 1fr;
  }
}
