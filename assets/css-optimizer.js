// CSS Optimizer - A lightweight script to improve CSS loading performance
// This script helps with CSS loading without breaking the design

(function() {
  'use strict';
  
  // Function to check if a stylesheet is loaded
  function isStylesheetLoaded(href) {
    const links = document.getElementsByTagName('link');
    for (let i = 0; i < links.length; i++) {
      if (links[i].href.indexOf(href) !== -1) {
        return true;
      }
    }
    return false;
  }
  
  // Function to optimize CSS loading
  function optimizeCssLoading() {
    // Get all link elements
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    
    // Process each stylesheet
    links.forEach(function(link) {
      // Skip if already processed
      if (link.hasAttribute('data-optimized')) {
        return;
      }
      
      // Mark as processed
      link.setAttribute('data-optimized', 'true');
      
      // Check if this is a critical stylesheet (theme variables, base, theme)
      const href = link.getAttribute('href');
      const isCritical = href.includes('theme-variables') || 
                         href.includes('base.css') || 
                         href.includes('theme.css');
      
      if (!isCritical) {
        // For non-critical stylesheets, we can use media query switching
        // to make them load in a non-render-blocking way
        const originalMedia = link.getAttribute('media') || 'all';
        
        // Only apply this technique if it's not a print stylesheet already
        if (originalMedia !== 'print') {
          // Store the original media
          link.setAttribute('data-original-media', originalMedia);
          
          // Set to print to make it non-render-blocking
          link.setAttribute('media', 'print');
          
          // Once loaded, switch back to the original media
          link.onload = function() {
            this.setAttribute('media', this.getAttribute('data-original-media'));
          };
        }
      }
    });
  }
  
  // Run optimization when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', optimizeCssLoading);
  } else {
    optimizeCssLoading();
  }
  
  // Also run when the page is fully loaded to catch any dynamically added stylesheets
  window.addEventListener('load', optimizeCssLoading);
})();
