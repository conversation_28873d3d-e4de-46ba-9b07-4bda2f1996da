(()=>{let n={highPriority:["theme-variables.css","base.css"],lowPriority:["theme.css","keen-slider.css","collection-filters.css","section-headings.css","button-styles.css","collection-page.css"],conditional:{collection:["collection-page.css"],product:["product-page.css"]}};function loadCSS(t,e="low"){var o=document.createElement("link");o.rel="stylesheet",o.href=t,"high"===e?(o.setAttribute("fetchpriority","high"),o.rel="preload",o.as="style",o.onload=function(){this.onload=null,this.rel="stylesheet"}):(o.media="print",o.onload=function(){this.onload=null,this.media="all"}),document.head.appendChild(o)}function loadAllCSS(l){n.highPriority.forEach(t=>{loadCSS(l+t,"high")}),setTimeout(()=>{n.lowPriority.forEach(t=>{let e=!1;for(var o in n.conditional)if(n.conditional[o].includes(t)){e=!0;break}e||loadCSS(l+t,"low")})},100);var t,e=document.body.className;for(t in n.conditional)e.includes("template-"+t)&&n.conditional[t].forEach(t=>{loadCSS(l+t,"low")})}var t;"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){var t=document.getElementById("asset-url");t&&loadAllCSS(t.getAttribute("content"))}):(t=document.getElementById("asset-url"))&&loadAllCSS(t.getAttribute("content"))})();