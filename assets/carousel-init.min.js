document.addEventListener("DOMContentLoaded",function(){function updateSlideWidths(e,t){var i=e.querySelectorAll(".keen-slider__slide");let s=100/t;i.forEach(e=>{e.style.cssText=`min-width: ${s}% !important; max-width: ${s}% !important; width: ${s}% !important;`});t=e.querySelector(".keen-slider");t&&(t.style.cssText="display: flex; flex-wrap: nowrap; width: 100%;")}function setupArrows(e,t){var i=e.querySelector("[data-carousel-prev]"),e=e.querySelector("[data-carousel-next]");i&&i.addEventListener("click",()=>t.prev()),e&&e.addEventListener("click",()=>t.next())}function setupDots(e,s){var i=e.querySelector("[data-carousel-dots]");if(i){var d=s.details().size;let t=[];for(let e=0;e<d;e++){var r=document.createElement("button");r.classList.add("testimonial-carousel__dot"),r.setAttribute("aria-label","Go to slide "+(e+1)),r.addEventListener("click",()=>s.moveToIdx(e)),i.appendChild(r),t.push(r)}s.on("slideChanged",()=>{let i=s.details().relativeSlide;t.forEach((e,t)=>{t===i?e.classList.add("testimonial-carousel__dot--active"):e.classList.remove("testimonial-carousel__dot--active")})}),0<t.length&&t[0].classList.add("testimonial-carousel__dot--active")}}function setupAutoplay(e,t){let i,s=()=>{i=setInterval(()=>{e.next()},t)},d=()=>{i&&clearInterval(i)};s(),e.on("mouseenter",()=>d()),e.on("mouseleave",()=>s()),e.on("destroyed",()=>d())}initCarousels(),Shopify&&Shopify.designMode&&document.addEventListener("shopify:section:load",function(e){initCarousels(e.detail.sectionId)}),window.addEventListener("pageshow",function(e){e.persisted&&(console.log("Carousels: Page restored from bfcache"),document.querySelectorAll('[data-section-type="testimonial-carousel"], [data-section-type="collection-carousel"], [data-section-type="logo-carousel"], [data-section-type="hero-carousel"]').forEach(e=>{e.keenSlider&&(e.keenSlider.destroy(),e.keenSlider=null)}),initCarousels())}),document.addEventListener("bfcache:restored",function(){console.log("Carousels: Received bfcache:restored event"),initCarousels()}),window.initCarousels=function(e){(e?[document.querySelector(`[data-section-id="${e}"]`)]:document.querySelectorAll('[data-section-type="testimonial-carousel"], [data-section-type="collection-carousel"], [data-section-type="logo-carousel"], [data-section-type="hero-carousel"], [data-section-type="marketplace-logos"]')).forEach(e=>{if(e)if("testimonial-carousel"===e.getAttribute("data-section-type")){var o=e,n=o.querySelector(".keen-slider")?.id;if(n){let s=parseInt(o.getAttribute("data-slides-per-view")||3,10),d=parseInt(o.getAttribute("data-slides-per-view-tablet")||2,10),r=parseInt(o.getAttribute("data-slides-per-view-mobile")||1,10),t="true"===o.getAttribute("data-autoplay"),i=parseInt(o.getAttribute("data-autoplay-speed")||5e3,10),a=null!==o.querySelector("[data-carousel-dots]");var u={"(min-width: 990px)":{slides:{perView:s,spacing:20}},"(min-width: 750px) and (max-width: 989px)":{slides:{perView:d,spacing:15}},"(max-width: 749px)":{slides:{perView:r,spacing:10}}};let l=s;var p=window.innerWidth,p=(p<750?l=r:p<990&&(l=d),console.log("Initializing testimonial carousel with settings:",{slidesPerView:s,slidesPerViewTablet:d,slidesPerViewMobile:r,initialSlidesPerView:l,windowWidth:p,breakpoints:u}),updateSlideWidths(o,l),new KeenSlider("#"+n,{loop:!0,mode:"free-snap",rtl:"rtl"===document.dir,created:function(e){setupArrows(o,e),a&&setupDots(o,e),t&&setupAutoplay(e,i),setTimeout(()=>{updateSlideWidths(o,l)},0)}}));"undefined"!=typeof ResizeObserver&&new ResizeObserver(e=>{var t=window.innerWidth;let i=s;t<750?i=r:t<990&&(i=d),updateSlideWidths(o,i)}).observe(o),o.keenSlider=p,"undefined"==typeof ResizeObserver&&window.addEventListener("resize",function(){var e=window.innerWidth;let t=s;e<750?t=r:e<990&&(t=d),updateSlideWidths(o,t)}),setTimeout(()=>{updateSlideWidths(o,l)},100)}}else if("collection-carousel"===e.getAttribute("data-section-type")){var c=e,u=c.querySelector(".keen-slider")?.id;if(u){let s=parseInt(c.getAttribute("data-slides-per-view")||3,10),d=parseInt(c.getAttribute("data-slides-per-view-tablet")||2,10),r=parseInt(c.getAttribute("data-slides-per-view-mobile")||1,10),t="true"===c.getAttribute("data-autoplay"),i=parseInt(c.getAttribute("data-autoplay-speed")||5e3,10),a=null!==c.querySelector("[data-carousel-dots]");n={"(min-width: 990px)":{slides:{perView:s,spacing:20}},"(min-width: 750px) and (max-width: 989px)":{slides:{perView:d,spacing:15}},"(max-width: 749px)":{slides:{perView:r,spacing:10}}};let l=s;p=window.innerWidth,p=(p<750?l=r:p<990&&(l=d),console.log("Initializing collection carousel with settings:",{slidesPerView:s,slidesPerViewTablet:d,slidesPerViewMobile:r,initialSlidesPerView:l,windowWidth:p,breakpoints:n}),updateSlideWidths(c,l),new KeenSlider("#"+u,{loop:!0,mode:"free-snap",rtl:"rtl"===document.dir,created:function(e){setupArrows(c,e),a&&setupDots(c,e),t&&setupAutoplay(e,i),setTimeout(()=>{updateSlideWidths(c,l)},0)}}));c.keenSlider=p,"undefined"!=typeof ResizeObserver&&new ResizeObserver(e=>{var t=window.innerWidth;let i=s;t<750?i=r:t<990&&(i=d),updateSlideWidths(c,i)}).observe(c),"undefined"==typeof ResizeObserver&&window.addEventListener("resize",function(){var e=window.innerWidth;let t=s;e<750?t=r:e<990&&(t=d),updateSlideWidths(c,t)}),setTimeout(()=>{updateSlideWidths(c,l)},100)}}else if("logo-carousel"===e.getAttribute("data-section-type")){var w=e,v=w.querySelector(".keen-slider")?.id;if(v){let s=parseInt(w.getAttribute("data-slides-per-view")||5,10),d=parseInt(w.getAttribute("data-slides-per-view-tablet")||3,10),r=parseInt(w.getAttribute("data-slides-per-view-mobile")||2,10),t="false"!==w.getAttribute("data-autoplay"),i=parseInt(w.getAttribute("data-autoplay-speed")||3e3,10),a=null!==w.querySelector("[data-carousel-dots]"),l=s;var h=window.innerWidth,h=(h<750?l=r:h<990&&(l=d),console.log("Initializing logo carousel with settings:",{slidesPerView:s,slidesPerViewTablet:d,slidesPerViewMobile:r,initialSlidesPerView:l,windowWidth:h}),updateSlideWidths(w,l),new KeenSlider("#"+v,{loop:!0,mode:"free-snap",rtl:"rtl"===document.dir,created:function(e){setupArrows(w,e),a&&setupDots(w,e),t&&setupAutoplay(e,i),setTimeout(()=>{updateSlideWidths(w,l)},0)}}));w.keenSlider=h,"undefined"!=typeof ResizeObserver&&new ResizeObserver(e=>{var t=window.innerWidth;let i=s;t<750?i=r:t<990&&(i=d),updateSlideWidths(w,i)}).observe(w),"undefined"==typeof ResizeObserver&&window.addEventListener("resize",function(){var e=window.innerWidth;let t=s;e<750?t=r:e<990&&(t=d),updateSlideWidths(w,t)}),setTimeout(()=>{updateSlideWidths(w,l)},100)}}else if("hero-carousel"!==e.getAttribute("data-section-type")&&"marketplace-logos"===e.getAttribute("data-section-type")){var b=e,v=b.querySelector(".keen-slider")?.id;if(v){let s=parseInt(b.getAttribute("data-slides-per-view")||5,10),d=parseInt(b.getAttribute("data-slides-per-view-tablet")||3,10),r=parseInt(b.getAttribute("data-slides-per-view-mobile")||2,10),t="true"===b.getAttribute("data-autoplay"),i=parseInt(b.getAttribute("data-autoplay-speed")||3e3,10),a=null!==b.querySelector("[data-carousel-dots]"),l=s;h=window.innerWidth,h=(h<750?l=r:h<990&&(l=d),console.log("Initializing marketplace logos carousel with settings:",{slidesPerView:s,slidesPerViewTablet:d,slidesPerViewMobile:r,initialSlidesPerView:l,windowWidth:h}),updateSlideWidths(b,l),new KeenSlider("#"+v,{loop:!0,mode:"free-snap",rtl:"rtl"===document.dir,created:function(e){setupArrows(b,e),a&&setupDots(b,e),t&&setupAutoplay(e,i),setTimeout(()=>{updateSlideWidths(b,l)},0)}}));b.keenSlider=h,"undefined"!=typeof ResizeObserver&&new ResizeObserver(e=>{var t=window.innerWidth;let i=s;t<750?i=r:t<990&&(i=d),updateSlideWidths(b,i)}).observe(b),"undefined"==typeof ResizeObserver&&window.addEventListener("resize",function(){var e=window.innerWidth;let t=s;e<750?t=r:e<990&&(t=d),updateSlideWidths(b,t)}),setTimeout(()=>{updateSlideWidths(b,l)},100)}}})}});