(()=>{let i={ready:function(t){"loading"!==document.readyState?t():document.addEventListener("DOMContentLoaded",t)},on:function(t,e,a,i){t.addEventListener(e,function(t){var e=t.target.closest(a);e&&i(t,e)})},all:function(t,e=document){return Array.from(e.querySelectorAll(t))},get:function(t,e=document){return e.querySelector(t)}},n={init:function(){this.productGallery.init(),this.quantityAdjuster.init(),this.variantSelector.init(),this.mobileMenu.init(),this.lazyLoad.init()},productGallery:{init:function(){if(i.get(".product-gallery")){let a=i.all(".product-thumbnail");0!==a.length&&(a.forEach(e=>{e.addEventListener("click",()=>{var t=e.getAttribute("data-thumbnail-id"),t=i.get(`.product-gallery__item[data-media-id="${t}"]`);t&&(i.all(".product-gallery__item").forEach(t=>{t.style.display="none"}),t.style.display="block",a.forEach(t=>{t.classList.remove("active")}),e.classList.add("active"))})}),0<a.length)&&a[0].click()}}},quantityAdjuster:{init:function(){i.all(".quantity-input").forEach(e=>{e.addEventListener("change",()=>{var t=parseInt(e.getAttribute("min"),10)||1;e.value<t&&(e.value=t)})})}},variantSelector:{init:function(){var t=i.get('[data-section-type="product"]');if(t){var a=t.getAttribute("data-product-id");if(a){let e=i.all(".product-form__select",t);0!==e.length&&fetch(`/products/${a}.js`).then(t=>t.json()).then(t=>{this.setupVariantSelectors(t,e)}).catch(t=>console.error("Error loading product data:",t))}}},setupVariantSelectors:function(e,a){e.variants;a.forEach(t=>{t.addEventListener("change",()=>{this.updateVariantSelection(e,a)})}),this.updateVariantSelection(e,a)},updateVariantSelection:function(t,e){let a=e.map(t=>t.value);e=t.variants.find(t=>t.options.every((t,e)=>t===a[e]));e&&((t=i.get('input[name="id"]'))&&(t.value=e.id),(t=i.get("[data-regular-price]"))&&(t.textContent=this.formatMoney(e.price)),t=i.get(".product-form__submit"))&&(e.available?(t.disabled=!1,t.textContent=n.strings.addToCart||"Add to cart"):(t.disabled=!0,t.textContent=n.strings.soldOut||"Sold out"))},formatMoney:function(t){return"$"+(t/100).toFixed(2)}},mobileMenu:{init:function(){}},lazyLoad:{init:function(){"loading"in HTMLImageElement.prototype?i.all('img[loading="lazy"]').forEach(t=>{t.dataset.src&&(t.src=t.dataset.src),t.dataset.srcset&&(t.srcset=t.dataset.srcset)}):this.lazyLoadWithIntersectionObserver()},lazyLoadWithIntersectionObserver:function(){if("IntersectionObserver"in window){let a=new IntersectionObserver((t,e)=>{t.forEach(t=>{t.isIntersecting&&((t=t.target).dataset.src&&(t.src=t.dataset.src),t.dataset.srcset&&(t.srcset=t.dataset.srcset),t.removeAttribute("data-src"),t.removeAttribute("data-srcset"),a.unobserve(t))})});i.all("img[data-src], img[data-srcset]").forEach(t=>{a.observe(t)})}}}};i.ready(()=>{n.init()}),window.addEventListener("pageshow",function(t){t.persisted&&(console.log("Theme: Page restored from bfcache"),n.init())})})();