.product-card-styled{display:flex;flex-direction:column;border-radius:var(--border-radius-large);overflow:hidden;background-color:var(--color-background);box-shadow:var(--shadow-small);transition:box-shadow var(--transition-duration) var(--transition-timing),transform var(--transition-duration) var(--transition-timing);height:100%}
.product-card-styled:hover{box-shadow:var(--shadow-large);transform:translateY(-5px)}
.product-card-styled__image-container{position:relative;overflow:hidden;background-color:var(--color-background-light)}
.product-card-styled__image-wrapper{position:relative;padding-top:100%;overflow:hidden;background-color:#f8f8f8}
.product-card-styled__image{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;transition:transform .5s;display:block}
.product-card-styled__link:hover .product-card-styled__image{transform:scale(1.08)}
.product-card-styled__tag{position:absolute;top:var(--spacing-small);left:var(--spacing-small);padding:6px 12px;font-size:var(--font-size-smaller);font-weight:var(--font-weight-semibold);text-transform:uppercase;border-radius:var(--border-radius);z-index:2}
.product-card-styled__tag--new{background-color:var(--color-badge-new);color:#fff;font-size:var(--font-size-small);font-weight:var(--font-weight-bold);padding:4px 10px;border-radius:var(--border-radius)}
.product-card-styled__tag--sale{background-color:var(--color-sale);color:#fff}
.product-card-styled__rating{position:absolute;bottom:var(--spacing-small);left:var(--spacing-small);background-color:rgba(255,255,255,.9);padding:4px 8px;border-radius:var(--border-radius);display:flex;align-items:center;font-size:var(--font-size-small);font-weight:var(--font-weight-medium);z-index:2}
.product-card-styled__rating-stars{color:#ffb400;margin-right:5px;font-weight:var(--font-weight-bold)}
.product-card-styled__rating-count{color:var(--color-text-secondary)}
.product-card-styled__info{padding:var(--spacing-base);display:flex;flex-direction:column;flex-grow:1}
.product-card-styled__title{font-size:var(--font-size-large);font-weight:var(--font-weight-semibold);margin:0 0 var(--spacing-small);line-height:var(--line-height-tight);color:var(--color-text)}
.product-card-styled__title-link{text-decoration:none;color:inherit}
.product-card-styled__description{font-size:var(--font-size-small);color:var(--color-text-secondary);margin-bottom:var(--spacing-base);line-height:var(--line-height)}
.product-card-styled__price-container{margin-bottom:var(--spacing-base);margin-top:auto}
.product-card-styled__price{font-size:var(--font-size-larger);font-weight:var(--font-weight-bold);color:var(--color-text)}
.product-card-styled__price-from{font-size:var(--font-size-small);font-weight:var(--font-weight-regular);color:var(--color-text-secondary);margin-right:var(--spacing-extra-small)}
.product-card-styled__price-compare{font-size:var(--font-size-base);color:var(--color-text-lighter);text-decoration:line-through;margin-left:var(--spacing-small)}
.product-card-styled__actions{margin-top:var(--spacing-small)}
.product-card-styled__add-to-cart,.product-card-styled__sold-out{width:100%;padding:12px;font-size:var(--font-size-small);font-weight:var(--font-weight-semibold);text-transform:uppercase;border-radius:var(--border-radius-button);background-color:var(--color-button);color:var(--color-button-text);border:none;cursor:pointer;transition:background-color var(--transition-duration) var(--transition-timing),transform var(--transition-duration) var(--transition-timing),box-shadow var(--transition-duration) var(--transition-timing)}
.product-card-styled__add-to-cart:hover{background-color:var(--color-button-hover);transform:translateY(-2px);box-shadow:var(--shadow-medium)}
.product-card-styled__sold-out{background-color:var(--color-border-dark);cursor:not-allowed}
.product-card-styled__sold-out:hover{background-color:var(--color-border);transform:none;box-shadow:none}
.product-grid-styled{display:grid;grid-template-columns:repeat(4,1fr);gap:var(--grid-gap);margin:var(--spacing-large) 0}
@media screen and (max-width:var(--breakpoint-large)){
.product-grid-styled{grid-template-columns:repeat(3,1fr);gap:calc(var(--grid-gap) * .75)}
}
@media screen and (max-width:var(--breakpoint-medium)){
.product-grid-styled{grid-template-columns:repeat(2,1fr);gap:calc(var(--grid-gap) * .5)}
.product-card-styled__title{font-size:var(--font-size-base)}
.product-card-styled__price{font-size:var(--font-size-large)}
}
@media screen and (max-width:var(--breakpoint-small)){
.product-grid-styled{grid-template-columns:1fr}
}