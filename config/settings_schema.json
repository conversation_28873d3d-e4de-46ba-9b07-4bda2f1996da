[{"name": "theme_info", "theme_name": "Levo", "theme_version": "2.0.0", "theme_author": "Levo Theme", "theme_documentation_url": "https://example.com/docs", "theme_support_url": "https://example.com/support"}, {"name": "Colors - Brand & Text", "settings": [{"type": "header", "content": "Brand colors"}, {"type": "color", "id": "color_primary", "label": "Primary brand color", "default": "#000000", "info": "Used for buttons, accents, and highlights"}, {"type": "color", "id": "color_secondary", "label": "Secondary brand color", "default": "#4A4A4A", "info": "Used for secondary elements and accents"}, {"type": "header", "content": "Text colors"}, {"type": "color", "id": "color_text_primary", "label": "Primary text color", "default": "#333333", "info": "Used for body text and most content"}, {"type": "color", "id": "color_text_secondary", "label": "Secondary text color", "default": "#6A6A6A", "info": "Used for less important text and captions"}, {"type": "header", "content": "Background colors"}, {"type": "color", "id": "color_background", "label": "Main background color", "default": "#FFFFFF"}, {"type": "color", "id": "color_background_secondary", "label": "Secondary background color", "default": "#F5F5F5", "info": "Used for alternate sections and highlights"}, {"type": "header", "content": "Button colors"}, {"type": "color", "id": "color_button", "label": "Button background", "default": "#000000"}, {"type": "color", "id": "color_button_text", "label": "Button text", "default": "#FFFFFF"}, {"type": "header", "content": "Sale and badge colors"}, {"type": "color", "id": "color_sale", "label": "Sale badge", "default": "#e32c2b"}, {"type": "color", "id": "color_badge", "label": "Other badges", "default": "#000000"}]}, {"name": "Typography - Fonts & Sizes", "settings": [{"type": "header", "content": "Font families"}, {"type": "select", "id": "font_heading", "label": "Heading font", "options": [{"value": "'<PERSON><PERSON><PERSON>', sans-serif", "label": "<PERSON><PERSON><PERSON>"}, {"value": "'Poppins', sans-serif", "label": "<PERSON><PERSON><PERSON>"}, {"value": "'Roboto', sans-serif", "label": "Roboto"}, {"value": "'Montserrat', sans-serif", "label": "Montserrat"}, {"value": "'Open Sans', sans-serif", "label": "Open Sans"}, {"value": "'Lato', sans-serif", "label": "<PERSON><PERSON>"}, {"value": "'Playfair Display', serif", "label": "Playfair Display"}, {"value": "'Inter', sans-serif", "label": "Inter"}], "default": "'Inter', sans-serif"}, {"type": "select", "id": "font_body", "label": "Body font", "options": [{"value": "'<PERSON><PERSON><PERSON>', sans-serif", "label": "<PERSON><PERSON><PERSON>"}, {"value": "'Poppins', sans-serif", "label": "<PERSON><PERSON><PERSON>"}, {"value": "'Roboto', sans-serif", "label": "Roboto"}, {"value": "'Montserrat', sans-serif", "label": "Montserrat"}, {"value": "'Open Sans', sans-serif", "label": "Open Sans"}, {"value": "'Lato', sans-serif", "label": "<PERSON><PERSON>"}, {"value": "'Inter', sans-serif", "label": "Inter"}], "default": "'Inter', sans-serif"}, {"type": "header", "content": "Heading font sizes"}, {"type": "range", "id": "font_size_h1", "label": "H1 font size", "min": 24, "max": 60, "step": 2, "unit": "px", "default": 40}, {"type": "range", "id": "font_size_h2", "label": "H2 font size", "min": 20, "max": 50, "step": 2, "unit": "px", "default": 32}, {"type": "range", "id": "font_size_h3", "label": "H3 font size", "min": 18, "max": 40, "step": 2, "unit": "px", "default": 28}, {"type": "range", "id": "font_size_h4", "label": "H4 font size", "min": 16, "max": 30, "step": 2, "unit": "px", "default": 24}, {"type": "range", "id": "font_size_h5", "label": "H5 font size", "min": 14, "max": 24, "step": 1, "unit": "px", "default": 20}, {"type": "header", "content": "Body font sizes"}, {"type": "range", "id": "font_size_base", "label": "Base font size", "min": 14, "max": 18, "step": 1, "unit": "px", "default": 16}, {"type": "range", "id": "font_size_small", "label": "Small font size", "min": 12, "max": 16, "step": 1, "unit": "px", "default": 14}, {"type": "header", "content": "Text spacing"}, {"type": "range", "id": "line_height", "label": "Line height", "min": 1.2, "max": 2.0, "step": 0.1, "default": 1.5}, {"type": "range", "id": "letter_spacing", "label": "Letter spacing", "min": 0, "max": 2, "step": 0.1, "unit": "px", "default": 0.5}]}, {"name": "Layout", "settings": [{"type": "select", "id": "layout_max_width", "label": "Maximum page width", "options": [{"value": "1200px", "label": "1200px"}, {"value": "1400px", "label": "1400px"}, {"value": "1600px", "label": "1600px"}], "default": "1200px"}, {"type": "header", "content": "Spacing"}, {"type": "range", "id": "section_spacing", "label": "Section spacing", "min": 0, "max": 100, "step": 5, "unit": "px", "default": 50}, {"type": "range", "id": "grid_gap", "label": "Grid gap", "min": 10, "max": 40, "step": 2, "unit": "px", "default": 24}]}, {"name": "Social media", "settings": [{"type": "header", "content": "Social accounts"}, {"type": "text", "id": "social_facebook_link", "label": "Facebook", "info": "https://facebook.com/shopify"}, {"type": "text", "id": "social_instagram_link", "label": "Instagram", "info": "https://instagram.com/shopify"}, {"type": "text", "id": "social_twitter_link", "label": "Twitter", "info": "https://twitter.com/shopify"}, {"type": "text", "id": "social_linkedin_link", "label": "LinkedIn", "info": "https://linkedin.com/company/shopify"}, {"type": "text", "id": "social_youtube_link", "label": "YouTube", "info": "https://youtube.com/shopify"}, {"type": "text", "id": "social_snapchat_link", "label": "Snapchat", "info": "https://snapchat.com/add/shopify"}, {"type": "text", "id": "social_pinterest_link", "label": "Pinterest", "info": "https://pinterest.com/shopify"}, {"type": "text", "id": "social_tiktok_link", "label": "TikTok", "info": "https://tiktok.com/@shopify"}]}, {"name": "Favicon", "settings": [{"type": "image_picker", "id": "favicon", "label": "Favicon image", "info": "Will be scaled down to 32 x 32px"}]}, {"name": "Collection Pages", "settings": [{"type": "header", "content": "All Products Page"}, {"type": "image_picker", "id": "all_products_banner_image", "label": "All Products banner image", "info": "1800 x 400px recommended"}, {"type": "text", "id": "all_products_tagline", "label": "All Products tagline", "default": "Find uncommon ground."}, {"type": "textarea", "id": "all_products_description", "label": "All Products description", "default": "Browse our complete collection of products. Use the filters on the left to narrow down your search."}]}]